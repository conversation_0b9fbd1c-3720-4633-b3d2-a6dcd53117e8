import type { ListBaseResponse } from "../../types";

export type MedicalHistory = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  type: string;
  onsetDate: string;
  endDate: string;
  status: string;
  patientId: string;
};

export type CreateMedicalHistoryPayload = {
  name: string;
  type: string;
  onsetDate: string;
  endDate: string;
  status: string;
};

export type MedicalHistoryListResponse = ListBaseResponse<MedicalHistory>;
