import { z } from "zod";

import { addSponsorSchema } from "@/components/features/sponsors/default/modal-add-sponsor";

import type { Address } from "../addresses";
import type { Group } from "../groups/types";
import type { ListBaseResponse } from "../types";

export type Sponsor = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  displayName: string;
  phone: string;
  isActive: boolean;
  archived: boolean;
  logoUrl: string | null;
  group?: Group;
};

export type SponsorGetUploadLogoPayload = {
  fileType: string;
  extension: string;
};

export const addSponsorContactPayloadSchema = z.object({
  firstName: z
    .string({ required_error: "First Name is required" })
    .min(1, "First Name is required"),
  lastName: z
    .string({ required_error: "Last Name is required" })
    .min(1, "Last Name is required"),
  email: z
    .string({ required_error: "Email is required" })
    .email("Invalid email format")
    .min(1, "Email is required"),
  phone: z
    .string({ required_error: "Phone is required" })
    .min(1, "Phone is required"),
  address: z.object({
    addressLine: z
      .string({ required_error: "Address Line is required" })
      .min(1, "Address Line is required"),
    city: z
      .string({ required_error: "City is required" })
      .min(1, "City is required"),
    stateProvinceId: z
      .string({ required_error: "State is required" })
      .min(1, "State is required"),
    zipPostalCode: z
      .string({ required_error: "Zip/Postal Code is required" })
      .min(1, "Zip/Postal Code is required"),
    countryId: z
      .string({ required_error: "Country is required" })
      .min(1, "Country is required"),
  }),
  role: z.string().optional(),
});

export type AddSponsorContactPayload = z.infer<
  typeof addSponsorContactPayloadSchema
>;

export const editSponsorSchema = addSponsorSchema.partial();
export type EditSponsorPayload = z.infer<typeof editSponsorSchema>;

export type SponsorListResponse = ListBaseResponse<Sponsor>;

export type AddSponsorPayload = {
  name: string;
  address?: Omit<Address, "country" | "stateProvince">;
  sites?: string[];
};

export type UpdateSponsorPayload = Partial<AddSponsorPayload>;
