import { DocumentType } from "@/components/icons/doc-icons";

import { CsvViewer } from "./csv-viewer";
import { EmlViewer } from "./eml-viewer";
import { ImageTextViewer } from "./image-text-viewer";
import { LegacyOfficeViewer } from "./office-viewer";
import { PDFViewer } from "./pdf-viewer";

type Props = {
  type: DocumentType;
  url: string;
};

const VIEWER_TYPES = {
  pdf: "pdf",
  jpg: "image",
  jpeg: "image",
  png: "image",
  txt: "text",
  csv: "csv",
  docx: "office",
  xlsx: "office",
  pptx: "office",
  doc: "legacy-office",
  xls: "legacy-office",
  ppt: "legacy-office",
  eml: "eml",
} as const;

export const DocumentViewer = ({ type, url }: Props) => {
  const viewerType = VIEWER_TYPES[type];

  const viewers = {
    pdf: <PDFViewer url={url} />,
    image: <ImageTextViewer url={url} />,
    text: <ImageTextViewer url={url} />,
    csv: <CsvViewer url={url} />,
    office: <LegacyOfficeViewer url={url} />,
    "legacy-office": <LegacyOfficeViewer url={url} />,
    eml: <EmlViewer url={url} />,
  };

  return (
    viewers[viewerType] ?? (
      <div className="flex h-full flex-col items-center justify-center p-8 text-center">
        <p className="mb-4 text-gray-500 dark:text-gray-400">
          This file type cannot be previewed directly.
        </p>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
        >
          Open in new tab
        </a>
      </div>
    )
  );
};
