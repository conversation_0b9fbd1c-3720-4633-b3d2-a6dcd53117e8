import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import {
  Form,
  FormActions,
  InputField,
  Select,
  Textarea,
} from "@/components/ui/form";
import { FileDropzone } from "@/components/ui/form/file-drop-zone";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from "@/lib/constants";

import { getExtensionFromFileName } from "../folders/utils";
import { useFilterDocuments } from "../hooks/use-filter-documents";
import {
  useUpdateDocument,
  useUploadMultiple,
} from "../hooks/use-isf-mutations";
import {
  isfKeys,
  useIsfArtifacts,
  useIsfSections,
  useIsfZones,
} from "../hooks/use-isf-queries";

const schema = z.object({
  title: z
    .string({
      invalid_type_error: "File name is required",
      required_error: "File name is required",
    })
    .min(1, { message: "File name is required" }),
  categoryId: z
    .string({
      invalid_type_error: "Artifact is required",
      required_error: "Artifact is required",
    })
    .min(1, { message: "Artifact is required" })
    .optional(),
  description: z.string().optional(),
  zone: z
    .string({
      invalid_type_error: "Zone is required",
      required_error: "Zone is required",
    })
    .min(1, { message: "Zone is required" })
    .optional(),
  section: z
    .string({
      invalid_type_error: "Section is required",
      required_error: "Section is required",
    })
    .min(1, { message: "Section is required" })
    .optional(),
  file: z
    .instanceof(File)
    .refine(
      (file) => {
        const splittedName = file.name.split(".");
        const extension = splittedName[splittedName.length - 1];
        return ALLOWED_FILE_TYPES.includes(`.${extension}`);
      },
      { message: "Invalid document file type" },
    )
    .refine((file) => file.size <= MAX_FILE_SIZE, {
      message: "File size should not exceed 5MB",
    }),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onCreatePlaceHolder: () => void;
  path: string;
};

export const UploadDocumentModal = function ({
  isOpen,
  onClose,
  onCreatePlaceHolder,
  path,
}: Props) {
  const queryClient = useQueryClient();
  const { siteId, studyId } = useFilterDocuments();

  const [zone, setZone] = useState("");
  const [section, setSection] = useState("");
  const [artifact, setArtifact] = useState("");
  const [isManualUploading, setIsManualUploading] = useState(false);
  const formRef = useRef<FormActions<z.ZodType<z.infer<typeof schema>>>>(null);

  const { data: zones } = useIsfZones();
  const { data: sections } = useIsfSections(zone);
  const { data: artifacts } = useIsfArtifacts(zone, section);
  const { mutateAsync: uploadMultiple, isPending: isUploadingMultiple } =
    useUploadMultiple();
  const { mutateAsync: updateDocument, isPending: isUpdatingDocument } =
    useUpdateDocument();

  const handleZoneChange = (value: string) => {
    setZone(value);
  };

  const handleSectionChange = (value: string) => {
    setSection(value);
  };
  const handleArtifactChange = (value: string) => {
    setArtifact(value);
  };

  const onSubmit = async (data: z.infer<typeof schema>) => {
    const file = data.file;
    const extension = getExtensionFromFileName(data.file.name);
    try {
      setIsManualUploading(true);
      const fileName = path ? `${path.slice(1)}/${data.title}` : data.title;
      const uploadResult = await uploadMultiple({
        studyId,
        siteId,
        categoryId: data.categoryId,
        files: [
          {
            fileName: fileName,
            description: data.description,
            contentType: data.file.type,
            originationType: "web",
            extension: extension.toLowerCase(),
          },
        ],
      });

      if (uploadResult[fileName]) {
        const { url, fileId } = uploadResult[fileName];

        await fetch(url, {
          method: "PUT",
          body: file,
          headers: {
            "Content-Type": file.type,
          },
        });

        await updateDocument({
          id: fileId,
          processingStatus: "completed",
          hideSuccessNotification: true,
        });
        queryClient.invalidateQueries({
          queryKey: isfKeys.allDocumentLists(),
        });
        queryClient.invalidateQueries({
          queryKey: isfKeys.allFolderLists(),
        });
        toast.success("New document uploaded successfully");
        onClose();
      } else {
        toast.error("Failed to upload document");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Error uploading document");
      setIsManualUploading(false);
    } finally {
      setIsManualUploading(false);
    }
  };

  useEffect(() => {
    if (artifact && artifacts) {
      const selectedArtifact = artifacts.find((a) => a.id === artifact);
      if (selectedArtifact?.subartifact) {
        formRef.current?.formHandler.setValue(
          "title",
          selectedArtifact.subartifact,
          {
            shouldValidate: true,
          },
        );
      }
    }
  }, [artifact, artifacts]);

  return (
    <WrapperModal
      size="3xl"
      isOpen={isOpen}
      onClose={onClose}
      title={`Upload Document`}
    >
      <Form
        ref={formRef}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        className="space-y-4"
      >
        <div className="grid gap-4 sm:grid-cols-2 sm:gap-2">
          <div className="flex flex-col gap-2">
            <Label htmlFor="title">Zone</Label>
            <Select
              id="zone"
              name="zone"
              placeholder="Select zone"
              options={zones?.reduce(
                (acc: { label: string; value: string }[], zone) =>
                  zone?.name
                    ? [
                        ...acc,
                        {
                          label: zone?.name,
                          value: zone?.name,
                        },
                      ]
                    : acc,
                [],
              )}
              onChange={handleZoneChange}
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label htmlFor="title">Section</Label>
            <Select
              id="section"
              name="section"
              placeholder="Select section"
              options={sections?.map((section) => ({
                label: section.name,
                value: section.name,
              }))}
              onChange={handleSectionChange}
            />
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="title">Artifact Name</Label>
          <Select
            id="categoryId"
            name="categoryId"
            placeholder="Select artifact name"
            options={artifacts?.map((artifact) => ({
              label: artifact.name,
              value: artifact.id,
            }))}
            onChange={handleArtifactChange}
          />
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="title">File Name</Label>
          <InputField
            id="title"
            name="title"
            placeholder="Enter file name..."
          />
        </div>
        <div className="flex flex-col gap-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            placeholder="Enter description..."
          />
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="file">Document</Label>

            <div className="flex gap-1 text-xs">
              <span className="text-gray-400">No files?</span>
              <button
                type="button"
                onClick={onCreatePlaceHolder}
                className="font-medium text-blue-500 underline-offset-2 hover:underline"
              >
                Create a placeholder instead
              </button>
            </div>
          </div>
          <FileDropzone name="file" acceptTypes={ALLOWED_FILE_TYPES} />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            variant="primary"
            isLoading={
              isUploadingMultiple || isUpdatingDocument || isManualUploading
            }
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
