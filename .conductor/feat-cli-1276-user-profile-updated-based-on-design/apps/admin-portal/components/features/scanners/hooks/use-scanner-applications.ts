import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";

export const USE_SCANNER_APPLICATIONS_QUERY_KEY = "scannerApplications";

export const useScannerApplications = () => {
  const { page, take } = usePagination();

  return useQuery({
    queryKey: [USE_SCANNER_APPLICATIONS_QUERY_KEY, page, take],
    queryFn: () => api.scannerApplications.list({ page, take }),
    placeholderData: (prevData) => prevData,
  });
};

export const useInfiniteScannerApplications = (
  search: string,
  initialPageSize = 10,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-scanner-applications", search],
    queryFn: ({ pageParam = 1 }) =>
      api.scannerApplications.list({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
