import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { ContactModal } from "@/components/shared/contacts/contact-modal";
import type { CreateContactPayload } from "@/lib/apis/contacts";
import type { AddSponsorContactPayload } from "@/lib/apis/sponsors/types";

import { useAddSponsorContact } from "../../hooks/use-add-contact";

export const AddContactCard = () => {
  const { mutateAsync: addContact, isPending: isAddingContact } =
    useAddSponsorContact();
  const [showModalAddContact, setShowModalAddContact] = useState(false);
  const params = useParams();
  const sponsorId = params.id as string;

  const onSubmit = async (data: AddSponsorContactPayload) => {
    await addContact({ ...data, sponsorId });
    setShowModalAddContact(false);
  };

  return (
    <>
      <Card
        className="group min-h-48 cursor-pointer transition-all hover:bg-gray-200 dark:hover:bg-gray-700 [&>div]:p-4"
        onClick={() => setShowModalAddContact(true)}
      >
        <div className="flex items-center justify-center gap-2 text-xl text-gray-500">
          <IoMdAdd className="size-10" />
          <div>Add Contact</div>
        </div>
      </Card>
      <ContactModal
        show={showModalAddContact}
        onClose={() => setShowModalAddContact(false)}
        onSubmit={onSubmit as (data: CreateContactPayload) => void}
        isLoading={isAddingContact}
      />
    </>
  );
};
