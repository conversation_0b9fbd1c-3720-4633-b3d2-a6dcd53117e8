import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type {
  CreateStudyPayload,
  UpdateStudyPayload,
} from "@/lib/apis/studies";

import { studyKeys } from "./use-studies-queries";

export const useCreateStudy = () => {
  return useMutation({
    mutationFn: (data: CreateStudyPayload) => api.studies.create(data),
    onError: (err) => toast.error(err.message || "Failed to create study"),
    onSettled: (_, err) => !err && toast.success("Study created successfully"),
    meta: {
      awaits: studyKeys.allLists(),
    },
  });
};

export const useUpdateStudy = () => {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateStudyPayload }) =>
      api.studies.update(id, data),
    onMutate: () => {
      toast.dismiss();
    },
    onError: (error) => {
      toast.dismiss();
      toast.error(error.message || "Failed to update study");
    },
    onSettled: (_, err) => {
      if (!err) {
        toast.dismiss();
        toast.success("Study updated successfully");
      }
    },
    meta: {
      awaits: [studyKeys.allLists(), studyKeys.allDetails()],
    },
  });
};
