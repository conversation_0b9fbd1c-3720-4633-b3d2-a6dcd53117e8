import { DocumentStatus } from "@/components/ui/badges/ebinder-document-badge";

import { ListBaseResponse } from "../types";

export type DocumentAnalyticsFilters = {
  sponsorId?: string;
  studyId?: string;
  fromDate?: string;
  toDate?: string;
  days?: number;
};

export type TotalDocumentsResponse = {
  count: number;
  sourceCount: number;
  artifactCount: number;
  docExchangeCount: number;
};

export type DocumentsPendingReviewResponse = {
  count: number;
  sourceCount: number;
  artifactCount: number;
  docExchangeCount: number;
};

export type VolumeData = {
  date: string;
  count: number;
};

export type DocumentVolumeOverTimeResponse = {
  source: VolumeData[];
  artifact: VolumeData[];
  docExchange: VolumeData[];
};

export type OriginationTypeData = {
  type: string;
  count: number;
  details: {
    source: string;
    count: number;
  }[];
};

export type UploadsByOriginationTypeResponse = OriginationTypeData[];

// Document Workflow & Bottlenecks Types
export type SourceDocumentStatusData = {
  processingStatus: string;
  count: number;
};

export type SourceDocumentStatusFunnelResponse = SourceDocumentStatusData[];

export type ISFArtifactStatusData = {
  statusId: string;
  count: number;
  statusName: DocumentStatus;
};

export type ISFArtifactStatusBreakdownResponse = ISFArtifactStatusData[];

// Document Detail Types
export type DocumentDetailFilters = {
  sourceType?: string;
  sponsorId?: string;
  studyId?: string;
  fromDate?: string;
  toDate?: string;
  page?: number;
  limit?: number;
};

export type AgingDocumentsFilters = {
  sponsorId?: string;
  studyId?: string;
  fromDate?: string;
  toDate?: string;
  page?: number;
};

export type DocumentDetail =
  | {
      id: string;
      title: string;
      sourceType: "artifact_files" | "doc_exchange_files";
      createdDate: string;
      studyId: string;
      studyName: string;
      sponsorId: string;
      sponsorName: string;
      siteId: string;
      siteName: string;
    }
  | {
      sourceType: "source_documents";
      id: string;
      title: string;
      createdDate: string;
      studyId: string;
      studyName: string;
      sponsorId: string;
      sponsorName: string;
      siteId: string;
      siteName: string;
      patientId: string;
    };

export type DocumentDetailResponse = ListBaseResponse<DocumentDetail>;

export type AgingDocumentType =
  | "pendingReviewOver7Days"
  | "inReviewOver14Days"
  | "expiringSoon";

export type AgingDocumentItem = {
  id: string;
  createdDate: string; // ISO date string
  type: AgingDocumentType; // document aging type
  title: string;
  status: string;
  sourceType: string;
  siteId: string;
  studyId: string;
};
export type AgingDocumentsResponse = ListBaseResponse<AgingDocumentItem>;

export type SiteActivity = {
  siteId: string;
  siteName: string;
  studyId: string;
  studyName: string;
  totalDocs: number;
  docsLast30Days: number;
  usersUploading: number;
  lastUploadDate: string;
  avgTimeToProcess: number;
  isSiteActive: boolean;
};

export type SiteDocExchange = {
  siteId: string;
  siteName: string;
  numberDocExchange: number;
};

export type SitePerformanceFilters = {
  sponsorId?: string;
  studyId?: string;
  fromDate?: string;
  toDate?: string;
  page?: number;
};

export type SitePerformanceResponse = ListBaseResponse<SiteActivity>;
export type InactiveSitesResponse = ListBaseResponse<SiteActivity>;
export type SiteDocExchangeResponse = ListBaseResponse<SiteDocExchange>;
