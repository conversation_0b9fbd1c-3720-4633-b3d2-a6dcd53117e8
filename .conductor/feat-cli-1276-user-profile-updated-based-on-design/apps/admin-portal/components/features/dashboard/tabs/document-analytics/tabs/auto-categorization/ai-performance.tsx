"use client";

import { Card } from "flowbite-react";
import Link from "next/link";
import { MdRepeat } from "react-icons/md";
import { Tb<PERSON><PERSON>tTriangle, TbTargetArrow } from "react-icons/tb";

import { Skeleton } from "@/components/ui/skeleton";

import {
  useAiSuccessRate,
  useConversionRate,
  useFailureRate,
} from "./hooks/use-overview-categorization";

export const AiPerformanceOverview = () => {
  const { data: successData, isPending: isSuccessPending } = useAiSuccessRate();
  const { data: failureData, isPending: isFailurePending } = useFailureRate();
  const { data: conversionData, isPending: isConversionPending } =
    useConversionRate();

  const isPending = isSuccessPending || isFailurePending || isConversionPending;

  if (isPending) {
    return <AiPerformanceSkeleton />;
  }

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        AI Performance Overview
      </h3>
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 2xl:grid-cols-3">
        <Link href="/dashboards/auto-categorization">
          <Card className="h-full cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 [&>div]:p-4 sm:[&>div]:p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20">
                  <TbTargetArrow className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                  Success Rate
                </h4>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {successData?.aiSuccessRate
                    ? `${successData.aiSuccessRate.toFixed(1)}%`
                    : "0%"}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Accuracy:{" "}
                  {successData?.aiAccuracy
                    ? `${successData.aiAccuracy.toFixed(1)}%`
                    : "0%"}
                </div>
              </div>
            </div>
          </Card>
        </Link>

        <Link href="/dashboards/auto-categorization">
          <Card className="h-full cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 [&>div]:p-4 sm:[&>div]:p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/20">
                  <TbAlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                  Failure Rate
                </h4>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {failureData?.rate ? `${failureData.rate.toFixed(1)}%` : "0%"}
                </div>
              </div>
            </div>
          </Card>
        </Link>

        <Link href="/dashboards/auto-categorization">
          <Card className="h-full cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 [&>div]:p-4 sm:[&>div]:p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900/20">
                  <MdRepeat className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                  Conversion Rate
                </h4>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {conversionData?.rate
                    ? `${conversionData.rate.toFixed(1)}%`
                    : "0%"}
                </div>
              </div>
            </div>
          </Card>
        </Link>
      </div>
    </div>
  );
};

const AiPerformanceSkeleton = () => (
  <div>
    <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
      AI Performance Overview
    </h3>
    <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 2xl:grid-cols-3">
      <Card className="[&>div]:p-4 sm:[&>div]:p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Skeleton className="h-12 w-12 rounded-lg" />
            <Skeleton className="h-6 w-24" />
          </div>
          <div className="text-right">
            <Skeleton className="mb-1 h-8 w-16" />
            <Skeleton className="h-4 w-20" />
          </div>
        </div>
      </Card>

      <Card className="[&>div]:p-4 sm:[&>div]:p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Skeleton className="h-12 w-12 rounded-lg" />
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="text-right">
            <Skeleton className="h-8 w-16" />
          </div>
        </div>
      </Card>
      <Card className="[&>div]:p-4 sm:[&>div]:p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Skeleton className="h-12 w-12 rounded-lg" />
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="text-right">
            <Skeleton className="h-8 w-16" />
          </div>
        </div>
      </Card>
    </div>
  </div>
);
