import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import type { ButtonProps as FlowbiteButtonProps } from "flowbite-react";
import { Button as FlowbiteButton, Spinner } from "flowbite-react";
import type { PropsWithChildren } from "react";
import type { Merge } from "type-fest";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "px-2 sm:px-5 py-2 sm:py-2.5 rounded-lg flex items-center justify-center gap-2 [&>span]:p-0 [&>span]:flex [&>span]:items-center [&>span]:gap-2 [&:disabled]:pointer-events-none",
  {
    variants: {
      variant: {
        default:
          "bg-gray-200 text-gray-800 hover:!bg-gray-300 focus:!ring-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:!bg-gray-600 dark:focus:!ring-gray-500",
        green:
          "bg-green-500 text-white hover:!bg-green-600 focus:!ring-green-300 dark:bg-green-600 dark:hover:!bg-green-500 dark:focus:!ring-green-400",
        primary:
          "bg-blue-700 text-white hover:!bg-blue-600 focus:!ring-blue-300 dark:bg-blue-600 dark:hover:!bg-blue-500 dark:focus:!ring-blue-400",
        warning:
          "bg-yellow-400 text-white hover:!bg-yellow-600 focus:!ring-yellow-300 dark:bg-yellow-600 dark:hover:!bg-yellow-500 dark:focus:!ring-yellow-400",
        purple:
          "bg-purple-500 text-white hover:!bg-purple-600 focus:!ring-purple-300 dark:bg-purple-600 dark:hover:!bg-purple-500 dark:focus:!ring-purple-400",
        outline:
          "!bg-transparent border border-blue-700 hover:!bg-blue-50 text-blue-700 focus:!ring-blue-300 dark:!bg-transparent dark:border-blue-400 dark:text-blue-400 dark:hover:!bg-blue-950/20 dark:focus:!ring-blue-400",
        danger:
          "bg-red-600 text-white hover:!bg-red-700 focus:!ring-red-300 dark:bg-red-600 dark:hover:!bg-red-500 dark:focus:!ring-red-400",
        info: "bg-blue-600 text-white hover:!bg-blue-700 focus:!ring-blue-300 dark:bg-blue-600 dark:hover:!bg-blue-500 dark:focus:!ring-blue-400",
        outlineDanger:
          "!bg-transparent border border-red-500 hover:!bg-red-50 text-red-500 focus:!ring-red-300 dark:!bg-transparent dark:border-red-400 dark:text-red-400 dark:hover:!bg-red-950/20 dark:focus:!ring-red-400/50",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

type ButtonProps = Merge<
  VariantProps<typeof buttonVariants>,
  FlowbiteButtonProps & {
    isLoading?: boolean;
    disabledForInvalid?: boolean;
    enabledForDirty?: boolean;
  }
>;

export const Button = ({
  className,
  variant,
  children,
  onClick,
  isLoading,
  disabled,
  disabledForInvalid = true,
  enabledForDirty,
  ...props
}: PropsWithChildren<ButtonProps>) => {
  return (
    <FlowbiteButton
      className={cn(
        buttonVariants({ variant }),
        disabledForInvalid &&
          props.type === "submit" &&
          "group-[.is-invalid]:pointer-events-none group-[.is-invalid]:opacity-50",
        enabledForDirty &&
          props.type === "submit" &&
          "group-[.not-dirty]:pointer-events-none group-[.not-dirty]:opacity-50",
        className,
      )}
      onClick={onClick}
      disabled={isLoading || disabled}
      {...props}
    >
      <div className="flex items-center gap-2 [&>span]:!p-0">
        {isLoading && <Spinner size="sm" className="fill-white" />}
        {children}
      </div>
    </FlowbiteButton>
  );
};

type CloseButtonProps = ButtonProps & {
  onClose: () => void;
};

export const CloseButton = ({ onClose, ...props }: CloseButtonProps) => {
  return (
    <Button
      className="border border-gray-300 !bg-inherit text-black !ring-0 hover:!bg-inherit dark:border-gray-500 dark:!text-gray-100"
      onClick={onClose}
      disabledForInvalid={false}
      {...props}
    >
      Close
    </Button>
  );
};
