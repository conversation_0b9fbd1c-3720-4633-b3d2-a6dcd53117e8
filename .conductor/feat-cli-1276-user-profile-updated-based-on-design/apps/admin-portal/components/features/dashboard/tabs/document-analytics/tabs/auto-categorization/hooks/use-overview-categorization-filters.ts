import { endOfDay, startOfDay } from "date-fns";
import { parseAsString, useQueryState } from "nuqs";

export const useOverviewCategorizationFilters = () => {
  const [siteId, setSiteId] = useQueryState("siteId", parseAsString);
  const [studyId, setStudyId] = useQueryState("studyId", parseAsString);
  const [fromDate, setFromDate] = useQueryState("fromDate", parseAsString);
  const [toDate, setToDate] = useQueryState("toDate", parseAsString);

  const params = {
    siteId: siteId || undefined,
    studyId: studyId || undefined,
    fromDate: fromDate
      ? startOfDay(new Date(fromDate)).toISOString()
      : undefined,
    toDate: toDate ? endOfDay(new Date(toDate)).toISOString() : undefined,
  };

  return {
    siteId,
    studyId,
    fromDate,
    toDate,
    setSiteId,
    setStudyId,
    setFromDate,
    setToDate,
    params,
  };
};
