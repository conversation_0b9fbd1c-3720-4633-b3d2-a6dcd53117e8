import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { SiteNoAssignedUser } from "@/lib/apis/data-integrity";

export const sitesNoAssignedUserColumns: ColumnDef<SiteNoAssignedUser>[] = [
  {
    accessorKey: "id",
    header: "Site ID",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        href={`/sites/${row.original.id}`}
      >
        {row.original.id}
      </Link>
    ),
  },
  {
    accessorKey: "name",
    header: "Site Name",
  },
];
