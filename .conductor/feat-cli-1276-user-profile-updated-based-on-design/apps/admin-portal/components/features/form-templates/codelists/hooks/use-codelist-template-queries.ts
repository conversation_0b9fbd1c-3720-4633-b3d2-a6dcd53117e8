import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";
import { codelistTemplates } from "@/lib/apis/codelist-templates";
import { MetadataParams } from "@/lib/apis/types";

export const codelistTemplateKeys = {
  all: () => ["codelist-templates"] as const,
  allLists: () => [...codelistTemplateKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...codelistTemplateKeys.allLists(), params] as const,
  allDetails: () => [...codelistTemplateKeys.all(), "detail"] as const,
  detail: (id: string) => [...codelistTemplateKeys.allDetails(), id] as const,
};

export const useCodelistTemplates = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();

  const params = {
    page,
    take,
    filter: {
      search: search || undefined,
    },
  };

  return useQuery({
    queryKey: codelistTemplateKeys.list(params),
    queryFn: () => codelistTemplates.list(params),
    placeholderData: (prev) => prev,
  });
};

export const useCodelistTemplateItem = (id: string) => {
  return useQuery({
    queryKey: codelistTemplateKeys.detail(id),
    queryFn: () => codelistTemplates.get(id),
    enabled: !!id,
  });
};
