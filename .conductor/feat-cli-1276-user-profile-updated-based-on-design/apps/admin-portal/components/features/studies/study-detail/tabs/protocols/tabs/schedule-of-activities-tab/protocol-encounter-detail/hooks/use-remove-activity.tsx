import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { RemoveActivityFromEncounterPayload } from "@/lib/apis/encounters";

import { USE_PROTOCOL_ENCOUNTERS_QUERY_KEY } from "../../../../hooks/use-protocol-encounters";
import { USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY } from "./use-encounter-activities";

export function useRemoveActivityFromEncounter(protocolId: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payload: RemoveActivityFromEncounterPayload) =>
      api.encounters.removeActivityFromEncounter(payload),
    onSuccess: (_, { encounterId }) => {
      queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY, encounterId],
      });
      queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTERS_QUERY_KEY, protocolId],
      });
    },
    onSettled: (_, err) =>
      !err && toast.success("Remove activity successfully"),
    onError: (err) => toast.error(err?.message || "Failed to remove activity"),
  });
}
