import {
  MutationFunction,
  MutationKey,
  Query<PERSON>ey,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";

import { isArrayOfQueryKeys } from "@/utils/react-query";

type OptimisticMutationOptions<TData, TVariables, TSnapshot> = {
  mutationFn: MutationFunction<TData, TVariables>;
  queryKey: QueryKey;
  updater: (
    oldData: TSnapshot | undefined,
    variables: TVariables,
  ) => TSnapshot | undefined;
  invalidates?: QueryKey | QueryKey[];
  onMutate?: (payload: TVariables) => void;
  onError?: (error: any, payload: TVariables) => void;
  onSuccess?: (data: TData, variables: TVariables, context: () => void) => void;
  // in some *rare* cases, mutationKey is needed to make sure that only invalidating query<PERSON>ey if there is only 1 mutation is running
  mutationKey?: MutationKey;
};

export const useOptimisticMutation = <TData, TVariables, TSnapshot>({
  mutationFn,
  queryKey,
  updater,
  invalidates,
  onSuccess,
  onError,
  onMutate,
  mutationKey,
}: OptimisticMutationOptions<TData, TVariables, TSnapshot>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn,
    onMutate: async (variables) => {
      await queryClient.cancelQueries({ queryKey });
      const snapshot = queryClient.getQueryData<TSnapshot>(queryKey);

      queryClient.setQueryData(queryKey, (old: TSnapshot | undefined) =>
        updater(old, variables),
      );

      onMutate?.(variables);

      return () => {
        queryClient.setQueryData(queryKey, snapshot);
      };
    },
    onError: (err, variables, rollback) => {
      onError?.(err, variables);
      rollback?.();
    },
    onSuccess,
    onSettled: async () => {
      if (!invalidates) return;

      const invalidateKeys = isArrayOfQueryKeys(invalidates)
        ? invalidates
        : [invalidates];

      if (mutationKey) {
        if (
          queryClient.isMutating({
            mutationKey,
          }) === 1
        ) {
          return invalidateKeys.forEach((key) =>
            queryClient.invalidateQueries({ queryKey: key }),
          );
        }
        return;
      }

      return invalidateKeys.forEach((key) =>
        queryClient.invalidateQueries({ queryKey: key }),
      );
    },
    mutationKey,
  });
};
