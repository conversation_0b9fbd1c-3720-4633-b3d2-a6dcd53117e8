import { Tooltip } from "flowbite-react";
import { useState } from "react";

import { Tabs, TabsItem } from "@/components/ui/tabs";
import { Protocol } from "@/lib/apis/protocols";

import { EpochsTab } from "./tabs/epochs";
import { OverviewTab } from "./tabs/overview";
import { ProtocolContentTab } from "./tabs/protocol-content";
import { ProtocolsTab } from "./tabs/protocols";
import { ScheduleOfActivitiesTab } from "./tabs/schedule-of-activities-tab";
import { StudyArmsTab } from "./tabs/study-arms";

export const ProtocolContainerTab = () => {
  const [selectedProtocol, setSelectedProtocol] = useState<Protocol | null>(
    null,
  );
  const tabs = [
    {
      title: "Protocol Versions",
      content: (
        <ProtocolsTab
          setSelectedProtocol={setSelectedProtocol}
          selectedProtocol={selectedProtocol}
        />
      ),
    },
    {
      title: !selectedProtocol ? (
        <Tooltip content={"Select Protocol Version"}>Epochs</Tooltip>
      ) : (
        "Epochs"
      ),
      content: <EpochsTab selectedProtocol={selectedProtocol} />,
      disabled: !selectedProtocol,
    },
    {
      title: !selectedProtocol ? (
        <Tooltip content={"Select Protocol Version"}>Study Arm</Tooltip>
      ) : (
        "Study Arm"
      ),
      content: <StudyArmsTab selectedProtocol={selectedProtocol} />,
      disabled: !selectedProtocol,
    },
    {
      title: !selectedProtocol ? (
        <Tooltip content={"Select Protocol Version"}>
          Schedule of Activities
        </Tooltip>
      ) : (
        "Schedule of Activities"
      ),

      content: <ScheduleOfActivitiesTab selectedProtocol={selectedProtocol} />,
      disabled: !selectedProtocol,
    },
    {
      title: !selectedProtocol ? (
        <Tooltip content={"Select Protocol Version"}>Protocol Content</Tooltip>
      ) : (
        "Protocol Content"
      ),
      content: <ProtocolContentTab selectedProtocol={selectedProtocol} />,
      disabled: !selectedProtocol,
    },
    {
      title: !selectedProtocol ? (
        <Tooltip content={"Select Protocol Version"}>Overview</Tooltip>
      ) : (
        "Overview"
      ),

      content: (
        <OverviewTab
          selectedProtocol={selectedProtocol}
          setSelectedProtocol={setSelectedProtocol}
        />
      ),
      disabled: !selectedProtocol,
    },
  ];
  return (
    <Tabs>
      {tabs.map((tab, idx) => (
        <TabsItem key={idx} disabled={tab.disabled} title={tab.title}>
          {tab.content}
        </TabsItem>
      ))}
    </Tabs>
  );
};
