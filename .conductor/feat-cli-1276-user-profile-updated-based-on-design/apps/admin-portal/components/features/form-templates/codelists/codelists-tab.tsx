"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { ConfirmationModal } from "@/components/shared/confirmation-modal";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { SearchField } from "@/components/shared/search-field";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { CodelistTemplate } from "@/lib/apis/codelist-templates/types";

import { CodelistTemplateModal } from "./codelist-template-modal";
import { generateCodelistTemplateColumns } from "./columns";
import { useDeleteCodelistTemplate } from "./hooks/use-codelist-template-mutations";
import { useCodelistTemplates } from "./hooks/use-codelist-template-queries";

export const CodelistsTab = () => {
  const { data, isPending, isPlaceholderData } = useCodelistTemplates();
  const { mutateAsync: deleteCodelist, isPending: isDeleting } =
    useDeleteCodelistTemplate();
  const {
    isOpen: isModalOpen,
    open: openModal,
    close: closeModal,
  } = useDisclosure();
  const {
    isOpen: isDeleteModalOpen,
    open: openDeleteModal,
    close: closeDeleteModal,
  } = useDisclosure();
  const [selectedCodelist, setSelectedCodelist] =
    useState<CodelistTemplate | null>(null);

  const openAddModal = () => {
    setSelectedCodelist(null);
    openModal();
  };

  const openEditModal = (codelist: CodelistTemplate) => {
    setSelectedCodelist(codelist);
    openModal();
  };

  const handleCloseModal = () => {
    closeModal();
    setSelectedCodelist(null);
  };

  const handleShowDeleteModal = (codelist: CodelistTemplate) => {
    setSelectedCodelist(codelist);
    openDeleteModal();
  };

  const handleConfirmDelete = async () => {
    if (selectedCodelist) {
      await deleteCodelist({ id: selectedCodelist.id });
      closeDeleteModal();
      setSelectedCodelist(null);
    }
  };

  const columns = useMemo(
    () =>
      generateCodelistTemplateColumns({
        onEdit: openEditModal,
        onDelete: handleShowDeleteModal,
      }),
    [],
  );

  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Codelist Templates
          </h3>

          <div className="flex items-center gap-4">
            <SearchField placeholder="Search ..." />
            <Button variant="primary" onClick={openAddModal}>
              <IoMdAdd className="h-4 w-4" /> New Codelist
            </Button>
          </div>
        </div>
      </div>

      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData || isDeleting}>
          <Table columns={columns} data={data?.results ?? []} />
          {data?.metadata && <TableDataPagination metadata={data.metadata} />}
        </LoadingWrapper>
      )}

      {isModalOpen && (
        <CodelistTemplateModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          selectedCodelist={selectedCodelist}
        />
      )}

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleConfirmDelete}
        variant="danger"
        message="Are you sure you want to delete this codelist template?"
        itemName={selectedCodelist?.templateIdentifier}
        confirmLabel="Delete"
        isLoading={isDeleting}
      />
    </Card>
  );
};
