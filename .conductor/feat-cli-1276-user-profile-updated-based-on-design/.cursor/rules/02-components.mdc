---
description: Guidelines for React component development
alwaysApply: false
---

# React Component Guidelines

## Component Organization

- Organize components by features and functionality
- Group related components together
- Create appropriate subdirectories for larger features

## Component Structure

```tsx
// Imports in order: external, internal, hooks, types, etc.
import { useState, useEffect } from "react";
import { MySharedComponent } from "@/components/shared";
import { useMyFeature } from "@/hooks/useMyFeature";
import type { MyComponentProps } from "@/types";

export const MyComponent = ({ prop1, prop2, children }: MyComponentProps) => {
  // Hooks at the top
  const [state, setState] = useState(initialState);
  const { data, isLoading } = useMyFeature();

  // Side effects
  useEffect(() => {
    // Effect logic
  }, [dependencies]);

  // Event handlers
  const handleEvent = () => {
    // Handler logic
  };

  // Conditional renders
  if (isLoading) return <LoadingState />;

  // Main render
  return (
    <div>
      <h1>{prop1}</h1>
      <p>{prop2}</p>
      {children}
    </div>
  );
};
```

## Component Types

1. **Page Components**: App router pages, handle data fetching
2. **Feature Components**: Domain-specific components
3. **UI Components**: Reusable UI elements
4. **Layout Components**: Structure the page layout
5. **Provider Components**: Provide context/state

## Props Management

- Use TypeScript interfaces for props
- Destructure props in component parameters
- Provide default values for optional props
- Use proper prop naming (avoid abbreviations)

## State Management

- Keep state as local as possible
- Lift state up when needed
- Use appropriate hooks (useState, useReducer)
- Use context for component trees
- Use Zustand for global state

## Performance Optimization

- Use React.memo for expensive renders
- Use useCallback for functions passed as props
- Use useMemo for expensive calculations
- Avoid unnecessary re-renders
- Use proper keys for list items

## Composition vs. Props

- Prefer composition over props when appropriate
- Use render props or compound components for complex components
- Create reusable component patterns

## Error Handling

- Implement error boundaries for component trees
- Handle loading and error states
- Provide meaningful error messages
- Implement fallback UI for errors
