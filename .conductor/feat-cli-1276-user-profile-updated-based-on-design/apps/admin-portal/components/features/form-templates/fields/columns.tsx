import { ColumnDef } from "@tanstack/react-table";
import { MdVisibility } from "react-icons/md";

import {
  TableEditButton,
  TableGenericButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { FieldTemplateItem } from "@/lib/apis/field-templates/types";

export const FIELD_TYPES = [
  "Text",
  "TextArea",
  "Integer",
  "Decimal",
  "Date",
  "DateTime",
  "RadioButton",
  "Checkbox",
  "Dropdown",
  // "Autocomplete",
] as const;

type FieldTemplateActions = {
  onView: (field: FieldTemplateItem) => void;
  onEdit: (field: FieldTemplateItem) => void;
  onDelete: (field: FieldTemplateItem) => void;
};

export const generateFieldTemplateColumns = (
  actions: FieldTemplateActions,
): ColumnDef<FieldTemplateItem>[] => [
  {
    accessorKey: "friendlyName",
    header: "Field Label",
    cell: ({ row }) => (
      <button
        onClick={() => actions.onView(row.original)}
        className="text-primary-500 font-medium hover:underline"
      >
        {row.original.friendlyName}
      </button>
    ),
  },
  {
    accessorKey: "fieldName",
    header: "Field Name",
    cell: ({ row }) => (
      <span className="font-mono text-sm">{row.original.fieldName}</span>
    ),
  },
  {
    accessorKey: "fieldType",
    header: "Field Type",
    cell: ({ row }) => (
      <PillBadge className="text-sm" variant="default">
        {row.original.fieldType}
      </PillBadge>
    ),
  },
  {
    accessorKey: "templateIdentifier",
    header: "Identifier",
    cell: ({ row }) => (
      <span className="font-mono text-sm">
        {row.original.templateIdentifier}
      </span>
    ),
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const field = row.original;

      return (
        <div className="flex items-center gap-2">
          <TableGenericButton
            type="button"
            onClick={() => actions.onView(field)}
          >
            View
            <MdVisibility className="h-4 w-4" />
          </TableGenericButton>

          <TableEditButton
            type="button"
            onClick={() => actions.onEdit(field)}
          />

          <TableRemoveButton
            onClick={() => actions.onDelete(field)}
            label="Delete"
          />
        </div>
      );
    },
  },
];
