import { Triangle<PERSON>lert } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { Course } from "@/lib/apis/training-modules";

import { useDeleteCourse } from "../../hooks/use-training-module-mutations";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedCourse: Course;
};

export const ConfirmDeleteCourseModal = ({
  isOpen,
  onClose,
  selectedCourse,
}: Props) => {
  const { mutateAsync, isPending } = useDeleteCourse();

  const handleConfirmDelete = async () => {
    await mutateAsync(selectedCourse.id);
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Delete Course</Modal.Header>
      <Modal.Body>
        <div className="flex flex-col items-center justify-center gap-2.5">
          <TriangleAlert className="text-red-500" size={34} />
          <span className="text-xl font-medium leading-[150%] dark:text-white">
            Are you sure you want to delete this course?
          </span>
          <p className="text-center text-xl text-gray-900 dark:text-gray-400">
            <strong>{selectedCourse.name}</strong>
          </p>
          <p className="mt-4 text-center text-sm italic text-gray-500 dark:text-gray-500">
            This action cannot be undone.
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer className="flex justify-end gap-2.5">
        <Button variant="outline" onClick={onClose} className="border-none">
          Cancel
        </Button>
        <Button
          variant="outlineDanger"
          onClick={handleConfirmDelete}
          disabled={isPending}
          isLoading={isPending}
        >
          Delete
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
