"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { HiPlus } from "react-icons/hi";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { VisitType } from "@/lib/apis/visit-types";

import { getColumns } from "./columns";
import { useVisitTypes } from "./hooks/use-visit-types";
import { ModalVisitType } from "./modal-visit-type";
import { VisitTypeFilters } from "./visit-type-filters";

const BREADCRUMB_ITEMS = [{ label: "Visit Types" }];

export const VisitTypesContent = () => {
  const { data, isPending, isPlaceholderData } = useVisitTypes();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedVisitType, setSelectedVisitType] = useState<VisitType | null>(
    null,
  );
  const handleViewType = (visitType: VisitType) => {
    setSelectedVisitType(visitType);
    setIsModalOpen(true);
  };

  const columns = useMemo(() => getColumns(handleViewType), []);

  const handleAddType = () => {
    setSelectedVisitType(null);
    setIsModalOpen(true);
  };

  return (
    <>
      <div className="space-y-4">
        <Breadcrumb items={BREADCRUMB_ITEMS} />
        <PageHeader>Visit Types</PageHeader>
        <Card className="[&>div]:p-0">
          <div className="flex items-center justify-end gap-2 p-4">
            <VisitTypeFilters />
            <Button
              variant="primary"
              className="w-fit px-0 py-1.5"
              onClick={handleAddType}
            >
              <HiPlus />
              Add Visit Type
            </Button>
          </div>
          {isPending ? (
            <TableLoading columns={columns} />
          ) : (
            <LoadingWrapper isLoading={isPlaceholderData}>
              {data && <Table data={data.results} columns={columns} />}
              {data?.metadata && (
                <TableDataPagination metadata={data.metadata} />
              )}
            </LoadingWrapper>
          )}
        </Card>
      </div>
      {isModalOpen && (
        <ModalVisitType
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          visitType={selectedVisitType}
        />
      )}
    </>
  );
};
