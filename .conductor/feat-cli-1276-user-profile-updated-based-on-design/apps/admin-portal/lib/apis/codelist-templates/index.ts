import Base<PERSON>pi from "../base";
import type { MetadataParams } from "../types";
import type {
  CodelistTemplate,
  CodelistTemplateListResponse,
  CreateCodelistTemplatePayload,
  UpdateCodelistTemplatePayload,
} from "./types";

export class CodelistTemplates<PERSON>pi extends BaseApi {
  constructor() {
    super("/codelist-templates", true);
  }

  public async list(
    params?: MetadataParams,
  ): Promise<CodelistTemplateListResponse> {
    return this.http.get<CodelistTemplateListResponse>("", params);
  }

  public async get(id: string): Promise<CodelistTemplate> {
    return this.http.get<CodelistTemplate>(`/${id}`);
  }

  public async create(
    payload: CreateCodelistTemplatePayload,
  ): Promise<CodelistTemplate> {
    return this.http.post<CodelistTemplate>("", payload);
  }

  public async update(
    payload: UpdateCodelistTemplatePayload,
  ): Promise<CodelistTemplate> {
    return this.http.put<CodelistTemplate>(`/${payload.id}`, payload);
  }

  public async delete(id: string): Promise<void> {
    return this.http.delete<void>(`/${id}`);
  }
}

export const codelistTemplates = new CodelistTemplatesApi();
