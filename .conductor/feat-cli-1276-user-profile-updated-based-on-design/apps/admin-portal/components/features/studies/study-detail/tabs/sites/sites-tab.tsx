import { Card } from "flowbite-react";
import { useParams } from "next/navigation";

import { Table, TableLoading } from "@/components/ui/table";

import { columns } from "./columns";
import { useStudySites } from "./hooks/use-sites-queries";

export const SitesTab = () => {
  const { id } = useParams();
  const { data, isPending } = useStudySites(id as string);

  if (isPending) {
    return (
      <>
        <Card className="[&>div]:p-0">
          <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold dark:text-gray-400">
            Sites
          </div>
          <TableLoading columns={columns} />
        </Card>
      </>
    );
  }

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold dark:text-gray-400">
          Sites
        </div>
        <Table columns={columns} data={data?.results ?? []} />
      </Card>
    </>
  );
};
