import { useState } from "react";

import { DocumentError } from "../error-document";
import { LoadingDocument } from "../loading-document";
export const LegacyOfficeViewer = ({ url }: { url: string }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Microsoft Office Online Viewer URL
  const viewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`;

  const handleIframeLoad = () => {
    setLoading(false);
  };

  const handleIframeError = () => {
    setLoading(false);
    setError("Failed to load the document. Please try downloading it instead.");
  };

  const handleRender = () => {
    if (loading) return <LoadingDocument />;
    if (error) return <DocumentError message={error} />;
  };

  return (
    <div className="legacy-office-viewer h-full w-full">
      {handleRender()}

      <iframe
        src={viewerUrl}
        title={"Office Document"}
        width="100%"
        height="100%"
        frameBorder="0"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        style={{ display: loading || error ? "none" : "block" }}
      />
    </div>
  );
};
