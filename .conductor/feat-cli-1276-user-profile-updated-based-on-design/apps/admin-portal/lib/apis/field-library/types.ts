import {
  FIELD_TYPES,
  FieldType,
} from "@/components/features/studies/study-detail/tabs/form-studio/fields/columns";
import type { ListBaseResponse } from "@/lib/apis/types";

import { Codelist } from "../codelist-definitions/types";

export type FieldStatus = "DRAFT" | "PUBLISHED" | "ARCHIVED";

export type FieldConfig = {
  allowPartial?: string;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
  maxLength?: number;
  min?: number;
  max?: number;
  unitOfMeasure?: string;
  layoutDirection?: "horizontal" | "vertical";
  decimalPlaces?: number;
  codelistId?: string;
  isDisplayOnForm?: boolean;
};

export type FieldLibraryItem = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  fieldName: string;
  fieldType: FieldType;
  displayLabel: string;
  shortCode: string;
  description?: string;
  status: FieldStatus;
  version: number;
  config?: FieldConfig;
  metadata?: any;
  codelist?: Codelist;
};

export type FieldLibraryListResponse = ListBaseResponse<FieldLibraryItem>;

export type CreateFieldPayload = {
  fieldName: string;
  studyId: string;
  fieldType: (typeof FIELD_TYPES)[number];
  displayLabel: string;
  description?: string;
  config?: FieldConfig;
  metadata?: any;
};

export type UpdateFieldPayload = CreateFieldPayload & {
  id: string;
};
