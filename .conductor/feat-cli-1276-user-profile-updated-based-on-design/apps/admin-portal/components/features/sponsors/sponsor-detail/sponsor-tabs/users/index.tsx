import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import React, { useMemo, useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";

import { useSponsor } from "../../../hooks/use-sponsor";
import { generateUserColumns } from "./columns";
import { useUpdateProfileStatus } from "./hooks/use-users-mutations";
import { useUsersByGroupId } from "./hooks/use-users-queries";
import { ModalEditProfile } from "./modal-edit-profile";
import StudiesModal from "./studies-modal/studies-modal";

export const UsersTab = () => {
  const params = useParams();
  const id = params.id as string;
  const { data: sponsor } = useSponsor(id);
  const { data, isPending, isPlaceholderData } = useUsersByGroupId(
    sponsor?.group?.id || undefined,
  );
  const { mutateAsync: updateStatus, isPending: isUpdateStatus } =
    useUpdateProfileStatus();
  const [selectedUserId, setSelectedUserId] = useState("");
  const {
    close: onCloseEditModal,
    isOpen: isOpenEditModal,
    open: onOpenEditModal,
  } = useDisclosure();

  const {
    close: onCloseStudiesModal,
    isOpen: isOpenStudiesModal,
    open: onOpenStudiesModal,
  } = useDisclosure();

  const columns = useMemo(
    () =>
      generateUserColumns({
        onEdit: (user) => {
          onOpenEditModal();
          setSelectedUserId(user.id);
        },
        onToggleStatus: (user) => {
          updateStatus({
            userId: user.id,
            profileId: user.currentProfile.id,
            isActive: !user.currentProfile.isActive,
          });
        },
        onViewStudies: (user) => {
          onOpenStudiesModal();
          setSelectedUserId(user.id);
        },
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [updateStatus],
  );

  const handleCloseEditModal = () => {
    setSelectedUserId("");
    onCloseEditModal();
  };

  const handleCloseStudiesModal = () => {
    setSelectedUserId("");
    onCloseStudiesModal();
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <span className="p-4 text-lg font-semibold dark:text-gray-400">
          Profiles
        </span>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData || isUpdateStatus}>
            <Table columns={columns} data={data?.results ?? []} />
          </LoadingWrapper>
        )}
      </Card>
      {isOpenEditModal && (
        <ModalEditProfile
          isOpen={isOpenEditModal}
          onClose={handleCloseEditModal}
          selectedUserId={selectedUserId}
        />
      )}
      {isOpenStudiesModal && (
        <StudiesModal
          isOpen={isOpenStudiesModal}
          onClose={handleCloseStudiesModal}
          selectedUserId={selectedUserId}
        />
      )}
    </>
  );
};
