import { Select } from "flowbite-react";
import type { ReactNode } from "react";
import { CSVLink } from "react-csv";
import { HiDownload, HiPlus } from "react-icons/hi";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { cn } from "@/lib/utils";

import { Button } from "../ui/button";

type HeaderActionsProps = {
  data: any[][];
  buttonText?: string;
  filename: string;
  onButtonClick?: () => void;
  rightContent?: ReactNode;
  isShowedSearchInput?: boolean;
  className?: string;
};

export const HeaderActions = ({
  data,
  buttonText,
  filename,
  onButtonClick,
  rightContent,
  isShowedSearchInput = true,
  className,
}: HeaderActionsProps) => {
  const { take, changePageSize } = usePagination();
  const { search, changeSearch } = useSearch();

  return (
    <div
      className={cn(
        "flex flex-col gap-y-2 p-3 sm:p-4 md:flex-row md:items-center md:justify-between",
        className,
      )}
    >
      <div className="flex items-center">
        <span className="mr-2 dark:text-white">Show</span>
        <Select
          id="show"
          className="mr-2 [&_select]:py-1.5"
          defaultValue={take}
          onChange={(e) => changePageSize(Number(e.target.value))}
        >
          <option value={5}>5 entries</option>
          <option value={10}>10 entries</option>
          <option value={25}>25 entries</option>
          <option value={50}>50 entries</option>
          <option value={100}>100 entries</option>
        </Select>
        <CSVLink
          data={data}
          filename={filename}
          className="inline-flex items-center"
        >
          <Button variant="primary" className="px-0 py-1.5">
            <HiDownload />
            <div className="text-white">Export CSV</div>
          </Button>
        </CSVLink>
      </div>
      <div className="ml-auto flex items-center space-x-2 md:ml-0">
        <div className="flex flex-1 flex-shrink items-center">
          {isShowedSearchInput && (
            <input
              name="search"
              type="text"
              placeholder="Search..."
              className="w-full rounded-md border-gray-300 px-2.5 py-1.5 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              defaultValue={search}
              onChange={(e) => changeSearch(e.target.value)}
            />
          )}
        </div>

        {rightContent}

        {buttonText && onButtonClick && (
          <Button
            variant="primary"
            className="flex-shrink-0 px-0 py-1.5"
            onClick={onButtonClick}
          >
            <HiPlus />
            {buttonText}
          </Button>
        )}
      </div>
    </div>
  );
};
