import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateActivityPayload } from "@/lib/apis/activities";

import { USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY } from "./use-encounter-activities";

export const useUpdateEncounterActivity = (activityId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateActivityPayload) =>
      api.activities.update(activityId, data),
    onSuccess: () => {
      toast.success("Activity update successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY],
      });
    },
    onError: (err) => {
      toast.error(err.message);
    },
  });
};
