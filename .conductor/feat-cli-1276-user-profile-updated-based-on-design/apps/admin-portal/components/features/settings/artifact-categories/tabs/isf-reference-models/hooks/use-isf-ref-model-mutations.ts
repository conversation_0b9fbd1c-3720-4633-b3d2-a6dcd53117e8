import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  AddISFRefModelPayload,
  UpdateISFRefModelPayload,
} from "@/lib/apis/isf-ref-models";

import { isfRefModelKeys } from "./use-isf-ref-model-queries";

export const useAddISFRefModel = () =>
  useMutation({
    mutationFn: (payload: AddISFRefModelPayload) =>
      api.isfRefModelApi.create(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to add ISF Reference Model"),
    onSettled: (_, err) =>
      !err && toast.success("Add ISF Reference Model successfully"),
    meta: {
      awaits: isfRefModelKeys.allLists(),
    },
  });

export const useUpdateISFRefModel = () =>
  useMutation({
    mutationFn: (payload: UpdateISFRefModelPayload) =>
      api.isfRefModelApi.update(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to update ISF Reference Model"),
    onSettled: (_, err) =>
      !err && toast.success("Update ISF Reference Model successfully"),
    meta: {
      awaits: isfRefModelKeys.allLists(),
    },
  });

export const useDeleteISFRefModel = () =>
  useMutation({
    mutationFn: (id: string) => api.isfRefModelApi.delete(id),
    onError: (err) =>
      toast.error(err?.message || "Fail to delete ISF Reference Model"),
    onSettled: (_, err) =>
      !err && toast.success("Delete ISF Reference Model successfully"),
    meta: {
      awaits: isfRefModelKeys.allLists(),
    },
  });
