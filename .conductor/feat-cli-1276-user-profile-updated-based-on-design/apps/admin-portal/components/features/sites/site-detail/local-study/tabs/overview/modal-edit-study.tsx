import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { z } from "zod";

import { useStudyProtocols } from "@/components/features/studies/study-detail/tabs/protocols/hooks/use-protocols-queries";
import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField, Select } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { STUDY_STATUSES } from "@/lib/apis/studies";

import { useUpdateLocalStudy } from "../../hooks/use-local-study-mutations";
import { useLocalStudy } from "../../hooks/use-local-study-queries";

const editStudySchema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  nct: z.string().optional(),
  studyCode: z.string().optional(),
  status: z
    .string({ required_error: "Status is required" })
    .min(1, "Status is required"),
  patientTarget: z.coerce
    .number({ required_error: "Patient target is required" })
    .min(1, "Patient target must be greater than 0"),
  principalInvestigator: z
    .string({ required_error: "Principal investigator is required" })
    .min(1, "Principal investigator is required"),
  subInvestigator: z.string().optional(),
  siteNumber: z
    .string()
    .max(30, "Maximum allowed length is 30 characters")
    .optional(),
  protocolId: z.string({ required_error: "Protocol is required" }),
});

type ModalEditStudyProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalEditStudy = ({ isOpen, onClose }: ModalEditStudyProps) => {
  const params = useParams();
  const siteId = params.id as string;
  const studyId = params.studyId as string;

  const { data } = useLocalStudy({
    siteId,
    studyId,
  });
  const { mutateAsync: updateStudy, isPending: isUpdating } =
    useUpdateLocalStudy({
      siteId,
      studyId,
    });

  async function onSubmit(data: z.infer<typeof editStudySchema>) {
    await updateStudy({
      ...data,
      id: siteId,
      studyId: studyId,
    });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Edit Study</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={editStudySchema}
          onSubmit={onSubmit}
          defaultValues={{
            name: data?.study.name || "",
            nct: data?.study.nct || "",
            studyCode: data?.study.studyCode || "",
            status: data?.status || "",
            patientTarget: data?.patientTarget || 0,
            principalInvestigator: data?.principalInvestigator || "",
            subInvestigator: data?.subInvestigator || "",
            protocolId: data?.protocolId || "",
            siteNumber: data?.siteNumber || "",
          }}
          formProps={{ shouldFocusError: false }}
        >
          <StudyForm onClose={onClose} isSubmitting={isUpdating} />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type StudyFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
};

const StudyForm = ({ onClose, isSubmitting }: StudyFormProps) => {
  const params = useParams();
  const siteId = params.id as string;
  const studyId = params.studyId as string;

  const { data } = useLocalStudy({
    siteId,
    studyId,
  });
  const { data: protocols } = useStudyProtocols(studyId);

  const statusIndex = STUDY_STATUSES.findIndex(
    (status) => status.value === data?.status,
  );

  const allowedStatusIndex = [statusIndex, statusIndex + 1];

  const validStatuses = STUDY_STATUSES.filter((_, idx) =>
    allowedStatusIndex.includes(idx),
  );

  return (
    <>
      <div className="grid gap-3 sm:grid-cols-2 sm:gap-6">
        <div className="space-y-1">
          <Label htmlFor="name">Name</Label>
          <InputField
            readOnly
            id="name"
            name="name"
            placeholder="Enter name..."
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="nct">NCT</Label>
          <InputField readOnly id="nct" name="nct" placeholder="Enter NCT..." />
        </div>

        <div className="space-y-1">
          <Label htmlFor="studyCode">Study Code</Label>
          <InputField
            readOnly
            id="studyCode"
            name="studyCode"
            placeholder="Enter study code..."
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="protocolId">Protocol</Label>
          <Select
            id="protocolId"
            name="protocolId"
            placeholder="Select a protocol"
            options={protocols?.results.map((protocol) => ({
              label: protocol.name,
              value: protocol.id,
            }))}
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="status">Status</Label>
          <Select
            id="status"
            name="status"
            placeholder="Select status"
            options={validStatuses.map((status) => ({
              label: status.label,
              value: status.value,
            }))}
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="patientTarget">Patient Target</Label>
          <InputNumber
            min={1}
            id="patientTarget"
            name="patientTarget"
            placeholder="Enter patient target..."
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="principalInvestigator">Principal Investigator</Label>
          <InputField
            id="principalInvestigator"
            name="principalInvestigator"
            placeholder="Enter principal investigator..."
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="subInvestigator">Sub Investigator</Label>
          <InputField
            id="subInvestigator"
            name="subInvestigator"
            placeholder="Enter sub investigator..."
          />
        </div>
        <div className="space-y-1 sm:col-span-2">
          <Label htmlFor="siteNumber">Site Number</Label>
          <InputField
            id="siteNumber"
            name="siteNumber"
            placeholder="Enter site number..."
          />
        </div>
      </div>
      <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </>
  );
};
