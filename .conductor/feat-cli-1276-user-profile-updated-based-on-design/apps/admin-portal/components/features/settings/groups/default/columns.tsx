"use client";
import type { ColumnDef, Row } from "@tanstack/react-table";
import Link from "next/link";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import type { Group } from "@/lib/apis/groups/types";

export const columns: ColumnDef<Group>[] = [
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <Link
        href={`/settings/groups/${row.original.id}`}
        className="text-primary-500 font-medium hover:underline"
      >
        {row.getValue("name")}
      </Link>
    ),
  },
  {
    header: "Type",
    accessorKey: "type",
    cell: ({ row }) => (
      <span className="uppercase">{row.original.type || "-"}</span>
    ),
  },
  {
    header: "City",
    accessorKey: "address.city",
    cell: ({ row }) => row.original.address?.city || "-",
  },
  {
    header: "State/Province",
    accessorKey: "address.stateProvince",
    cell: ({ row }) => row.original.address?.stateProvince?.name || "-",
  },
  {
    header: "Action",
    accessorKey: "id",
    cell: function Cell({ row }: { row: Row<Group> }) {
      return (
        <TableViewButton
          type="link"
          href={`/settings/groups/${row.original.id}`}
        />
      );
    },
    enableSorting: false,
  },
];
