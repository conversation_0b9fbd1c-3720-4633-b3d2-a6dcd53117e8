import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteLocalStudies } from "@/hooks/queries/use-infinite-local-studies";
import { useInfiniteStudies } from "@/hooks/queries/use-infinite-studies";
import { LocalStudyDetail } from "@/lib/apis/studies";

import { useAddStudyToGroup } from "../hooks/use-add-study-to-group";

const addStudySchema = z.object({
  studyId: z
    .string({ required_error: "Please select a study" })
    .min(1, "Please select a study"),
});

type ModalAddStudyProps = {
  groupId: string;
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddStudy = ({
  groupId,
  isOpen,
  onClose,
}: ModalAddStudyProps) => {
  const { mutateAsync: addStudy, isPending } = useAddStudyToGroup();

  const handleSubmit = async (values: z.infer<typeof addStudySchema>) => {
    const selectedOption: LocalStudyDetail = JSON.parse(values.studyId);

    if (!selectedOption) return;

    await addStudy({
      groupId,
      studyId: selectedOption.studyId,
      siteId: selectedOption.siteId,
    });
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Study</Modal.Header>
      <Modal.Body>
        <Form
          schema={addStudySchema}
          onSubmit={handleSubmit}
          className="space-y-4"
        >
          {/* Optimized LazySelect */}
          <LazySelect
            name="studyId"
            id="studyId"
            placeholder="Select Study"
            searchPlaceholder="Search studies..."
            useInfiniteQuery={useInfiniteLocalStudies}
            getOptionLabel={(option) => `${option.name} (${option.site.name})`}
            getOptionValue={(option) => JSON.stringify(option)}
          />

          {/* Modal Footer */}
          <div className="flex flex-col justify-end gap-4 sm:flex-row">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              disabled={isPending}
              variant="primary"
              isLoading={isPending}
              enabledForDirty
              disabledForInvalid
            >
              Add Study
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
