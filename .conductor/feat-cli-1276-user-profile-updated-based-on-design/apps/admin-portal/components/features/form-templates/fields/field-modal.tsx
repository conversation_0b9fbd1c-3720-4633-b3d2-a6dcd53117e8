import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod";
import { Tooltip, useThemeMode } from "flowbite-react";
import { Edit, Eye } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import ReactJsonView from "react-json-view";
import { z, ZodIssueCode } from "zod";

import { EditorModal } from "@/components/shared/editor-modal";
import {
  getConfigByFieldType,
  NormalizedCodelist,
  renderFieldValidation,
} from "@/components/shared/field-builder";
import { Button } from "@/components/ui/button";
import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Switch } from "@/components/ui/form/toggle-switch";
import { CodelistTemplate } from "@/lib/apis/codelist-templates/types";
import { FieldTemplateItem } from "@/lib/apis/field-templates/types";
import { cn } from "@/lib/utils";

import { FIELD_TYPES } from "./columns";
import FieldPreview from "./field-preview";
import {
  useCreateFieldTemplate,
  useUpdateFieldTemplate,
} from "./hooks/use-field-template-mutations";

export const CODE_LIST_FIELDS = ["RadioButton", "Checkbox", "Dropdown"];

const metadataSchema = z.record(z.any()).nullable();

const getSafeMetadata = (metadata: any): Record<string, any> | null => {
  const result = metadataSchema.safeParse(metadata);
  return result.success && result.data ? result.data : null;
};

const baseSchema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required")
    .regex(
      /^[a-zA-Z0-9_]+$/,
      "Name can only contain letters, numbers, and underscores",
    ),
  label: z
    .string({
      required_error: "Label is required",
      invalid_type_error: "Label is required",
    })
    .min(1, "Label is required"),
  templateIdentifier: z
    .string({
      required_error: "Identifier is required",
      invalid_type_error: "Identifier is required",
    })
    .min(1, "Identifier is required")
    .regex(
      /^[a-zA-Z0-9_]+$/,
      "Identifier can only contain letters, numbers, and underscores",
    ),
  description: z.string().optional(),
  type: z.enum(FIELD_TYPES, {
    errorMap: () => ({ message: "Type is required" }),
  }),
  config: z
    .object({
      maxLength: z.coerce
        .number({ invalid_type_error: "Max length must be a number" })
        .optional(),
      min: z.coerce
        .number({ invalid_type_error: "Min value must be a number" })
        .optional(),
      max: z.coerce
        .number({ invalid_type_error: "Max value must be a number" })
        .optional(),
      unitOfMeasure: z.string().optional(),
      dateFormat: z.string().optional(),
      disableFutureDates: z.boolean().optional(),
      disablePastDates: z.boolean().optional(),
      layoutDirection: z
        .enum(["horizontal", "vertical"])
        .default("horizontal")
        .optional(),
      decimalPlaces: z.coerce
        .number({ invalid_type_error: "Decimal places must be a number" })
        .min(0, "Decimal places must be at least 0")
        .max(10, "Decimal places must be at most 10")
        .optional(),
      codelistId: z.string().optional(),
      codelist: z.custom<NormalizedCodelist>().optional(),
      isDisplayOnForm: z.boolean().optional(),
    })
    .optional(),
  shortCode: z.string().optional(),
  isEditing: z.boolean().optional(),
});

const schema = z.preprocess((input, ctx) => {
  const configFields = baseSchema
    .pick({
      config: true,
      type: true,
    })
    .safeParse(input);

  if (configFields.success) {
    const configData = configFields.data;
    if (
      configData.type &&
      CODE_LIST_FIELDS.includes(configData.type) &&
      !configData.config?.codelistId
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "CodeList is required",
        path: ["config.codelistId"],
      });
    }

    if (
      (configData.type === "Date" || configData.type === "DateTime") &&
      !configData.config?.dateFormat
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Date format is required",
        path: ["config.dateFormat"],
      });
    }

    if (configData.type === "Integer" || configData.type === "Decimal") {
      const minValue = configData.config?.min;
      const maxValue = configData.config?.max;
      if (typeof minValue === "number" && typeof maxValue === "number") {
        if (minValue > maxValue) {
          ctx.addIssue({
            code: ZodIssueCode.custom,
            message: "Min value cannot be greater than max value",
            path: ["config.min"],
          });
        }
        if (maxValue < minValue) {
          ctx.addIssue({
            code: ZodIssueCode.custom,
            message: "Max value cannot be less than min value",
            path: ["config.max"],
          });
        }
      }
    }
  }

  const shortCodeFields = baseSchema
    .pick({
      shortCode: true,
      isEditing: true,
    })
    .safeParse(input);

  if (shortCodeFields.success) {
    const shortCodeFieldsData = shortCodeFields.data;
    if (shortCodeFieldsData.isEditing && !shortCodeFieldsData.shortCode) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Short code is required",
        path: ["shortCode"],
      });
    }
  }

  return input;
}, baseSchema);

export type FieldTemplateValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedField: FieldTemplateItem | null;
  isViewMode?: boolean;
};

export const FieldModal = function ({
  isOpen,
  onClose,
  selectedField,
  isViewMode = false,
}: Props) {
  const { mode } = useThemeMode();
  const [isPreview, setIsPreview] = useState(false);

  const { mutateAsync: createFieldTemplate, isPending: isCreatingField } =
    useCreateFieldTemplate();
  const { mutateAsync: updateFieldTemplate, isPending: isUpdatingField } =
    useUpdateFieldTemplate();

  const isEditing = !!selectedField;

  const formMethods = useForm<FieldTemplateValues>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      name: selectedField?.fieldName || "",
      label: selectedField?.friendlyName || "",
      templateIdentifier: selectedField?.templateIdentifier || "",
      description: selectedField?.description || "",
      type: selectedField?.fieldType,
      shortCode: selectedField?.shortCode || "",
      config: {
        ...selectedField?.config,
      },
      isEditing: isEditing,
    },
    disabled: isViewMode || undefined,
  });
  const values = formMethods.getValues();
  const isSubmitting = isCreatingField || isUpdatingField;
  const currentType = formMethods.watch("type");
  const isValid = formMethods.formState.isValid;
  const safeMetadata = selectedField
    ? getSafeMetadata(selectedField.metadata)
    : null;
  const isShowShortCode = isEditing || isViewMode;
  const onSubmit = async (data: FieldTemplateValues) => {
    if (isViewMode) return;

    const config = getConfigByFieldType(data.type, data.config);

    const payload = {
      fieldName: data.name,
      fieldType: data.type,
      friendlyName: data.label,
      templateIdentifier: data.templateIdentifier,
      description: data.description || undefined,
      config,
    };

    if (isEditing && selectedField) {
      await updateFieldTemplate({ id: selectedField.id, ...payload });
    } else {
      await createFieldTemplate(payload);
    }

    onClose();
  };

  const handlePreview = () => {
    setIsPreview(!isPreview);
  };

  const handleClosePreview = () => {
    setIsPreview(false);
  };

  useEffect(() => {
    const { unsubscribe } = formMethods.watch((values, { name }) => {
      if (name !== "config.max" && name !== "config.min") return;
      formMethods.trigger(["config.min", "config.max"]);
    });
    return () => unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <EditorModal
        isOpen={isOpen}
        onClose={onClose}
        title="Field Template Editor"
        formId="field-template-form"
        isValid={isValid && !isViewMode}
        isSubmitting={isSubmitting}
        leftFooterAction={
          isValid || isViewMode ? (
            <Tooltip content={isPreview ? "Edit" : "Preview"}>
              <Button
                onClick={handlePreview}
                className="!p-2.5 [&_span]:!p-0"
                variant="outline"
              >
                {isPreview ? <Edit size={24} /> : <Eye size={24} />}
              </Button>
            </Tooltip>
          ) : (
            <Tooltip content="Complete required fields to preview">
              <Button
                onClick={handlePreview}
                className="!p-2.5 [&_span]:!p-0"
                variant="outline"
                disabled
              >
                <Eye size={24} />
              </Button>
            </Tooltip>
          )
        }
      >
        {isPreview && (
          <FieldPreview onClose={handleClosePreview} values={values} />
        )}
        <Form
          schema={schema}
          formMethods={formMethods}
          onSubmit={onSubmit}
          id="field-template-form"
          className={cn("space-y-5", isPreview && "hidden")}
        >
          <div className="space-y-4 rounded-xl border border-gray-200 p-5 shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="type">Type</Label>
                <Select
                  name="type"
                  options={FIELD_TYPES.map((type) => ({
                    label: type,
                    value: type,
                  }))}
                  placeholder="Select field type"
                  onChange={async (value) => {
                    if (value === currentType) return;
                    formMethods.setValue(
                      "config",
                      {
                        layoutDirection: "horizontal",
                        isDisplayOnForm: values.config?.isDisplayOnForm,
                      },
                      {
                        shouldValidate: true,
                      },
                    );
                  }}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="templateIdentifier">Identifier</Label>
                <InputField
                  id="templateIdentifier"
                  name="templateIdentifier"
                  placeholder="Enter identifier..."
                />
              </div>
            </div>

            <div
              className={cn(
                "grid gap-4",
                isShowShortCode ? "grid-cols-3" : "grid-cols-2",
              )}
            >
              <div className="space-y-1">
                <Label htmlFor="name">Name</Label>
                <InputField
                  id="name"
                  name="name"
                  placeholder="Enter field name..."
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="label">Label</Label>
                <InputField
                  id="label"
                  name="label"
                  placeholder="Enter field label..."
                />
              </div>
              {isShowShortCode && (
                <div className="space-y-1">
                  <Label required htmlFor="shortCode">
                    Short Code
                  </Label>
                  <InputField
                    id="shortCode"
                    name="shortCode"
                    placeholder="Enter short code..."
                  />
                </div>
              )}
            </div>

            <div className="space-y-1">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter description..."
              />
              <div key={currentType} className="flex justify-end gap-2 py-2.5">
                <Label htmlFor="config.isDisplayOnForm">Display on Form</Label>
                <Switch sizing="sm" name="config.isDisplayOnForm" />
              </div>
            </div>
          </div>

          {safeMetadata && (
            <div className="space-y-2.5 rounded-xl border border-gray-200 p-5 shadow-sm dark:border-gray-700 dark:bg-gray-800">
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                Metadata
              </span>

              <div className="rounded-lg border border-gray-200 dark:border-gray-600">
                <ReactJsonView
                  src={safeMetadata}
                  theme={mode === "dark" ? "monokai" : "rjv-default"}
                  displayObjectSize={false}
                  displayDataTypes={false}
                  enableClipboard={false}
                  name={false}
                  collapsed={false}
                  style={{
                    backgroundColor: "transparent",
                    fontSize: "14px",
                    fontFamily:
                      'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                  }}
                />
              </div>
            </div>
          )}

          {!!currentType && (
            <div className="space-y-2.5 rounded-xl border border-gray-200 p-5 shadow-sm dark:border-gray-700 dark:bg-gray-800">
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                Format
              </span>

              {renderFieldValidation(
                currentType,
                `validation-${currentType}`,
                "global",
              )}
            </div>
          )}
        </Form>
      </EditorModal>
    </>
  );
};
