import { Card } from "flowbite-react";
import Image from "next/image";

import { cn } from "@/lib/utils";

type OverviewCardProps = {
  children: React.ReactNode;
  title: string;
  className?: string;
  rightContent?: React.ReactNode;
};

export const OverviewCard = ({
  children,
  title,
  className,
  rightContent,
}: OverviewCardProps) => {
  return (
    <Card className={cn("[&>div]:p-3 sm:[&>div]:p-4", className)}>
      <div className="mb-3 flex items-center justify-between">
        <span className=" text-lg font-semibold dark:text-gray-400">
          {title}
        </span>
        {rightContent}
      </div>
      {children}
    </Card>
  );
};

type OverviewItemProps = {
  label: string;
  value?: string;
  imgUrl?: string;
  children?: React.ReactNode;
  className?: string;
};

const OverviewItem = ({
  label,
  value,
  imgUrl,
  children,
  className,
}: OverviewItemProps) => {
  return (
    <div className={cn("flex items-start gap-4", className)}>
      <div className="flex min-w-0 flex-col gap-1">
        <span className="text-md font-medium dark:text-gray-400">{label}</span>
        {imgUrl ? (
          <Image
            src={imgUrl}
            alt={label}
            width={150}
            height={150}
            className="max-h-[150px] max-w-[150px]"
          />
        ) : (
          !!value && <span className="text-sm text-gray-500">{value}</span>
        )}
        {children}
      </div>
    </div>
  );
};

export { OverviewItem };
