"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { ConfirmationModal } from "@/components/shared/confirmation-modal";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { SearchField } from "@/components/shared/search-field";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { FieldLibraryItem } from "@/lib/apis/field-library/types";

import { generateFieldLibraryColumns } from "./columns";
import { FieldModal } from "./field-modal";
import { FilterFields } from "./filter-fields";
import {
  useArchiveField,
  useCreateFieldVersion,
  useDeleteField,
  usePublishField,
} from "./hooks/use-field-library-mutations";
import { useFieldLibrary } from "./hooks/use-field-library-queries";

export const FieldsTab = () => {
  const { data, isPending, isPlaceholderData } = useFieldLibrary();
  const { mutateAsync: archiveField, isPending: isArchiving } =
    useArchiveField();
  const { mutateAsync: publishField, isPending: isPublishing } =
    usePublishField();
  const { mutateAsync: deleteField, isPending: isDeleting } = useDeleteField();
  const { mutateAsync: createFieldVersion, isPending: isCopyingField } =
    useCreateFieldVersion();

  const {
    isOpen: isModalOpen,
    open: openModal,
    close: closeModal,
  } = useDisclosure();
  const {
    isOpen: isPublishModalOpen,
    open: openPublishModal,
    close: closePublishModal,
  } = useDisclosure();
  const {
    isOpen: isDeleteModalOpen,
    open: openDeleteModal,
    close: closeDeleteModal,
  } = useDisclosure();
  const {
    isOpen: isArchiveModalOpen,
    open: openArchiveModal,
    close: closeArchiveModal,
  } = useDisclosure();
  const [selectedField, setSelectedField] = useState<FieldLibraryItem | null>(
    null,
  );

  const openAddModal = () => {
    setSelectedField(null);
    openModal();
  };

  const openEditModal = (field: FieldLibraryItem) => {
    setSelectedField(field);
    openModal();
  };

  const handleCopyField = async (field: FieldLibraryItem) => {
    await createFieldVersion(field.id);
  };

  const handleCloseModal = () => {
    closeModal();
    setSelectedField(null);
  };

  const handleShowPublishModal = (field: FieldLibraryItem) => {
    setSelectedField(field);
    openPublishModal();
  };

  const handleShowDeleteModal = (field: FieldLibraryItem) => {
    setSelectedField(field);
    openDeleteModal();
  };

  const handleShowArchiveModal = (field: FieldLibraryItem) => {
    setSelectedField(field);
    openArchiveModal();
  };

  const handleConfirmPublish = async () => {
    if (selectedField) {
      await publishField(selectedField.id);
      closePublishModal();
      setSelectedField(null);
    }
  };

  const handleConfirmDelete = async () => {
    if (selectedField) {
      await deleteField(selectedField.id);
      closeDeleteModal();
      setSelectedField(null);
    }
  };

  const handleConfirmArchive = async () => {
    if (selectedField) {
      await archiveField(selectedField.id);
      closeArchiveModal();
      setSelectedField(null);
    }
  };

  const columns = useMemo(
    () =>
      generateFieldLibraryColumns({
        onEdit: openEditModal,
        onCopy: handleCopyField,
        onPublish: handleShowPublishModal,
        onArchive: handleShowArchiveModal,
        onDelete: handleShowDeleteModal,
      }),
    [],
  );

  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Fields
          </h3>

          <div className="flex items-center gap-4">
            <SearchField placeholder="Search fields..." />
            <FilterFields />
            <Button variant="primary" onClick={openAddModal}>
              <IoMdAdd className="h-4 w-4" /> New Field
            </Button>
          </div>
        </div>
      </div>

      <div className="p-0">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper
            isLoading={
              isPlaceholderData ||
              isPublishing ||
              isArchiving ||
              isDeleting ||
              isCopyingField
            }
          >
            <Table columns={columns} data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </div>

      {isModalOpen && (
        <FieldModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          selectedField={selectedField}
        />
      )}

      <ConfirmationModal
        isOpen={isPublishModalOpen}
        onClose={closePublishModal}
        onConfirm={handleConfirmPublish}
        variant="warning"
        message="Are you sure you want to publish this field?"
        itemName={selectedField?.displayLabel}
        confirmLabel="Publish"
        warningMessage="Once published, this field cannot be edited anymore."
        isLoading={isPublishing}
      />

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleConfirmDelete}
        variant="danger"
        message="Are you sure you want to delete this field?"
        itemName={selectedField?.displayLabel}
        confirmLabel="Delete"
        isLoading={isDeleting}
      />

      <ConfirmationModal
        isOpen={isArchiveModalOpen}
        onClose={closeArchiveModal}
        onConfirm={handleConfirmArchive}
        variant="warning"
        message="Are you sure you want to archive this field?"
        itemName={selectedField?.displayLabel}
        confirmLabel="Archive"
        warningMessage="Once archived, this field cannot be edited anymore."
        isLoading={isArchiving}
      />
    </Card>
  );
};
