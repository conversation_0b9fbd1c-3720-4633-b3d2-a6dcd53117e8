import { TabsRef } from "flowbite-react";
import { usePathname, useRouter } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";

import { capitalize } from "@/utils/string";

import { Tabs, TabsItem } from "./tabs";

type Tab = {
  key: string;
  title?: React.ReactNode;
  content: React.ReactNode;
  query?: string;
  disabled?: boolean;
};

type Props = {
  tabs: Tab[] | Readonly<Tab[]>;
  className?: string;
  mountOnActive?: boolean;
  ref?: React.MutableRefObject<TabsRef | null>;
  queryKey?: string;
};

export const TabsWrapper = ({
  tabs,
  className,
  mountOnActive = false,
  ref,
  queryKey = "tab",
}: Props) => {
  const router = useRouter();
  const pathname = usePathname();
  const [currentTab] = useQueryState(queryKey, parseAsString);

  const tabIndex = tabs.findIndex((tab) => tab.key === currentTab);

  const activeIndex = tabIndex !== -1 ? tabIndex : 0;

  const handleChangeTab = (nextTabIdx: number) => {
    const nextTab = tabs[nextTabIdx];
    const newParams = new URLSearchParams();
    newParams.set(queryKey, tabs[nextTabIdx].key);
    const query = newParams
      ? `?${newParams.toString()}${nextTab.query ? `&${nextTab.query}` : ""}`
      : "";
    router.push(`${pathname}${query}`);
  };

  return (
    <Tabs ref={ref} className={className} onActiveTabChange={handleChangeTab}>
      {tabs.map((tab, idx) => (
        <TabsItem
          active={idx === activeIndex}
          key={tab.key}
          disabled={tab.disabled}
          title={tab.title || capitalize(tab.key)}
        >
          {mountOnActive
            ? idx === activeIndex
              ? tab.content
              : null
            : tab.content}
        </TabsItem>
      ))}
    </Tabs>
  );
};
