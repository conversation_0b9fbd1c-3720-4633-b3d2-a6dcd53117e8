import { ListFilter } from "lucide-react";
import { parseAsString } from "nuqs";
import { useQueryState } from "nuqs";
import { useRef, useState } from "react";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, FormRef, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { usePagination } from "@/hooks/use-pagination";
import { cn } from "@/lib/utils";

import { FIELD_TYPES } from "../../studies/study-detail/tabs/form-studio/fields/columns";

const FIELD_TYPE_OPTIONS = FIELD_TYPES.map((type) => ({
  label: type,
  value: type,
}));

const schema = z.object({
  fieldType: z.string().optional(),
});

export const FilterFieldTemplates = () => {
  const [open, setOpen] = useState(false);
  const [fieldType] = useQueryState("fieldType", parseAsString);

  const filters = [!!fieldType];
  const totalFilters = filters.filter(Boolean).length;

  return (
    <Dropdown open={open} onOpenChange={setOpen} placement="bottom-end">
      <DropdownTrigger>
        <Button variant="outline" className="relative !py-2">
          <ListFilter className="size-5" />

          {!!totalFilters && (
            <i
              style={{
                padding: "4px !important",
              }}
              className="bg-primary-600 absolute right-1 top-0 block rounded-lg px-1 not-italic text-white md:right-4"
            >
              {totalFilters}
            </i>
          )}
        </Button>
      </DropdownTrigger>
      <DropdownContent className="z-30 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <FilterContent setOpen={setOpen} />
      </DropdownContent>
    </Dropdown>
  );
};

const FilterContent = ({ setOpen }: { setOpen: (open: boolean) => void }) => {
  const ref = useRef<FormRef<typeof schema>>(null);
  const { goToPage } = usePagination();

  const [fieldType, setFieldType] = useQueryState("fieldType", parseAsString);

  function onSubmit(data: z.infer<typeof schema>) {
    setFieldType(data.fieldType || null);
    goToPage(1);
    setOpen(false);
  }

  function onClear() {
    setFieldType(null);
    goToPage(1);
    ref.current?.formHandler?.reset();
    setOpen(false);
  }

  const defaultValues = {
    fieldType: fieldType || "",
  };

  const hasFilters = !!fieldType;

  return (
    <div className="">
      <Form
        schema={schema}
        mode="onChange"
        onSubmit={onSubmit}
        className="w-80 md:w-96"
        ref={ref}
        defaultValues={defaultValues}
        key={Object.values(defaultValues).join("-")}
      >
        <div className="flex flex-col divide-y">
          {/* Field Type */}
          <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="fieldType"
              className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Field Type
            </Label>
            <Select
              id="fieldType"
              name="fieldType"
              className="h-fit w-full"
              options={FIELD_TYPE_OPTIONS}
              placeholder="Select field type"
            />
          </div>
        </div>

        <div className="flex justify-end gap-5 px-[15px] py-2 md:pb-5 md:pt-2.5">
          <Button
            variant="outline"
            className={cn(
              "w-full justify-center",
              !hasFilters && "pointer-events-none invisible",
            )}
            onClick={() => {
              onClear();
            }}
            type="button"
          >
            Clear Filters
          </Button>
          <Button
            variant="primary"
            type="submit"
            className="w-full justify-center"
          >
            Apply Filters
          </Button>
        </div>
      </Form>
    </div>
  );
};
