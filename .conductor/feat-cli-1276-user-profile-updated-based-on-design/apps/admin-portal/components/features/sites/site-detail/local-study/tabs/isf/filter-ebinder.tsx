import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import {
  Checkbox,
  Form,
  FormRef,
  InputField,
  Select,
} from "@/components/ui/form";
import { DateRangePicker } from "@/components/ui/form/date-picker/date-range-picker";
import { Label } from "@/components/ui/form/label";
import { MultiSelect } from "@/components/ui/form/multi-select";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";

import { useFilterDocuments } from "./hooks/use-filter-documents";

// List of document types
const DOC_TYPES = [
  { label: "PDF", value: "pdf" },
  { label: "DOC", value: "doc" },
  { label: "DOCX", value: "docx" },
  { label: "XLS", value: "xls" },
  { label: "XLSX", value: "xlsx" },
  { label: "CSV", value: "csv" },
  { label: "TXT", value: "txt" },
  { label: "JPG", value: "jpg" },
  { label: "JPEG", value: "jpeg" },
  { label: "PNG", value: "png" },
  { label: "EML", value: "eml" },
];

// List of status options
const STATUS_OPTIONS = [
  { label: "Placeholder", value: "placeholder" },
  { label: "Draft", value: "draft" },
  { label: "Submitted", value: "submitted" },
  { label: "Reviewed", value: "reviewed" },
  { label: "Attention", value: "attention" },
  { label: "Finalized", value: "finalized" },
  { label: "Rejected", value: "rejected" },
];

const schema = z.object({
  extension: z.string().optional(),
  name: z.string().optional(),
  isShowPlaceholder: z.boolean().optional(),
  showAllDetails: z.boolean().optional(),
  status: z.string().array().optional(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
});

export const FilterEBinder = () => {
  const [open, setOpen] = useState(false);
  const isTablet = useMediaQuery("(min-width: 768px)");
  const {
    fromCreatedDate,
    toCreatedDate,
    extension,
    statuses,
    search,
    isShowPlaceholder,
    showAllDetails,
  } = useFilterDocuments();
  const filters = !isTablet
    ? [
        !!extension,
        !!statuses,
        !!fromCreatedDate || !!toCreatedDate,
        !!isShowPlaceholder,
        !!showAllDetails,
        !!search,
      ]
    : [!!extension, !!statuses, !!fromCreatedDate || !!toCreatedDate];

  const totalFilters = filters.filter(Boolean).length;

  return (
    <Dropdown open={open} onOpenChange={setOpen} placement="bottom-end">
      <DropdownTrigger>
        <Button variant="outline" className="relative !py-2 ">
          <ListFilter className="size-5" />

          {!!totalFilters && (
            <i
              style={{
                padding: "4px !important",
              }}
              className="bg-primary-600 absolute right-1 top-0 block rounded-lg px-1 not-italic text-white md:right-4 "
            >
              {totalFilters}
            </i>
          )}
        </Button>
      </DropdownTrigger>
      <DropdownContent className="z-30 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <FilterContent setOpen={setOpen} />
      </DropdownContent>
    </Dropdown>
  );
};

const FilterContent = ({ setOpen }: { setOpen: (open: boolean) => void }) => {
  const ref = useRef<FormRef<typeof schema>>(null);
  const isTablet = useMediaQuery("(min-width: 768px)");

  const {
    setExtension,
    setStatuses,
    setFromCreatedDate,
    setToCreatedDate,
    fromCreatedDate,
    toCreatedDate,
    extension,
    statuses,
    search,
    isShowPlaceholder,
    setIsShowPlaceholder,
    setShowAllDetails,
    showAllDetails,
    setSearch,
    goToPage,
  } = useFilterDocuments();

  function onSubmit(data: z.infer<typeof schema>) {
    setExtension(data.extension || null);
    setStatuses(data.status?.join(",") || null);
    setFromCreatedDate(
      data.dateRange?.from ? data.dateRange.from.toLocaleDateString() : null,
    );
    setToCreatedDate(
      data.dateRange?.to ? data.dateRange.to.toLocaleDateString() : null,
    );
    if (!isTablet) {
      setSearch(data.name || "");
      setShowAllDetails(data.showAllDetails || null);
      setIsShowPlaceholder(data.isShowPlaceholder || null);
    }
    goToPage(1);
    setOpen(false);
  }
  function onClear() {
    setExtension(null);
    setStatuses(null);
    setFromCreatedDate(null);
    setToCreatedDate(null);

    if (!isTablet) {
      setSearch(null);
      setShowAllDetails(null);
      setIsShowPlaceholder(null);
    }
    goToPage(1);

    ref.current?.formHandler?.reset();
    setOpen(false);
  }

  const dateRange =
    fromCreatedDate || toCreatedDate
      ? {
          from: fromCreatedDate ? new Date(fromCreatedDate) : undefined,
          to: toCreatedDate ? new Date(toCreatedDate) : undefined,
        }
      : undefined;

  const defaultValues = {
    extension: extension ?? "",
    status: statuses ? statuses.split(",") : [],
    dateRange,
    name: search || "",
    isShowPlaceholder: isShowPlaceholder || false,
    showAllDetails: showAllDetails || false,
  };

  const hasFilters = isTablet
    ? !!extension || !!statuses || !!fromCreatedDate || !!toCreatedDate
    : !!extension ||
      !!statuses ||
      !!fromCreatedDate ||
      !!toCreatedDate ||
      !!isShowPlaceholder ||
      !!showAllDetails ||
      !!search;

  return (
    <div className="">
      <Form
        schema={schema}
        mode="onChange"
        onSubmit={onSubmit}
        className="w-80 md:w-96"
        ref={ref}
        defaultValues={defaultValues}
        key={Object.values(defaultValues).join("-")}
      >
        <div className="flex flex-col divide-y">
          {/* Document Type */}
          <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="extension"
              className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Extension
            </Label>
            <Select
              id="extension"
              name="extension"
              className="h-fit w-full"
              options={DOC_TYPES}
              placeholder="Select extension"
            />
          </div>

          {/* Status */}
          <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="status"
              className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Status
            </Label>
            <MultiSelect
              name="status"
              className="h-fit w-full"
              options={STATUS_OPTIONS}
              placeholder="Select statuses"
            />
          </div>

          {/* Date Range */}
          <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="dateRange"
              className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Created Date
            </Label>
            <DateRangePicker
              name="dateRange"
              placeholder="Select date"
              maxDate={new Date()}
            />
          </div>
          {!isTablet && (
            <>
              {/* Search */}
              <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
                <Label
                  htmlFor="name"
                  className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
                >
                  Name
                </Label>
                <InputField id="name" name="name" placeholder="Search ..." />
              </div>

              {/* Show Placeholder */}
              <div className="flex items-center justify-between gap-x-2 px-[15px] py-2 md:pb-5 md:pt-2.5">
                <Label
                  htmlFor="isShowPlaceholder"
                  className=" text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
                >
                  Show Placeholder
                </Label>
                <Checkbox
                  className="size-[18px]"
                  name="isShowPlaceholder"
                  id="isShowPlaceholder"
                />
              </div>

              {/* Show All Details */}
              <div className="flex items-center justify-between gap-x-2 px-[15px] py-2 md:pb-5 md:pt-2.5">
                <Label
                  htmlFor="showAllDetails"
                  className=" text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
                >
                  Show All Details
                </Label>
                <Checkbox
                  className="size-[18px]"
                  name="showAllDetails"
                  id="showAllDetails"
                />
              </div>
            </>
          )}
        </div>

        <div className="flex justify-end gap-5 px-[15px] py-2 md:pb-5 md:pt-2.5">
          <Button
            variant="outline"
            className={cn(
              "w-full justify-center",
              !hasFilters && "pointer-events-none invisible",
            )}
            onClick={() => {
              onClear();
            }}
            type="button"
          >
            Clear Filters
          </Button>
          <Button
            variant="primary"
            type="submit"
            className="w-full justify-center"
          >
            Apply Filters
          </Button>
        </div>
      </Form>
    </div>
  );
};
