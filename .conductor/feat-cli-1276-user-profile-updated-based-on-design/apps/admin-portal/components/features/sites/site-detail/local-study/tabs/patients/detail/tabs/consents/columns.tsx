"use client";

import type { ColumnDef } from "@tanstack/react-table";

import { DOCUMENT_ICONS } from "@/components/icons/doc-icons";
import { DocumentTypeBadge } from "@/components/ui/badges/document-type-badge";
import type { Consent } from "@/lib/apis/patients/types/consent";
import {
  CONSENT_TYPE_LABELS,
  ConsentTypeEnum,
} from "@/lib/apis/patients/types/consent";
import { formatDate } from "@/lib/utils";

// type GenerateColumnsConsentsParams = {
//   onView: (consent: Consent) => void;
// };

export const generateColumnsConsents = (): ColumnDef<Consent>[] => {
  return [
    {
      accessorKey: "type",
      id: "extension",
      header: "Type",
      cell: ({ row }) => {
        const consent = row.original;
        return (
          <DocumentTypeBadge
            type={consent.sourceDocument?.fileRecord!.extension}
          />
        );
      },
    },
    {
      accessorKey: "title",
      id: "title",
      header: "Name",
      cell: ({ row }) => {
        const consent = row.original;
        return (
          <div
            className="flex w-fit gap-2.5 whitespace-nowrap font-medium text-purple-500 underline-offset-4 hover:underline"
            // onClick={() => onView(consent)}
          >
            {DOCUMENT_ICONS[row.original.sourceDocument.fileRecord!.extension]}
            {consent.title}
          </div>
        );
      },
    },
    {
      accessorKey: "uploadedBy",
      id: "uploadedByName",
      header: "Uploaded by",
      cell: ({ row }) => {
        const user =
          row.original.sourceDocument?.fileRecord?.originationProfile?.user;

        if (user) {
          return (
            <span className="whitespace-nowrap">
              {user.firstName} {user.lastName}
            </span>
          );
        }

        return <span className="text-gray-400">N/A</span>;
      },
    },
    {
      accessorKey: "consentTypeId",
      id: "consentType",
      header: "Consent Type",
      cell: ({ row }) => {
        const consent = row.original;
        // Try to find a matching label for the consent type
        const consentTypeKey = Object.keys(ConsentTypeEnum).find(
          (key) =>
            ConsentTypeEnum[key as keyof typeof ConsentTypeEnum] ===
            consent.consentType,
        );

        if (consentTypeKey) {
          const enumValue =
            ConsentTypeEnum[consentTypeKey as keyof typeof ConsentTypeEnum];
          return <div>{CONSENT_TYPE_LABELS[enumValue as ConsentTypeEnum]}</div>;
        }

        // Fallback to displaying the raw value if no match is found
        return <div>{consent.consentType}</div>;
      },
    },
    {
      accessorKey: "consentedDate",
      header: "Effective Date",
      cell: ({ row }) => {
        const consent = row.original;

        if (consent.consentedDate) return formatDate(consent.consentedDate);

        return <span className="text-gray-400">N/A</span>;
      },
    },
    {
      accessorKey: "expiresDate",
      header: "Expiration Date",
      cell: ({ row }) => {
        const consent = row.original;
        return (
          <div>
            {consent.expiresDate ? (
              formatDate(consent.expiresDate)
            ) : (
              <span className="text-gray-400">N/A</span>
            )}
          </div>
        );
      },
    },
    // {
    //   id: "actions",
    //   header: "Actions",
    //   cell: ({ row }) => {
    //     const consent = row.original;
    //     return (
    //       <TableViewButton
    //         type="button"
    //         onClick={() => {
    //           onView(consent);
    //         }}
    //       />
    //     );
    //   },
    // },
  ];
};
