import { useQuery } from "@tanstack/react-query";
import type { ComponentProps } from "react";

import api from "@/lib/apis";

import { Select } from "./select";

export const SelectCountries = (props: ComponentProps<typeof Select>) => {
  const { data } = useQuery({
    queryKey: ["countries"],
    queryFn: () => api.addresses.getCountries(),
  });
  return (
    <Select
      {...props}
      placeholder="Select Country"
      options={
        data?.map((country) => ({
          label: country.name,
          value: country.id,
        })) || []
      }
    />
  );
};
