import {
  closestCenter,
  DndContext,
  type DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useState } from "react";

import { HorizontalLine } from "./horizontal-line";
import { InlineTextEditor } from "./inline-text-editor";
import { Question } from "./question";

type FormElementType = "question" | "horizontal-line" | "text" | "header";

type FormElement = {
  id: string;
  type: FormElementType;
  data?: any;
};

export const QuestionsTab = () => {
  const [elements, setElements] = useState<FormElement[]>([
    { id: "question-1", type: "question" },
    { id: "horizontal-line-1", type: "horizontal-line" },
    { id: "text-1", type: "text" },
    { id: "header-1", type: "header" },
  ]);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor),
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setElements((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  return (
    <div className="h-full overflow-y-auto bg-gray-50 dark:bg-gray-800">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToVerticalAxis]}
      >
        <SortableContext
          items={elements.map((el) => el.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-5 p-0">
            {elements.map((element) => {
              switch (element.type) {
                case "question":
                  return <Question key={element.id} id={element.id} />;
                case "horizontal-line":
                  return <HorizontalLine key={element.id} id={element.id} />;
                case "text":
                  return (
                    <InlineTextEditor
                      key={element.id}
                      id={element.id}
                      type="text"
                    />
                  );
                case "header":
                  return (
                    <InlineTextEditor
                      key={element.id}
                      id={element.id}
                      type="header"
                    />
                  );
                default:
                  return null;
              }
            })}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
};
