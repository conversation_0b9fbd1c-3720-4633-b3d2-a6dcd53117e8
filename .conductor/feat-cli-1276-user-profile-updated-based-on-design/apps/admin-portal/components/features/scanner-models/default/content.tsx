"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { ScannerModel } from "@/lib/apis/scanner-models";

import { ModalScannerModel } from "../modal-scanner-models";
import { getColumns } from "./columns";
import { useScannerModels } from "./hooks/use-scanner-models";

const BREADCRUMB_ITEMS = [{ label: "Scanner Models" }];

const ScannerModelPageContent = () => {
  const { data, isLoading } = useScannerModels();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState<ScannerModel | null>(null);

  const exportCSVData = useMemo(() => {
    if (!data || !data.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Company Name", key: "companyName" },
      { label: "Model Name", key: "modelName" },
      { label: "Description", key: "description" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((model) => [
        model.id,
        model.companyName,
        model.modelName,
        model.description,
      ]),
    ];
  }, [data]);

  const handleViewModel = (model: ScannerModel) => {
    setSelectedModel(model);
    setIsModalOpen(true);
  };

  const handleAddModel = () => {
    setSelectedModel(null);
    setIsModalOpen(true);
  };

  return (
    <>
      <div className="space-y-4">
        <Breadcrumb items={BREADCRUMB_ITEMS} />
        <PageHeader>Scanner Models</PageHeader>
        <Card className="[&>div]:p-0">
          <HeaderActions
            data={exportCSVData}
            buttonText="Add Scanner Model"
            filename="scanner-models.csv"
            onButtonClick={handleAddModel}
          />
          {isLoading && <TableLoading columns={getColumns(handleViewModel)} />}
          {data && (
            <>
              <Table
                data={data.results}
                columns={getColumns(handleViewModel)}
              />
              {data.metadata && (
                <TableDataPagination metadata={data.metadata} />
              )}
            </>
          )}
        </Card>
      </div>
      {isModalOpen && (
        <ModalScannerModel
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          model={selectedModel}
        />
      )}
    </>
  );
};

export default ScannerModelPageContent;
