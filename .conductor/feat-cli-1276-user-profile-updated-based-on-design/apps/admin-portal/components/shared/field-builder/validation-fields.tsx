import { useFormContext } from "react-hook-form";

import { InputField, Select } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { Radio } from "@/components/ui/form/radio";
import { Switch } from "@/components/ui/form/toggle-switch";
import { LazySelect } from "@/components/ui/lazy-select";
import { useInfiniteCodelistTemplates } from "@/hooks/queries/use-infinite-codelist-templates";
import { useInfiniteStudyCodelists } from "@/hooks/queries/use-infinite-study-codelists";
import { Codelist } from "@/lib/apis/codelist-definitions/types";
import { CodelistTemplate } from "@/lib/apis/codelist-templates/types";
import { cn } from "@/lib/utils";

import { normalizeCodelist } from "./utils";

const TextValidations = () => {
  return (
    <div className="grid grid-cols-3">
      <div className="space-y-1">
        <Label htmlFor="config.maxLength">Max Length</Label>
        <InputNumber
          name="config.maxLength"
          id="config.maxLength"
          placeholder="Enter maximum character length..."
          min={1}
          decimalScale={0}
        />
      </div>
    </div>
  );
};

type NumberValidationsProps = {
  type: "integer" | "decimal";
};

const NumberValidations = ({ type }: NumberValidationsProps) => {
  const isDecimal = type === "decimal";

  return (
    <div
      className={cn("grid gap-4", isDecimal ? "grid-cols-4" : "grid-cols-3")}
    >
      <div className="space-y-1">
        <Label htmlFor="config.min">Min Value</Label>
        <InputNumber
          name="config.min"
          id="config.min"
          placeholder="Enter minimum value..."
          decimalScale={isDecimal ? 2 : 0}
        />
      </div>
      <div className="space-y-1">
        <Label htmlFor="config.max">Max Value</Label>
        <InputNumber
          name="config.max"
          id="config.max"
          placeholder="Enter maximum value..."
          decimalScale={isDecimal ? 2 : 0}
        />
      </div>
      {isDecimal && (
        <div className="space-y-1">
          <Label htmlFor="config.decimalPlaces">Decimal Places</Label>
          <InputNumber
            name="config.decimalPlaces"
            id="config.decimalPlaces"
            placeholder="Enter decimal places (e.g., 2)..."
            min={0}
            max={10}
            decimalScale={0}
          />
        </div>
      )}
      <div className="space-y-1">
        <Label htmlFor="config.unitOfMeasure">Unit of Measure</Label>
        <InputField
          name="config.unitOfMeasure"
          id="config.unitOfMeasure"
          placeholder="Enter unit (e.g., kg, cm, years)..."
        />
      </div>
    </div>
  );
};

export const DATE_FORMAT = ["mm/dd/yyyy", "dd/mm/yyyy", "yyyy/mm/dd"];
export const TIME_FORMAT = "HH:mm";

type DateValidationsProps = {
  type: "Date" | "DateTime";
};

const DateValidations = ({ type }: DateValidationsProps) => {
  const isDateTime = type === "DateTime";

  const dateFormatOptions = DATE_FORMAT.map((format) => {
    const fullFormat = isDateTime ? `${format} ${TIME_FORMAT}` : format;
    return {
      label: fullFormat.toUpperCase(),
      value: format,
    };
  });

  return (
    <div className="grid grid-cols-3 gap-4">
      <div className="space-y-1">
        <Label required htmlFor="config.dateFormat">
          Date Format
        </Label>
        <Select
          name="config.dateFormat"
          id="config.dateFormat"
          options={dateFormatOptions}
          placeholder="Select date format"
        />
      </div>

      <div className="flex items-center gap-4">
        <div className="flex justify-end gap-2">
          <Label htmlFor="config.disableFutureDates">
            Disable Future Dates
          </Label>
          <Switch sizing="sm" name="config.disableFutureDates" />
        </div>
        <div className="flex justify-end gap-2">
          <Label htmlFor="config.disablePastDates">Disable Past Dates</Label>
          <Switch sizing="sm" name="config.disablePastDates" />
        </div>
      </div>
    </div>
  );
};

type ChoiceValidationsProps = {
  showLayoutField?: boolean;
  mode: "global" | "study";
};

const ChoiceValidations = ({
  showLayoutField = true,
  mode,
}: ChoiceValidationsProps) => {
  const { setValue } = useFormContext();

  const handleSelect = (codelist: Codelist | CodelistTemplate | null) => {
    const normalizedCodelist = codelist
      ? normalizeCodelist(codelist)
      : undefined;
    setValue("config.codelist", normalizedCodelist);
  };

  return (
    <div className="grid grid-cols-3 gap-4">
      <div className="space-y-1">
        <Label required htmlFor="config.codelistId">
          {mode === "study" ? "CodeList" : "CodeList Template"}
        </Label>
        {mode === "study" ? (
          <LazySelect
            id="config.codelistId"
            name="config.codelistId"
            searchPlaceholder="Search codelist..."
            useInfiniteQuery={useInfiniteStudyCodelists}
            getOptionLabel={(codeList: Codelist) =>
              `${codeList.label} (${codeList.name})`
            }
            getOptionValue={(codeList: Codelist) => codeList.id}
            onSelect={handleSelect}
            placeholder="Select codelist"
            params={["PUBLISHED"]}
          />
        ) : (
          <LazySelect
            id="config.codelistId"
            name="config.codelistId"
            searchPlaceholder="Search codelist template..."
            useInfiniteQuery={useInfiniteCodelistTemplates}
            getOptionLabel={(codeList: CodelistTemplate) =>
              `${codeList.label} (${codeList.name})`
            }
            getOptionValue={(codeList: CodelistTemplate) => codeList.id}
            onSelect={handleSelect}
            placeholder="Select codelist template"
            params={["PUBLISHED"]}
          />
        )}
      </div>

      {showLayoutField && (
        <div className="space-y-2">
          <Label>Layout Direction</Label>
          <div className="flex gap-6">
            <div className="flex items-center gap-2">
              <Radio
                name="config.layoutDirection"
                value="horizontal"
                id="layout-horizontal"
              />
              <Label htmlFor="layout-horizontal">Horizontal</Label>
            </div>
            <div className="flex items-center gap-2">
              <Radio
                name="config.layoutDirection"
                value="vertical"
                id="layout-vertical"
              />
              <Label htmlFor="layout-vertical">Vertical</Label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export {
  ChoiceValidations,
  DateValidations,
  NumberValidations,
  TextValidations,
};
