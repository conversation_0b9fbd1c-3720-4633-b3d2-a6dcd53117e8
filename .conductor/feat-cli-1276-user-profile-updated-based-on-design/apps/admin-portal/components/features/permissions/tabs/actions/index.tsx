import { Card } from "flowbite-react";
import React, { useMemo, useState } from "react";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { Permission } from "@/lib/apis/roles";

import { ActionModal } from "./action-modal";
import { generatePermissionActionColumns } from "./columns";
import { usePermissionActions } from "./hooks/use-permission-actions-queries";

export const ActionsTab = () => {
  const { data, isPending, isPlaceholderData } = usePermissionActions();

  const [selectedAction, setSetSelectedAction] = useState<Permission | null>(
    null,
  );

  const columns = useMemo(
    () =>
      generatePermissionActionColumns({
        onView: (data) => {
          setSetSelectedAction(data);
        },
      }),
    [],
  );
  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="space-y-2 p-4">
          <div className="flex flex-col justify-end gap-4 sm:flex-row sm:items-center">
            <div className="sm:max-w-60">
              <SearchField
                queryKey="subject"
                placeholder="Search by subject..."
              />
            </div>
            <div className="sm:max-w-60">
              <SearchField
                queryKey="action"
                placeholder="Search by action..."
              />
            </div>
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData}>
              <TableData data={data?.results ?? []} columns={columns} />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
      {!!selectedAction && (
        <ActionModal
          selectedAction={selectedAction}
          isOpen={!!selectedAction}
          onClose={() => setSetSelectedAction(null)}
        />
      )}
    </>
  );
};
