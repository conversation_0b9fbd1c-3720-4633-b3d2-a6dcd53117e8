import {
  Accordion,
  AccordionContent,
  AccordionPanel,
  AccordionTitle,
} from "flowbite-react";

import { VisitStatusBadge } from "@/components/ui/badges/visit-status-badge";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Skeleton } from "@/components/ui/skeleton";
import { VisitActivity } from "@/lib/apis/patient-visits";
import { createRandomArray, formatDate } from "@/lib/utils";

import { useLocalStudyParams } from "../../../../../hooks/useLocalStudyParams";
import { usePatientVisit } from "./hook/use-visit-queries";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  visitId: string;
};

export const VisitModal = ({ isOpen, onClose, visitId }: Props) => {
  const { patientId } = useLocalStudyParams();
  const { data, isPending } = usePatientVisit({
    visitId,
    patientId,
  });
  return (
    <WrapperModal
      size="3xl"
      isOpen={isOpen}
      onClose={onClose}
      title="Visit Information"
    >
      {isPending ? (
        <OverviewSkeleton />
      ) : (
        <table className="mb-4 dark:text-white">
          <tr>
            <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
              Visit Name
            </td>
            <td className="w-full font-semibold">
              {data?.name ?? (
                <span className="text-gray-400 dark:text-gray-500">N/A</span>
              )}
            </td>
          </tr>
          <tr>
            <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
              Status
            </td>
            <td className="w-full font-semibold">
              <VisitStatusBadge status={data?.status?.name ?? "Pending"} />
            </td>
          </tr>
          <tr>
            <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
              Study Arm
            </td>
            <td className="w-full font-semibold">
              {data?.studyArm?.name ?? (
                <span className="text-gray-400 dark:text-gray-500">N/A</span>
              )}
            </td>
          </tr>
          <tr>
            <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
              Expected Visit Date
            </td>
            <td className="w-full font-semibold">
              {data?.visitDay ? (
                `Day ${data.visitDay} (${data.visitWindowStart ?? 0}/+${data.visitWindowEnd ?? 0})`
              ) : (
                <span className="text-gray-400 dark:text-gray-500">N/A</span>
              )}
            </td>
          </tr>
          <tr>
            <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
              Visit Date
            </td>
            <td className="w-full font-semibold">
              {data?.visitDate ? (
                formatDate(data.visitDate, "LLL dd, yyyy")
              ) : (
                <span className="text-gray-400 dark:text-gray-500">N/A</span>
              )}
            </td>
          </tr>
          <tr>
            <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
              Study Epoch
            </td>
            <td className="w-full font-semibold">
              {data?.epoch?.name ?? (
                <span className="text-gray-400 dark:text-gray-500">N/A</span>
              )}
            </td>
          </tr>
          <tr>
            <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
              Description
            </td>
            <td className="w-full font-semibold">
              {data?.description ?? (
                <span className="text-gray-400 dark:text-gray-500">N/A</span>
              )}
            </td>
          </tr>
          <tr>
            <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
              Activity
            </td>
            <td className="w-full font-semibold">
              <span className="flex size-6 items-center justify-center rounded-full bg-blue-500 text-sm font-semibold leading-6 text-white dark:bg-blue-400">
                {data?.activities?.length ?? 0}
              </span>
            </td>
          </tr>
        </table>
      )}
      {isPending
        ? createRandomArray().map((i) => <ActivitySkeleton key={i} />)
        : data?.activities?.map((activity) => (
            <Activity activity={activity} key={activity.id} />
          ))}
    </WrapperModal>
  );
};

const Activity = ({ activity }: { activity: VisitActivity }) => (
  <Accordion collapseAll className="mb-2 last:mb-0">
    <AccordionPanel>
      <AccordionTitle className="p-4 dark:hover:border-gray-700 dark:focus:ring-0">
        {activity.name}
      </AccordionTitle>
      <AccordionContent>
        <ul className="flex flex-wrap gap-2">
          {activity?.procedures.length ? (
            activity?.procedures?.map((procedure) => (
              <li
                className="w-fit cursor-pointer rounded-full border border-gray-200 px-[14px] py-2 text-center text-base font-medium leading-[18px] transition-all hover:border-blue-500 hover:bg-blue-50 dark:border-gray-600 dark:text-white dark:hover:border-blue-400 dark:hover:bg-blue-950"
                key={procedure.name}
              >
                {procedure.name}
              </li>
            ))
          ) : (
            <li className="italic text-gray-400 dark:text-gray-500">
              No procedures
            </li>
          )}
        </ul>
      </AccordionContent>
    </AccordionPanel>
  </Accordion>
);

const ActivitySkeleton = () => (
  <div className="mb-2 rounded-lg border border-gray-200 p-4 last:mb-0 dark:border-gray-700">
    <Skeleton className="h-6 w-48" />
  </div>
);

const OverviewSkeleton = () => (
  <table>
    <tr>
      <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
        <Skeleton className="h-4 w-14" />
      </td>
      <td className="w-full font-semibold">
        <Skeleton className="h-4 w-20" />
      </td>
    </tr>
    <tr>
      <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
        <Skeleton className="h-4 w-10" />
      </td>
      <td className="w-full font-semibold">
        <Skeleton className="h-4 w-28" />
      </td>
    </tr>
    <tr>
      <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
        <Skeleton className="h-4 w-14" />
      </td>
      <td className="w-full font-semibold">
        <Skeleton className="h-4 w-20" />
      </td>
    </tr>
    <tr>
      <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
        <Skeleton className="h-4 w-28" />
      </td>
      <td className="w-full font-semibold">
        <Skeleton className="h-4 w-40" />
      </td>
    </tr>
    <tr>
      <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
        <Skeleton className="h-4 w-14" />
      </td>
      <td className="w-full font-semibold">
        <Skeleton className="h-4 w-24" />
      </td>
    </tr>
    <tr>
      <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
        <Skeleton className="h-4 w-16" />
      </td>
      <td className="w-full font-semibold">
        <Skeleton className="h-4 w-28" />
      </td>
    </tr>
    <tr>
      <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
        <Skeleton className="h-4 w-16" />
      </td>
      <td className="w-full font-semibold">
        <Skeleton className="h-4 w-52" />
      </td>
    </tr>
    <tr>
      <td className="whitespace-nowrap py-2 pr-2 text-xs sm:pr-6">
        <Skeleton className="h-4 w-12" />
      </td>
      <td className="w-full font-semibold">
        <Skeleton className="size-6" />
      </td>
    </tr>
  </table>
);
