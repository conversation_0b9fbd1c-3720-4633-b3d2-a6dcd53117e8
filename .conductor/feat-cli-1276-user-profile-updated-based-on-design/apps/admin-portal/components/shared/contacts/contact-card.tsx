import { Card } from "flowbite-react";
import Link from "next/link";
import { useState } from "react";
import { CiEdit } from "react-icons/ci";
import { FaLocationDot } from "react-icons/fa6";
import {
  MdLocalPhone,
  MdOutlineAlternateEmail,
  MdOutlineEmail,
} from "react-icons/md";

import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import type { Contact } from "@/lib/apis/contacts";

import { EditContactModal } from "./edit-contact";

type ContactCardProps = {
  contact?: Contact;
  isLoading?: boolean;
  disableEdit?: boolean;
  onMutate?: () => void;
  isPending?: boolean;
};

export const ContactCard = ({
  contact,
  isLoading,
  disableEdit = false,
}: ContactCardProps) => {
  const [showModalEditContact, setShowModalEditContact] = useState(false);
  const { addressLine, city, stateProvince, country, zipPostalCode } =
    contact?.address ?? {
      addressLine: "",
      city: "",
      stateProvince: null,
      country: null,
      zipPostalCode: "",
    };

  if (isLoading) {
    return <ContactCardSkeleton />;
  }
  if (!contact?.email && !contact?.phone) return null;

  return (
    <>
      <Card className="[&>div]:p-4">
        <div className="text-md mb-4 font-semibold dark:text-white">{`${contact?.firstName} ${contact?.lastName}`}</div>
        <div className="flex flex-1 flex-col gap-2">
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <div className="rounded-full bg-gray-200 p-1">
              <MdOutlineAlternateEmail />
            </div>
            <div>{contact?.email ?? "N/A"}</div>
          </div>
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <div className="rounded-full bg-gray-200 p-1">
              <MdLocalPhone />
            </div>
            <div>{contact?.phone ?? "N/A"}</div>
          </div>
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <div className="rounded-full bg-gray-200 p-1">
              <FaLocationDot />
            </div>
            <div>
              {addressLine ? `${addressLine},` : ""} {city ? `${city},` : ""}
              {stateProvince?.name ? `${stateProvince?.name},` : ""}
              {country?.name ? `${country?.name},` : ""} {zipPostalCode}
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-2 sm:gap-4">
          {contact?.email ? (
            <Link href={`mailto:${contact?.email}`} className="w-fit">
              <Button
                color="white"
                className="border border-gray-400 px-1 py-1.5 dark:text-gray-400"
              >
                <MdOutlineEmail />
                Contact
              </Button>
            </Link>
          ) : (
            <Button
              color="white"
              className="border border-gray-300 px-1 py-1.5 dark:text-white"
              disabled
            >
              <MdOutlineEmail />
              Contact
            </Button>
          )}
          <Button
            variant="primary"
            className="px-1 py-1.5"
            onClick={() => setShowModalEditContact(true)}
            disabled={disableEdit}
          >
            <CiEdit />
            Edit
          </Button>
        </div>
      </Card>
      {showModalEditContact && contact && (
        <EditContactModal
          showModalEditContact={showModalEditContact}
          setShowModalEditContact={setShowModalEditContact}
          contact={contact}
        />
      )}
    </>
  );
};

const ContactCardSkeleton = () => {
  return (
    <Card className="[&>div]:p-4">
      <Skeleton className="mb-4 h-6 w-48" />
      <div className="mb-2 flex items-center gap-1">
        <Skeleton className="h-6 w-6 rounded-full" />
        <Skeleton className="h-4 w-48" />
      </div>
      <div className="mb-2 flex items-center gap-1">
        <Skeleton className="h-6 w-6 rounded-full" />
        <Skeleton className="h-4 w-32" />
      </div>
      <div className="flex items-center gap-1">
        <Skeleton className="h-6 w-6 rounded-full" />
        <Skeleton className="h-4 w-64" />
      </div>
      <div className="flex justify-end">
        <div className="mt-6 grid w-2/3 grid-cols-2 gap-2">
          <Skeleton className="h-9 w-full" />
          <Skeleton className="h-9 w-full" />
        </div>
      </div>
    </Card>
  );
};
