import { skipToken, useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";
import { MILLISECONDS_IN_MINUTE } from "@/lib/constants";

import { useFilterPrompt } from "./use-filter-prompts";

export const promptTemplateKeys = {
  all: () => ["prompt-templates"] as const,

  allLists: () => [...promptTemplateKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...promptTemplateKeys.all(), "list", { params }] as const,

  allModelLists: () => [...promptTemplateKeys.all(), "model-list"] as const,
  listModel: (params?: { provider?: string }) => [
    ...promptTemplateKeys.allModelLists(),
    params,
  ],
};

export const usePromptTemplates = () => {
  const { active, name, version, key, model, modelProvider } =
    useFilterPrompt();
  const { page, take } = usePagination();

  const params = {
    page,
    take,
    filter: {
      isActive: active || undefined,
      name: name || undefined,
      version: version || undefined,
      key: key || undefined,
      model: model || undefined,
      modelProvider: modelProvider || undefined,
    },
  };

  return useQuery({
    queryKey: promptTemplateKeys.list(params),
    queryFn: () => api.promptTemplate.list(params),
    placeholderData: (prev) => prev,
  });
};

export const useModels = (provider?: string) => {
  return useQuery({
    queryKey: promptTemplateKeys.listModel({
      provider,
    }),
    queryFn: provider
      ? () =>
          api.promptTemplate.listModel({
            provider,
          })
      : skipToken,
    staleTime: MILLISECONDS_IN_MINUTE * 60,
  });
};
