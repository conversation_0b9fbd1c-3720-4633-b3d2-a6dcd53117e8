import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import toast from "react-hot-toast";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import api from "@/lib/apis";
import { Protocol } from "@/lib/apis/protocols";

import { protocolKeys } from "../../hooks/use-protocols-queries";
// import { sleep } from "@/lib/utils";

type ModalUploadProtocolProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedProtocol: Protocol | null;
};

export const ModalUploadProtocol = function ({
  isOpen,
  onClose,
  selectedProtocol,
}: ModalUploadProtocolProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  const { mutateAsync: uploadProtocolDocument } = useMutation({
    mutationFn: (data: { protocolId: string }) =>
      api.protocols.signedUploadUrl(data.protocolId),
  });

  async function onSubmit() {
    if (!selectedFile) {
      toast.error("Please select a file to upload");
      return;
    }

    setIsLoading(true);
    try {
      const { url } = await uploadProtocolDocument({
        protocolId: selectedProtocol?.id as string,
      });
      const response = await fetch(url, {
        method: "PUT",
        body: selectedFile,
        headers: {
          "Content-Type": selectedFile.type,
        },
      });
      if (!response.ok) {
        throw new Error("Failed to upload document");
      }
      toast.success("Protocol uploaded successfully");
    } catch {
      toast.error("Failed to upload document");
    } finally {
      await queryClient.invalidateQueries({
        queryKey: protocolKeys.detail(selectedProtocol?.id as string),
      });

      // await sleep(2000);
      await queryClient.invalidateQueries({
        queryKey: ["downloaded-protocol"],
      });
      setIsLoading(false);
      onClose();
    }
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    setSelectedFile(file);
  };

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>Upload Protocol</Modal.Header>
      <Modal.Body>
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label required htmlFor="protocolDocument">
              Protocol Document
            </Label>
            <input
              required
              type="file"
              id="protocolDocument"
              name="protocolDocument"
              onChange={handleFileChange}
              accept=".pdf"
              className="block w-full cursor-pointer rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400"
            />
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="button"
            variant="primary"
            disabled={!selectedFile || isLoading}
            isLoading={isLoading}
            onClick={onSubmit}
          >
            Upload Protocol
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};
