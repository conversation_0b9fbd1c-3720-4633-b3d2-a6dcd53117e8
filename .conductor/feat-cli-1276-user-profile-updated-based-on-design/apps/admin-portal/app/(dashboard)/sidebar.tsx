"use client";

import { Sidebar, TextInput } from "flowbite-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import type { ComponentProps, FC, HTMLAttributeAnchorTarget } from "react";
import { useEffect, useState } from "react";
import { CgTemplate } from "react-icons/cg";
import { FaChalkboardTeacher, FaClipboard, FaCog } from "react-icons/fa";
import {
  FaBuilding,
  FaPlus,
  FaPrint,
  FaUser,
  FaWpforms,
} from "react-icons/fa6";
import { HiSearch } from "react-icons/hi";
import { MdPayment } from "react-icons/md";
import { MdAdminPanelSettings, MdDashboard } from "react-icons/md";
import { twMerge } from "tailwind-merge";

import { useSidebarContext } from "@/contexts/sidebar-context";

type SidebarItem =
  | {
      hasChildren: false;
      href: string;
      target?: HTMLAttributeAnchorTarget;
      icon?: FC<ComponentProps<"svg">>;
      label: string;
      badge?: string;
      query?: string;
    }
  | {
      hasChildren: true;
      target?: HTMLAttributeAnchorTarget;
      icon?: FC<ComponentProps<"svg">>;
      label: string;
      items: SubSidebarItem[];
      badge?: string;
      query?: string;
    };

type SubSidebarItem = {
  href: string;
  label: string;
  query?: string;
  target?: HTMLAttributeAnchorTarget;
};

const SIDEBAR_ITEMS = [
  {
    icon: MdDashboard,
    label: "Dashboard",
    hasChildren: false as const,
    href: "/dashboards",
  },
  {
    icon: FaUser,
    label: "Users",
    items: [
      { href: "/users", label: "All Users" },
      { href: "/users/invitations", label: "Invitations" },
    ],
    hasChildren: true as const,
  },
  {
    icon: MdAdminPanelSettings,
    label: "Access Control",
    items: [
      { href: "/roles", label: "Roles", query: "?active=true" },
      { href: "/permissions", label: "Permissions" },
    ],
    hasChildren: true as const,
  },

  {
    href: "/sponsors",
    icon: FaPlus,
    label: "Sponsors",
    hasChildren: false as const,
  },
  {
    href: "/studies",
    icon: FaClipboard,
    label: "Studies",
    hasChildren: false as const,
  },
  {
    href: "/sites",
    icon: FaBuilding,
    label: "Sites",
    hasChildren: false as const,
  },
  {
    href: "/form-templates",
    icon: FaWpforms,
    label: "Form Templates",
    hasChildren: false as const,
  },

  {
    icon: FaPrint,
    label: "Scanners Settings",
    hasChildren: true as const,
    items: [
      { href: "/scanners", label: "Scanners" },
      { href: "/scanner-applications", label: "Scanner Applications" },
      { href: "/scanner-models", label: "Scanner Models" },
    ],
  },
  {
    href: "/training",
    icon: FaChalkboardTeacher,
    label: "Training",
    hasChildren: false as const,
  },
  {
    href: "/isf-templates",
    icon: CgTemplate,
    label: "ISF Templates",
    hasChildren: false as const,
  },
  {
    href: "/billings",
    icon: MdPayment,
    label: "Billings",
    hasChildren: false as const,
  },
  {
    icon: FaCog,
    label: "Settings",
    items: [
      {
        href: "/settings/visit-types",
        label: "Visit Types",
        query: "?isActive=true",
      },
      {
        href: "/settings/artifact-categories",
        label: "Artifact Categories",
        query:
          "?isActive=true&versionLatest=true&orderBy=documentNumber&orderDirection=desc",
      },
      {
        href: "/settings/app-prompts",
        label: "App Prompts",
      },
      {
        href: "/settings/groups",
        label: "Groups",
      },
      {
        href: "/settings/configuration",
        label: "Configuration",
      },
    ],
    hasChildren: true as const,
  },
];

const isActive = (pathname: string, href: string): boolean => {
  if (href === "/users") {
    return pathname.startsWith(href) && pathname !== "/users/invitations";
  }
  return pathname.startsWith(href);
};

export function DashboardSidebar() {
  return (
    <>
      <div className="lg:hidden">
        <MobileSidebar />
      </div>
      <div className="hidden lg:block">
        <DesktopSidebar />
      </div>
    </>
  );
}

function DesktopSidebar() {
  const { isCollapsed } = useSidebarContext().desktop;
  const [isPreview, setIsPreview] = useState(isCollapsed);

  useEffect(() => {
    if (isCollapsed) setIsPreview(false);
  }, [isCollapsed]);

  const preview = {
    enable() {
      if (!isCollapsed) return;

      setIsPreview(true);
      // setCollapsed(false);
    },
    disable() {
      if (!isPreview) return;

      // setCollapsed(true);
    },
  };

  return (
    <Sidebar
      onMouseEnter={preview.enable}
      onMouseLeave={preview.disable}
      // aria-label="Sidebar with multi-level dropdown example"
      collapsed={isCollapsed}
      className={twMerge(
        "fixed inset-y-0 left-0 z-20 flex h-full w-[270px] shrink-0 flex-col border-r border-gray-200 pt-16 duration-75 lg:flex dark:border-gray-700 [&>*]:bg-[#212a36] [&>*]:text-white",
        isCollapsed && "hidden w-16",
      )}
      id="sidebar"
    >
      <div className="flex h-full flex-col justify-between">
        <div className="py-2">
          <Sidebar.Items>
            <Sidebar.ItemGroup className="mt-0 border-t-0 pb-1 pt-0">
              {SIDEBAR_ITEMS.map((item) => (
                <SidebarItem key={item.label} {...item} />
              ))}
            </Sidebar.ItemGroup>
            {/* <Settings /> */}
          </Sidebar.Items>
        </div>
      </div>
    </Sidebar>
  );
}

function MobileSidebar() {
  const { isOpen, close } = useSidebarContext().mobile;

  if (!isOpen) return null;

  return (
    <>
      <Sidebar
        aria-label="Sidebar with multi-level dropdown example"
        className={twMerge(
          "fixed inset-y-0 left-0 z-20 hidden h-full shrink-0 flex-col border-r border-gray-200 pt-16 lg:flex dark:border-gray-700 [&>*]:bg-[#212a36]",
          isOpen && "flex",
        )}
        id="sidebar"
      >
        <div className="flex h-full flex-col justify-between">
          <div className="py-2">
            <form className="pb-3">
              <TextInput
                icon={HiSearch}
                type="search"
                placeholder="Search"
                required
                size={32}
              />
            </form>
            <Sidebar.Items>
              <Sidebar.ItemGroup className="mt-0 border-t-0 pb-1 pt-0">
                {SIDEBAR_ITEMS.map((item) => (
                  <SidebarItem key={item.label} {...item} />
                ))}
              </Sidebar.ItemGroup>
            </Sidebar.Items>
          </div>
        </div>
      </Sidebar>
      <div
        onClick={close}
        aria-hidden="true"
        className="fixed inset-0 z-10 h-full w-full bg-gray-900/50 pt-16 dark:bg-gray-900/90"
      />
    </>
  );
}

function SidebarItem(props: SidebarItem) {
  const pathname = usePathname();

  if (props.hasChildren) {
    const isOpen = props.items.some((item) => pathname.startsWith(item.href));

    return (
      <Sidebar.Collapse
        icon={props.icon}
        label={props.label}
        open={isOpen}
        theme={{ list: "space-y-2 py-2  [&>li>div]:w-full" }}
        className="!text-gray-400 hover:!bg-gray-700 [&>svg]:!text-gray-400"
      >
        {props.items.map((item) => (
          <Sidebar.Item
            key={item.label}
            as={Link}
            href={item.query ? `${item.href}${item.query}` : item.href}
            target={item.target}
            className={twMerge(
              " justify-center hover:!bg-gray-700  [&>*]:font-normal [&>span]:truncate [&>span]:!text-gray-400 [&>span]:hover:text-gray-300",
              isActive(pathname, item.href || "") && "bg-gray-700",
            )}
          >
            {item.label}
          </Sidebar.Item>
        ))}
      </Sidebar.Collapse>
    );
  }
  return (
    <Sidebar.Item
      as={Link}
      href={props.query ? `${props.href}${props.query}` : props.href}
      target={props.target}
      icon={props.icon}
      label={props.badge}
      className={twMerge(
        "hover:!bg-gray-700 [&>*]:font-normal [&>span]:!text-gray-400 [&>span]:hover:text-gray-300 [&>svg]:!text-gray-400",
        pathname.startsWith(props.href) && "bg-gray-700",
      )}
    >
      {props.label}
    </Sidebar.Item>
  );
}
