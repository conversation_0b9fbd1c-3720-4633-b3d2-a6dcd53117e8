import { FolderIcon } from "lucide-react";

import { cn } from "@/lib/utils";

type FolderOverlayProps = {
  folderName?: string;
  fileCount?: number;
};

export const FolderOverlay = ({
  folderName,
  fileCount = 0,
}: FolderOverlayProps) => {
  return (
    <div className="flex h-11 w-fit cursor-grabbing items-center gap-2.5 rounded-lg border border-purple-500 bg-white pr-3 shadow-md">
      <div className="flex flex-1 items-center gap-2.5 rounded-lg px-3 py-2">
        <FolderIcon className={cn("h-5 w-5 text-purple-500")} />
        <span className="select-none text-sm font-medium leading-5">
          {folderName || "Name"}
        </span>
        <span
          className={cn(
            "font-plus-jakarta text-primary-500 ml-2 flex size-6 select-none justify-center rounded-full bg-gray-100 px-2 py-0.5 text-center text-sm font-medium tracking-[0.28px]",
          )}
        >
          {fileCount}
        </span>
      </div>
    </div>
  );
};
