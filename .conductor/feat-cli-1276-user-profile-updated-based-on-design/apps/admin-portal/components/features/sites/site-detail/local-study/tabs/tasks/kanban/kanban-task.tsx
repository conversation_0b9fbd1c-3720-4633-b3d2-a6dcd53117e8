import { useDraggable } from "@dnd-kit/core";
import { formatDistanceToNow } from "date-fns";
import React from "react";

import { TaskPriorityBadge } from "@/components/ui/badges/task-priority-badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Task } from "@/lib/apis/tasks";
import { cn } from "@/lib/utils";

import { useTaskModals } from "../hooks/use-task-modals-store";

type Props = {
  task: Task;
  className?: string;
};

export const KanbanTask = ({ task, className }: Props) => {
  const { onOpenTaskDetailModal } = useTaskModals();
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: `${task.status}_${task.id}`,
    data: task,
  });
  return (
    <div
      className={cn(
        "flex cursor-pointer flex-col gap-2.5 rounded border border-gray-200 p-3 dark:border-gray-400 dark:text-white",
        isDragging && "cursor-pointer opacity-50",
        className,
      )}
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      onClick={() => onOpenTaskDetailModal(task.id)}
    >
      <div
        className="flex select-none justify-between gap-2 text-sm font-bold"
        title={task.name}
      >
        <span className="font-plus-jakarta truncate text-base font-bold leading-normal">
          {task.name}
        </span>
        <TaskPriorityBadge variant={task.priority} />
      </div>
      <span className="select-none text-sm font-medium leading-5">
        {task.assignedTo.profile?.user?.fullName || "Unassigned"}
      </span>
      <div className="flex select-none justify-end gap-2">
        <span className="text-xs font-medium leading-5">
          Due date:{" "}
          {formatDistanceToNow(new Date(task.dueDate), {
            addSuffix: true,
          })}
        </span>
      </div>
    </div>
  );
};

export const KanbanTaskSkeleton = () => (
  <div
    className={cn(
      "flex cursor-pointer flex-col gap-2.5 rounded border border-gray-200 p-3 dark:border-gray-400 dark:text-white",
    )}
  >
    <div className="flex select-none justify-between gap-2 text-sm font-bold">
      <Skeleton className="h-6 w-52" />
      <Skeleton className="h-5 w-16" />
    </div>
    <Skeleton className="h-5 w-32" />
    <div className="flex justify-end">
      <Skeleton className="h-5 w-36" />
    </div>
  </div>
);
