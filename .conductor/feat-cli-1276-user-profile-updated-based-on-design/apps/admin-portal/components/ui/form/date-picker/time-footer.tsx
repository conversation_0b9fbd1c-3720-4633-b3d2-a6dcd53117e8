import { setHours, setMilliseconds, setMinutes, setSeconds } from "date-fns";
import { useTimescape } from "timescape/react";

type TimeFooterProps = {
  selectedDate?: Date;
  onTimeChange?: (date: Date) => void;
};

export const TimeFooter = ({ selectedDate, onTimeChange }: TimeFooterProps) => {
  const currentTime = selectedDate || new Date();

  const { getRootProps, getInputProps } = useTimescape({
    date: currentTime,
    onChangeDate: (date) => {
      if (date && selectedDate && onTimeChange) {
        const updatedDate = setMilliseconds(
          setSeconds(
            setMinutes(
              setHours(selectedDate, date.getHours()),
              date.getMinutes(),
            ),
            0,
          ),
          0,
        );
        onTimeChange(updatedDate);
      }
    },
    hour12: true,
    wheelControl: false,
    wrapAround: false,
  });

  return (
    <div className="flex items-center justify-between gap-4">
      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Time:
      </label>
      <div
        {...(getRootProps() as any)}
        className="flex flex-1 items-center gap-1 rounded-lg bg-gray-100 px-3 py-2 text-sm tabular-nums focus-within:ring-2 focus-within:ring-blue-500 dark:bg-gray-700"
      >
        <input
          {...getInputProps("hours")}
          id="hour"
          className="!w-6 bg-transparent text-center tabular-nums text-gray-900 outline-none focus:rounded focus:bg-blue-200 dark:text-gray-100 dark:focus:bg-blue-800"
        />
        <span className="text-gray-500 dark:text-gray-400">:</span>
        <input
          {...getInputProps("minutes")}
          className="!w-6 bg-transparent text-center tabular-nums text-gray-900 outline-none focus:rounded focus:bg-blue-200 dark:text-gray-100 dark:focus:bg-blue-800"
        />
        <input
          {...getInputProps("am/pm")}
          className="!w-6 bg-transparent text-center uppercase tabular-nums text-gray-900 outline-none focus:rounded focus:bg-blue-200 dark:text-gray-100 dark:focus:bg-blue-800"
        />
      </div>
    </div>
  );
};
