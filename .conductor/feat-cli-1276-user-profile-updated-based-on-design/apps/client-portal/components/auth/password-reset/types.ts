import { z } from "zod";

// Form validation schemas
export const emailSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
});

export const passwordResetSchema = z
  .object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[0-9]/, "Password must contain at least one number"),
    confirmPassword: z.string().min(1, "Please confirm your password"),
    signOutOfOtherSessions: z.boolean().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

// Type definitions
export type EmailFormData = z.infer<typeof emailSchema>;
export type PasswordResetFormData = z.infer<typeof passwordResetSchema>;
export type FormStep = "email" | "verification" | "reset" | "success";

// Step component props
export interface BaseStepProps {
  isLoading: boolean;
  isLoaded: boolean;
  email: string;
}

export interface EmailStepProps extends BaseStepProps {
  onSubmit: (data: EmailFormData, formHandler: any) => void;
}

export interface VerificationStepProps extends BaseStepProps {
  verificationCode: string;
  setVerificationCode: (code: string) => void;
  onSubmit: () => void;
  onResendCode: () => void;
  resendCooldown: number;
  otpError: string | null;
  setOtpError: (error: string | null) => void;
}

export interface ResetPasswordStepProps extends BaseStepProps {
  onSubmit: (data: PasswordResetFormData, formHandler: any) => void;
}

export interface SuccessStepProps extends BaseStepProps {
  onActivateSession: () => Promise<void>;
}

// Hook return types
export interface PasswordVisibilityReturn {
  showPassword: boolean;
  showConfirmPassword: boolean;
  togglePasswordVisibility: () => void;
  toggleConfirmPasswordVisibility: () => void;
}

export interface ResendCooldownReturn {
  cooldown: number;
  startCooldown: (seconds?: number) => void;
  resetCooldown: () => void;
}
