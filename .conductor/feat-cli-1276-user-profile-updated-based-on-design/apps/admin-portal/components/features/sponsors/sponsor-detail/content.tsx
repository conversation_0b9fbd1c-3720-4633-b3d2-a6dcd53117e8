"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import { RiEditLine } from "react-icons/ri";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";
import { formatDate } from "@/lib/utils";

import { useSponsor } from "../hooks/use-sponsor";
import { ModalEditSponsor } from "./modal-edit-sponsor";
import { ContactsTab } from "./sponsor-tabs/contacts-tab";
import { SitesTab } from "./sponsor-tabs/sites-tab";
import { StudiesTab } from "./sponsor-tabs/studies-tab";
import { UsersTab } from "./sponsor-tabs/users";

const SPONSOR_TABS = [
  {
    key: "studies",
    content: <StudiesTab />,
  },
  {
    key: "sites",
    content: <SitesTab />,
  },
  {
    key: "users",
    content: <UsersTab />,
  },
  {
    key: "contact",
    content: <ContactsTab />,
  },
];

export const SponsorDetailPageContent = () => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const { data: sponsor, isPending } = useSponsor(id);

  const { address } = sponsor?.group ?? {};

  const breadcrumbItems = [
    { label: "Sponsors", href: "/sponsors" },
    { label: sponsor?.name ?? "Sponsor Detail", loading: isPending },
  ];
  return (
    <>
      <div className="flex flex-col gap-4 ">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex justify-between gap-x-2">
          <PageHeader showBackButton href="/sponsors">
            {sponsor?.name}
          </PageHeader>

          <Button
            className="flex-shrink-0"
            variant="primary"
            onClick={() => setIsEditModalOpen(true)}
          >
            <RiEditLine />
            Edit Sponsor
          </Button>
        </div>
        {isPending ? (
          <OverviewSkeleton />
        ) : (
          <OverviewCard title="Overview">
            <div className="grid grid-cols-2 gap-2 lg:grid-cols-4 lg:gap-4 xl:grid-cols-5">
              <OverviewItem label="Name" value={sponsor?.name ?? ""} />
              <OverviewItem label="Logo" imgUrl={sponsor?.logoUrl ?? ""} />

              <OverviewItem label="Status">
                <PillBadge variant={sponsor?.isActive ? "success" : "default"}>
                  {sponsor?.isActive ? "Active" : "Inactive"}
                </PillBadge>
              </OverviewItem>
              <OverviewItem
                label="Created Date"
                value={
                  sponsor?.createdDate
                    ? formatDate(sponsor?.createdDate, "MMM dd, yyyy")
                    : ""
                }
              />
              <OverviewItem
                className="col-span-2 lg:col-span-4 xl:col-span-1"
                label="Address"
                value={
                  address
                    ? `${address.addressLine}, ${address.city}, ${address.stateProvince.name}, ${address.zipPostalCode}`
                    : "N/A"
                }
              />
            </div>
          </OverviewCard>
        )}

        <TabsWrapper tabs={SPONSOR_TABS} />
      </div>
      <ModalEditSponsor
        isOpen={isEditModalOpen}
        key={`modal-edit-sponsor-${sponsor?.id}-${isEditModalOpen}`}
        onClose={() => setIsEditModalOpen(false)}
        sponsor={sponsor}
      />
    </>
  );
};

const OverviewSkeleton = () => (
  <OverviewCard title="Overview">
    <div className="grid grid-cols-2 gap-2 lg:grid-cols-4 lg:gap-4 xl:grid-cols-5">
      <div className="space-y-1">
        <Skeleton className="h-6 w-14" />
        <Skeleton className="h-5 w-32" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-14" />
        <Skeleton className="h-9 w-32" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-5 w-16" />
      </div>

      <div className="space-y-1">
        <Skeleton className="h-6 w-28" />
        <Skeleton className="h-5 w-24" />
      </div>

      <div className="col-span-2 space-y-1 lg:col-span-4 xl:col-span-1">
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-5 w-40" />
      </div>
    </div>
  </OverviewCard>
);
