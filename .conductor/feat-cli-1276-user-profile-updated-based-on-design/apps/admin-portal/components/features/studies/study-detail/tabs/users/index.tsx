import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import React from "react";

import { useUsers } from "@/components/features/users/hooks/use-users-queries";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { userColumns } from "./columns";

export const UserTab = () => {
  const studyId = useParams().id;
  const { data, isPending, isPlaceholderData } = useUsers({
    studyId: studyId,
  });
  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold dark:text-gray-400">
        Users
      </div>
      {isPending ? (
        <TableLoading columns={userColumns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table columns={userColumns} data={data?.results ?? []} />
          {data?.metadata && <TableDataPagination metadata={data.metadata} />}
        </LoadingWrapper>
      )}
    </Card>
  );
};
