---
description: Security best practices
globs:
alwaysApply: false
---

# Security Best Practices

## Authentication
- Use secure authentication mechanisms
- Implement proper token handling
- Securely store tokens in HTTP-only cookies
- Implement proper session management
- Use secure and SameSite cookies
- Implement CSRF protection
- Implement proper logout functionality

## Authorization
- Implement proper role-based access control
- Use least privilege principle
- Check permissions on both client and server
- Implement proper authorization checks for API routes
- Avoid client-side authorization bypasses
- Validate user permissions for all operations

## Data Validation
- Validate all user inputs
- Use Zod for schema validation
- Implement proper error handling for validation failures
- Sanitize user inputs
- Validate data on both client and server
- Implement proper type checking

## API Security
- Use HTTPS for all API calls
- Implement proper rate limiting
- Validate API request parameters
- Implement proper error handling
- Avoid exposing sensitive information in responses
- Implement proper logging for security events

## Frontend Security
- Avoid client-side storage of sensitive data
- Implement Content Security Policy
- Use proper CORS configuration
- Avoid exposing sensitive information in the DOM
- Secure third-party dependencies
- Keep dependencies up-to-date

## Sensitive Data Handling
- Avoid exposing sensitive data in client-side code
- Encrypt sensitive data in transit
- Implement proper data masking
- Use proper data retention policies
- Implement proper access controls for sensitive data
- Avoid logging sensitive information

## Environment Variables
- Store sensitive configuration in environment variables
- Use proper environment variable validation
- Avoid exposing environment variables to the client
- Use different environment variables for different environments
- Implement proper secrets management
- Document required environment variables

## Dependency Management
- Regularly update dependencies
- Use pnpm audit to check for vulnerabilities
- Implement lockfile for dependencies
- Avoid using deprecated packages
- Use trusted dependencies
- Implement proper dependency review process

## Data Protection
- Sanitize user input
- Implement proper data validation
- Use proper data encryption
- Handle sensitive data properly
- Implement proper data masking

## Dependency Security
- Keep dependencies up to date
- Use proper dependency scanning
- Implement proper dependency management
- Handle vulnerable dependencies properly
- Use proper dependency locking

## Environment Security
- Use proper environment variable handling
- Implement proper secret management
- Use proper configuration management
- Handle sensitive configuration properly
- Implement proper environment isolation

## Deployment Security
- Implement proper CI/CD security
- Use proper deployment verification
- Implement proper rollback procedures
- Handle deployment failures properly
- Implement proper deployment logging

## Monitoring and Logging
- Implement proper security monitoring
- Use proper security logging
- Implement proper security alerting
- Handle security incidents properly
- Implement proper security auditing
