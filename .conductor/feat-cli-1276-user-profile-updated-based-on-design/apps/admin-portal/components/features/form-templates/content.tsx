"use client";

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { CodelistsTab } from "./codelists/codelists-tab";
import { FieldsTab } from "./fields/fields-tab";
import { FormsTab } from "./forms/forms-tab";
import { QuestionTab } from "./questions/questions-tab";

const TEMPLATE_TABS = [
  {
    key: "forms",
    content: <FormsTab />,
  },
  {
    key: "questions",
    content: <QuestionTab />,
  },
  {
    key: "fields",
    content: <FieldsTab />,
  },
  {
    key: "codelists",
    content: <CodelistsTab />,
  },
] as const;
const BREADCRUMB_ITEMS = [{ label: "Form Templates" }];

export const FormTemplatesContent = () => {
  return (
    <>
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader>Form Templates</PageHeader>
      <TabsWrapper tabs={TEMPLATE_TABS} />
    </>
  );
};
