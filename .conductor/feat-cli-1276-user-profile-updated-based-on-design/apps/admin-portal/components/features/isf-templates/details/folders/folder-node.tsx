import { useDraggable, useDroppable } from "@dnd-kit/core";
import {
  ChevronDown,
  ChevronRight,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  MoreVertical,
} from "lucide-react";
import { parseAsString, useQueryState } from "nuqs";
import React, { MouseEvent } from "react";

import {
  Dropdown,
  DropdownContent,
  DropdownItem,
  DropdownSeparator,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Directory } from "@/lib/apis/isf-templates";
import { cn } from "@/lib/utils";

import { FileWithParentDirectoryName } from "../documents/documents-table";

export type TreeNodeProps = {
  folder: Directory;
  activePath: string;
  setActivePath: React.Dispatch<React.SetStateAction<string>>;
  onEdit: (folder: Directory) => void;
  activeId?: string | null;
  draggingFolder: Directory | null;
  onDelete: (data: Directory) => void;
  level: number;
  path: string;
  rootPath: string;
  draggingFile: FileWithParentDirectoryName | null;
  indent?: number;
  parentDirectoryName?: string;
};

export type DirectoryWithPath = Directory & {
  path: string;
  parentDirectoryName?: string;
};

const DEFAULT_INDENT = 25;

export const FolderNode = ({
  draggingFolder,
  activePath,
  setActivePath,
  onDelete,
  onEdit,
  folder,
  level,
  path,
  rootPath,
  draggingFile,
  parentDirectoryName,
  indent = DEFAULT_INDENT,
}: TreeNodeProps) => {
  const [folderName, setFolderName] = useQueryState(
    "folderName",
    parseAsString,
  );

  const isCurrent = folder.name === folderName;

  const {
    isOpen: isOpenDropDown,
    close: onCloseDropDown,
    toggle: onToggleDropDown,
  } = useDisclosure();

  const { isOpen: isOpenFolder, toggle: onToggleFolder } = useDisclosure();

  const {
    attributes,
    listeners,
    setNodeRef: setDragRef,
    isDragging: isDraggingNode,
  } = useDraggable({
    id: folder.name,
    data: { ...folder, path, parentDirectoryName },
  });

  const { setNodeRef: setDropRef, isOver } = useDroppable({
    id: folder.name,
    data: { ...folder, path, parentDirectoryName },
  });

  const isValidDropFolder =
    draggingFolder && isOver
      ? !path.includes(draggingFolder.name) &&
        !folder.directories.some((dir) => dir.name === draggingFolder.name)
      : null;

  const isValidDropFile =
    draggingFile && isOver
      ? folder.name !== draggingFile?.parentDirectoryName
      : null;
  const handleFolderClick = (e: MouseEvent) => {
    setActivePath(path);
    onToggleFolder();
    setFolderName(folder.name);
  };

  return (
    <>
      <div
        ref={(node) => {
          setDragRef(node);
          setDropRef(node);
        }}
        className={cn(
          "group flex w-full cursor-pointer items-center justify-between whitespace-nowrap border border-transparent px-3 py-1.5 transition-colors hover:relative hover:border-purple-500",
          activePath.startsWith(rootPath) && "bg-gray-200 dark:bg-[#1a2b3c]",
          isCurrent ? "text-purple-700" : "text-black dark:text-white",
          isDraggingNode && "opacity-50",
          typeof isValidDropFolder === "boolean"
            ? isValidDropFolder
              ? "border-dashed border-purple-500 bg-purple-50 dark:bg-[#1a2b3c]"
              : "border-dashed border-red-500 !bg-red-300/45"
            : "",
          typeof isValidDropFile === "boolean"
            ? isValidDropFile
              ? "border-dashed border-purple-500 bg-purple-50 dark:bg-[#1a2b3c]"
              : "border-dashed border-red-500 !bg-red-300/45"
            : "",
        )}
        data-path={path}
        style={{
          paddingLeft: level * indent,
        }}
        onClick={handleFolderClick}
        {...attributes}
        {...listeners}
      >
        <div className="flex min-w-0 flex-1 items-center justify-between gap-x-1 pl-4">
          <div className="flex min-w-0 flex-1 items-center gap-2.5">
            <div className="flex items-center">
              {/* Folder toggle icon */}

              <div className="flex w-5 items-center">
                {!!folder.directories.length && (
                  <>
                    {isOpenFolder ? (
                      <ChevronDown
                        className={cn(
                          "size-4",
                          isCurrent
                            ? "text-purple-700"
                            : "text-black dark:text-white",
                        )}
                      />
                    ) : (
                      <ChevronRight
                        className={cn(
                          "size-4",
                          isCurrent
                            ? "text-purple-700"
                            : "text-black dark:text-white",
                        )}
                      />
                    )}
                  </>
                )}
              </div>

              {/* Folder icon */}
              <div className="flex w-5 items-center">
                {isOpenFolder ? (
                  <FolderOpenIcon
                    className={cn(
                      "h-5 w-5",
                      isCurrent
                        ? "text-purple-700"
                        : "text-black dark:text-white",
                    )}
                  />
                ) : (
                  <FolderIcon
                    className={cn(
                      "h-5 w-5",
                      isCurrent
                        ? "text-purple-700"
                        : "text-black dark:text-white",
                    )}
                  />
                )}
              </div>
            </div>

            {/* Folder name */}
            <span className="flex-1 select-none truncate text-sm font-medium leading-5">
              {folder.name}
            </span>

            {/* File count badge */}
            {typeof folder?.files?.length === "number" && (
              <span
                className={cn(
                  "font-plus-jakarta ml-2 flex  select-none justify-center rounded-full px-2 py-0.5 text-center text-sm font-medium tracking-[0.28px]",
                  isCurrent
                    ? "bg-purple-500 text-white"
                    : "text-primary-500 bg-gray-300",
                )}
              >
                {folder.files.length}
              </span>
            )}
          </div>
          <div
            onClick={(e: MouseEvent) => {
              e.stopPropagation();
            }}
          >
            <Dropdown open={isOpenDropDown} onOpenChange={onToggleDropDown}>
              <DropdownTrigger>
                <div className="flex items-center rounded p-1 hover:bg-gray-100">
                  <MoreVertical size={20} className="text-gray-500" />
                </div>
              </DropdownTrigger>
              <DropdownContent>
                <DropdownItem
                  onClick={() => {
                    onEdit(folder);
                    onCloseDropDown();
                  }}
                >
                  <span>Edit</span>
                </DropdownItem>
                <DropdownSeparator />

                {!folder.files.length && (
                  <DropdownItem
                    onClick={() => {
                      onDelete(folder);
                      onCloseDropDown();
                    }}
                    className="text-red-700"
                  >
                    <span>Delete</span>
                  </DropdownItem>
                )}
              </DropdownContent>
            </Dropdown>
          </div>
        </div>
      </div>

      {isOpenFolder &&
        !!folder.directories.length &&
        folder.directories.map((sub) => (
          <FolderNode
            key={sub.name}
            folder={sub}
            level={level + 1}
            draggingFolder={draggingFolder}
            draggingFile={draggingFile}
            path={`${path}/${sub.name}`}
            activePath={activePath}
            setActivePath={setActivePath}
            onDelete={onDelete}
            onEdit={onEdit}
            rootPath={rootPath}
            indent={indent}
            parentDirectoryName={folder.name}
          />
        ))}
    </>
  );
};
