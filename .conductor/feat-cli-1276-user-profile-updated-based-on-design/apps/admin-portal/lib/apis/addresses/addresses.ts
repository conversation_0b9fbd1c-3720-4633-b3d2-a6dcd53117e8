import BaseApi from "../base";
import type { Country, StateProvince } from "./types";

export class AddressesApi extends BaseApi {
  public constructor() {
    super("/addresses", true);
  }

  public async getCountries(): Promise<Country[]> {
    return this.http.get<Country[]>(`/countries`);
  }

  public async getStateProvinces(): Promise<StateProvince[]> {
    return this.http.get<StateProvince[]>(`/stateProvinces`);
  }
}
