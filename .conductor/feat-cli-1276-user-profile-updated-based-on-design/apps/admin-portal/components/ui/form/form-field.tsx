import type { ReactElement } from "react";
import type {
  ControllerFieldState,
  ControllerRenderProps,
  FieldValues,
  UseFormStateReturn,
} from "react-hook-form";
import { Controller, useFormContext } from "react-hook-form";

type RenderFnProps = {
  field: ControllerRenderProps<FieldValues, string>;
  fieldState: ControllerFieldState;
  formState: UseFormStateReturn<FieldValues>;
};

type FormFieldProps = {
  name: string;
  renderer: (props: RenderFnProps) => ReactElement;
};

const FormField = ({ name, renderer }: FormFieldProps) => {
  const { control } = useFormContext();
  return <Controller control={control} name={name} render={renderer} />;
};

export { FormField };
