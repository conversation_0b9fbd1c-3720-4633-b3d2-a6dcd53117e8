"use client";

import { DocumentVolumeChart } from "./components/document-volume-chart";
import { HighLevelSummaryFilter } from "./components/high-level-summary-filter";
import { KPICards } from "./components/kpi-cards";
import { OriginationTypeChart } from "./components/origination-type-chart";
export const HighLevelSummary = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          High-Level Summary
        </h2>
        <HighLevelSummaryFilter />
      </div>

      <KPICards />

      <DocumentVolumeChart />
      <OriginationTypeChart />
    </div>
  );
};
