import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { Protocol, ProtocolEpoch } from "@/lib/apis/protocols";

import { generateEpochColumns } from "./columns";
import { EpochModal } from "./epoch-modal";
import { useProtocolEpochs } from "./hooks/use-epochs-queries";

type Props = {
  selectedProtocol: Protocol | null;
};

export const EpochsTab = ({ selectedProtocol }: Props) => {
  const [isOpenEpochModal, setIsOpenEpochModal] = useState(false);
  const [selectedEpoch, setSelectedEpoch] = useState<ProtocolEpoch | null>(
    null,
  );

  const {
    data: protocolEpochs,
    isPending,
    isPlaceholderData,
  } = useProtocolEpochs(selectedProtocol?.id || "");

  const onEdit = (epoch: ProtocolEpoch) => {
    setSelectedEpoch(epoch);
    setIsOpenEpochModal(true);
  };

  const columns = useMemo(
    () =>
      generateEpochColumns({
        onEdit,
        isPublished: selectedProtocol?.isPublished,
      }),

    [selectedProtocol?.isPublished],
  );

  const onCloseEpochModal = () => {
    setIsOpenEpochModal(false);
    setSelectedEpoch(null);
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">
            Epochs {selectedProtocol && `(${selectedProtocol.name})`}
          </div>
          <Button
            disabled={selectedProtocol?.isPublished}
            variant="primary"
            onClick={() => setIsOpenEpochModal(true)}
          >
            <IoMdAdd />
            Add Epoch
          </Button>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={protocolEpochs?.results ?? []} />
            {protocolEpochs?.metadata && (
              <TableDataPagination metadata={protocolEpochs.metadata} />
            )}
          </LoadingWrapper>
        )}
      </Card>

      {isOpenEpochModal && selectedProtocol?.id && (
        <EpochModal
          selectedEpoch={selectedEpoch}
          isOpen={isOpenEpochModal}
          onClose={onCloseEpochModal}
          protocolId={selectedProtocol.id}
        />
      )}
    </>
  );
};
