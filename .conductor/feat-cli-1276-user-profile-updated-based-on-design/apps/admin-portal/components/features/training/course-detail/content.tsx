"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import { CiEdit } from "react-icons/ci";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";
import { Course } from "@/lib/apis/training-modules";

import { useCourse } from "../hooks/use-training-module-queries";
import { COURSE_STATUS_VARIANTS } from "../tabs/courses/columns";
import { CourseModal } from "../tabs/courses/course-modal";
import { ChapterTab } from "./tabs/chapters/content";
import { EnrollmentTab } from "./tabs/enrollments/content";

const COURSE_TABS = [
  {
    key: "enrollments",
    content: <EnrollmentTab />,
  },
  {
    key: "chapters",
    content: <ChapterTab />,
  },
];

export const CourseContent = () => {
  const courseId = useParams().id as string;
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const { data, isPending } = useCourse(courseId);

  const onCloseEditCourseModal = () => {
    setSelectedCourse(null);
  };

  const breadcrumbItems = [
    { label: "Training", href: "/training" },
    { label: data?.name || "Course Detail", loading: isPending },
  ];

  return (
    <>
      <div className="flex flex-col gap-4 ">
        <Breadcrumb items={breadcrumbItems} />
        <PageHeader showBackButton href="/training">
          {isPending ? (
            <Skeleton className="h-7 w-40" />
          ) : (
            data?.name || "Course Detail"
          )}
        </PageHeader>
        <OverviewCard
          title="Overview"
          rightContent={
            <Button
              onClick={() => {
                setSelectedCourse(data ? data : null);
              }}
              disabled={isPending}
              variant="primary"
              className="px-2 sm:px-4"
            >
              <CiEdit />
              Edit Course
            </Button>
          }
        >
          {isPending ? (
            <OverViewSkeleton />
          ) : (
            <div className="grid grid-cols-2 gap-x-2 gap-y-4">
              <OverviewItem label="Name" value={data?.name} />
              <OverviewItem label="Status">
                {data?.status ? (
                  <PillBadge
                    className="capitalize"
                    variant={COURSE_STATUS_VARIANTS[data?.status]}
                  >
                    {data.status}
                  </PillBadge>
                ) : (
                  "N/A"
                )}
              </OverviewItem>
              <OverviewItem label="Module" value={data?.module.name} />

              <OverviewItem label="Order" value={data?.order?.toString()} />
              <OverviewItem
                className="col-span-2"
                label="Description"
                value={data?.description}
              />
            </div>
          )}
        </OverviewCard>
        <TabsWrapper tabs={COURSE_TABS} />
      </div>
      <CourseModal
        isOpen={!!selectedCourse}
        onClose={onCloseEditCourseModal}
        selectedCourse={selectedCourse}
      />
    </>
  );
};

const OverViewSkeleton = () => {
  return (
    <div className="grid grid-cols-2 gap-x-2 gap-y-4">
      <div className="space-y-1">
        <Skeleton className="h-6 w-14" />
        <Skeleton className="h-5 w-28" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-5 w-28" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-20" />
        <Skeleton className="h-5 w-32" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-5 w-24" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-5 w-40" />
      </div>
    </div>
  );
};
