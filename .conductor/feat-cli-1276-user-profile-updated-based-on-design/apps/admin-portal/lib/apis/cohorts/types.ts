import { Study } from "../studies";
import { ListBaseResponse } from "../types";

export type CohortPayload = {
  studyId: string;
  name: string;
  description?: string;
};

export type CohortItem = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  name: string;
  description: string;
  isActive: boolean;
  studyId: string;
  study: Study;
};

export type CohortListResponse = ListBaseResponse<CohortItem>;
