"use client";

import { Card } from "flowbite-react";
import { type FC, useMemo, useState } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { Study } from "@/lib/apis/studies";

import { useStudies } from "../hooks/use-studies-queries";
import { columns } from "./columns";
import { ModalAddStudy } from "./modal-add-study";

const BREADCRUMB_ITEMS = [{ label: "Studies" }];

const StudyPageContent: FC = function () {
  const [showModalAddStudy, setShowModalAddStudy] = useState(false);
  const { data, isPending } = useStudies();

  const exportCSVData = useMemo(() => {
    if (!data || !data.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Name", key: "name" },
      { label: "Description", key: "description" },
      { label: "Status", key: "isActive" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((study) => {
        return headers.map((header) => {
          if (header.key === "isActive") {
            return study[header.key as keyof Study] ? "Active" : "Inactive";
          }
          return study[header.key as keyof Study];
        });
      }),
    ];
  }, [data]);

  return (
    <>
      <div className="space-y-4">
        <Breadcrumb items={BREADCRUMB_ITEMS} />
        <PageHeader>Studies</PageHeader>
        <Card className="[&>div]:p-0">
          <HeaderActions
            data={exportCSVData}
            buttonText="Add Study"
            filename="studies.csv"
            onButtonClick={() => setShowModalAddStudy(true)}
          />
          {isPending ? (
            <TableLoading columns={columns} />
          ) : (
            <>
              <Table data={data?.results ?? []} columns={columns} />
              {data?.metadata && (
                <TableDataPagination metadata={data.metadata} />
              )}
            </>
          )}
        </Card>
      </div>
      {showModalAddStudy && (
        <ModalAddStudy
          isOpen={showModalAddStudy}
          onClose={() => setShowModalAddStudy(false)}
        />
      )}
    </>
  );
};

export default StudyPageContent;
