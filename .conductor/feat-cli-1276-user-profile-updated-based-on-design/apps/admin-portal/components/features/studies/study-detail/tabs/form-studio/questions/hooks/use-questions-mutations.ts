import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type {
  CreateQuestionPayload,
  UpdateQuestionPayload,
} from "@/lib/apis/questions/types";

import { questionsKeys } from "./use-questions-queries";

export const useCreateQuestion = () =>
  useMutation({
    mutationFn: (payload: CreateQuestionPayload) =>
      api.questions.create(payload),
    onError: (err) => toast.error(err?.message || "Failed to create question"),
    onSettled: (_, err) =>
      !err && toast.success("Question created successfully"),
    meta: { awaits: questionsKeys.allLists() },
  });

export const useUpdateQuestion = () =>
  useMutation({
    mutationFn: (payload: UpdateQuestionPayload) =>
      api.questions.update(payload),
    onError: (err) => toast.error(err?.message || "Failed to update question"),
    onSettled: (_, err) =>
      !err && toast.success("Question updated successfully"),
    meta: { awaits: questionsKeys.allLists() },
  });

export const useArchiveQuestion = () =>
  useMutation({
    mutationFn: (id: string) => api.questions.archive(id),
    onError: (err) => toast.error(err?.message || "Failed to archive question"),
    onSettled: (_, err) =>
      !err && toast.success("Question archived successfully"),
    meta: { awaits: questionsKeys.allLists() },
  });

export const usePublishQuestion = () =>
  useMutation({
    mutationFn: (id: string) => api.questions.publish(id),
    onError: (err) => toast.error(err?.message || "Failed to publish question"),
    onSettled: (_, err) =>
      !err && toast.success("Question published successfully"),
    meta: { awaits: questionsKeys.allLists() },
  });
