---
description:
globs:
alwaysApply: true
---

# Data Fetching and API Integration Guidelines

## API Structure
- Organize API calls by domain/entity
- Use consistent naming conventions for API functions
- Implement proper error handling for all API calls
- Use TypeScript for API request and response types
- Document API functions with JSDoc

## React Query Usage
- Create custom hooks for each API call
- Use proper query keys for caching
- Implement proper error handling
- Use suspense mode when appropriate
- Implement proper retry logic
- Use query invalidation appropriately
- Implement optimistic updates for mutations
- Use prefetching for better UX

## Query Keys
- Structure query keys hierarchically
- Include all dependencies in query keys
- Use consistent naming for query keys
- Export query key constants for reuse

## Error Handling
- Implement proper error boundaries
- Show appropriate error messages to users
- Log errors for debugging
- Implement proper retry logic
- Handle network errors gracefully

## Loading States
- Show appropriate loading states
- Implement skeleton loaders for better UX
- Use suspense boundaries when appropriate
- Avoid UI jumps during loading

## Data Transformation
- Transform API responses to match UI needs
- Use proper TypeScript types for transformed data
- Implement proper data normalization
- Use selectors for derived data

## Pagination and Infinite Loading
- Implement proper pagination for lists
- Use infinite loading for long lists
- Show appropriate loading states for pagination
- Implement proper error handling for pagination

## Caching
- Use proper caching strategies
- Implement proper cache invalidation
- Use stale-while-revalidate pattern
- Implement proper cache time settings

## Authentication
- Handle authentication tokens properly
- Implement proper token refresh logic
- Handle unauthorized errors gracefully
- Redirect to login when needed
