import Base<PERSON>pi from "../base";
import type { MetadataParams } from "../types";
import type {
  CreateQuestionPayload,
  Question,
  QuestionListResponse,
  UpdateQuestionPayload,
} from "./types";

export class Questions<PERSON><PERSON> extends BaseApi {
  constructor() {
    super("/edc/questions", true);
  }

  public async list(params?: MetadataParams): Promise<QuestionListResponse> {
    return this.http.get<QuestionListResponse>("", params);
  }

  public async get(id: string): Promise<Question> {
    return this.http.get<Question>(`/${id}`);
  }

  public async create(payload: CreateQuestionPayload): Promise<Question> {
    return this.http.post<Question>("", payload);
  }

  public async update(payload: UpdateQuestionPayload): Promise<Question> {
    return this.http.put<Question>(`/${payload.id}`, payload);
  }

  public async archive(id: string): Promise<Question> {
    return this.http.patch<Question>(`/${id}/archive`);
  }

  public async publish(id: string): Promise<Question> {
    return this.http.patch<Question>(`/${id}/publish`);
  }
}

export const questions = new QuestionsApi();
