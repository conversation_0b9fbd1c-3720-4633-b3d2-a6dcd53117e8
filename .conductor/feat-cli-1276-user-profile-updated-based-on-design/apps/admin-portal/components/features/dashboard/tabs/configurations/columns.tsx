import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { NoCategoryArtifact, NoTMFISFStudy } from "@/lib/apis/data-integrity";

export const noTMFISFStudiesColumns: ColumnDef<NoTMFISFStudy>[] = [
  // {
  //   accessorKey: "id",
  //   header: "Study ID",
  //   cell: ({ row }) => (
  //     <Link
  //       className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
  //       href={`/studies/${row.original.id}`}
  //     >
  //       {row.original.id}
  //     </Link>
  //   ),
  // },
  {
    accessorKey: "name",
    header: "Study Name",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        href={`/studies/${row.original.id}`}
      >
        {row.original.name}
      </Link>
    ),
  },
];

export const noCategoryArtifactsColumns: ColumnDef<NoCategoryArtifact>[] = [
  // {
  //   accessorKey: "id",
  //   header: "Artifact ID",
  //   cell: ({ row }) =>
  //     row.original.isfFolderId &&
  //     row.original.siteId &&
  //     row.original.studyId ? (
  //       <Link
  //         className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
  //         href={`/sites/${row.original.siteId}/studies/${row.original.studyId}/?tab=isf&folderId=${row.original.isfFolderId}`}
  //       >
  //         {row.original.id}
  //       </Link>
  //     ) : (
  //       <span>{row.original.id}</span>
  //     ),
  // },
  {
    accessorKey: "title",
    header: "Title",
    cell: ({ row }) =>
      row.original.isfFolderId &&
      row.original.siteId &&
      row.original.studyId ? (
        <Link
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
          href={`/sites/${row.original.siteId}/studies/${row.original.studyId}/?tab=isf&folderId=${row.original.isfFolderId}`}
        >
          {row.original.title}
        </Link>
      ) : (
        row.original.title
      ),
  },
];
