import { z } from "zod";

import { VISIT_CATEGORIES } from "@/components/ui/badges";
import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import {
  Checkbox,
  Form,
  InputField,
  Select,
  Textarea,
} from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import type { VisitType } from "@/lib/apis/visit-types";

import {
  useAddStudyVisitType,
  useEditStudyVisitType,
} from "./hooks/use-visit-types-mutations";

export const visitTypeSchema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  visitCategory: z.string().optional(),
  description: z.string().nullable().optional(),
  isActive: z.boolean().default(true),
});

type ModalVisitTypeProps = {
  isOpen: boolean;
  onClose: () => void;
  visitType?: VisitType | null;
};

export const ModalVisitType = ({
  isOpen,
  onClose,
  visitType,
}: ModalVisitTypeProps) => {
  const { mutateAsync: addVisitType, isPending: isAdding } =
    useAddStudyVisitType();
  const { mutateAsync: editVisitType, isPending: isEditing } =
    useEditStudyVisitType();

  const isEditingMode = Boolean(visitType);
  const isSubmitting = isAdding || isEditing;

  async function onSubmit(data: z.infer<typeof visitTypeSchema>) {
    const payload = {
      ...data,
      visitCategory: data.visitCategory || undefined,
    };
    if (isEditingMode && visitType) {
      await editVisitType({ id: visitType.id, ...payload });
    } else {
      await addVisitType(payload);
    }
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>
        {isEditingMode ? "Edit Visit Type" : "Add Visit Type"}
      </Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={visitTypeSchema}
          onSubmit={onSubmit}
          defaultValues={{
            name: visitType?.name || "",
            description: visitType?.description || "",
            isActive: isEditingMode ? visitType?.isActive : true,
            visitCategory: visitType?.visitCategory || "",
          }}
          className="space-y-6"
        >
          <div className="space-y-1">
            <Label htmlFor="name">Name</Label>
            <InputField id="name" name="name" placeholder="Enter name..." />
          </div>

          <div className="space-y-1">
            <Label htmlFor="visitCategory">Visit Category</Label>
            <Select
              id="visitCategory"
              name="visitCategory"
              placeholder="Select a visit category"
              options={VISIT_CATEGORIES}
            />
          </div>

          <div className="space-y-1">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
            />
          </div>

          {isEditingMode && (
            <div className="flex items-center gap-2">
              <Checkbox id="isActive" name="isActive" />
              <Label htmlFor="isActive">Active</Label>
            </div>
          )}

          <div className="flex flex-col justify-end gap-5 border-none pt-0 sm:flex-row">
            <CloseButton onClose={onClose} />
            <Button type="submit" variant="primary" isLoading={isSubmitting}>
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
