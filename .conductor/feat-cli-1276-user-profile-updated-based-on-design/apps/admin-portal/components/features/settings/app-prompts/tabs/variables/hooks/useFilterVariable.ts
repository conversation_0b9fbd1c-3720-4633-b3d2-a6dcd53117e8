import {
  parseAsBoolean,
  parseAsInteger,
  parseAsString,
  useQueryState,
} from "nuqs";

export const useFilterVariable = () => {
  const [page, setPage] = useQueryState("page", parseAsInteger);
  const [key, setKey] = useQueryState("key", parseAsString);
  const [label, setLabel] = useQueryState("label", parseAsString);
  const [type, setType] = useQueryState("type", parseAsString);
  const [computed, setComputed] = useQueryState("computed", parseAsBoolean);
  const [published, setPublished] = useQueryState("published", parseAsBoolean);
  const [isActive, setIsActive] = useQueryState("isActive", parseAsBoolean);
  const [isCacheable, setIsCacheable] = useQueryState(
    "isCacheable",
    parseAsBoolean,
  );

  return {
    key,
    setKey,
    label,
    setLabel,
    type,
    setType,
    computed,
    setComputed,
    published,
    setPublished,
    isActive,
    setIsActive,
    isCacheable,
    setIsCacheable,
    page,
    setPage,
  };
};
