import { ColumnDef } from "@tanstack/react-table";

import {
  TableEditButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { CodelistTemplate } from "@/lib/apis/codelist-templates/types";

type CodelistTemplateActions = {
  onEdit: (codelist: CodelistTemplate) => void;
  onDelete: (codelist: CodelistTemplate) => void;
};

export const generateCodelistTemplateColumns = (
  actions: CodelistTemplateActions,
): ColumnDef<CodelistTemplate>[] => [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <button
        onClick={() => actions.onEdit(row.original)}
        className="text-primary-500 text-left font-medium hover:underline"
      >
        {row.original.name}
      </button>
    ),
  },
  {
    accessorKey: "label",
    header: "Label",
    cell: ({ row }) => <span className="text-sm">{row.original.label}</span>,
  },
  {
    accessorKey: "templateIdentifier",
    header: "Identifier",
    cell: ({ row }) => (
      <span className="font-mono text-sm text-gray-700 dark:text-gray-300">
        {row.original.templateIdentifier}
      </span>
    ),
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <span className="text-sm text-gray-600">
        {row.original.description || "-"}
      </span>
    ),
  },
  {
    id: "terms",
    header: "Terms",
    cell: ({ row }) => <TermsCell terms={row.original.templateTerms} />,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const codelist = row.original;

      return (
        <div className="flex items-center gap-2">
          <TableEditButton
            type="button"
            onClick={() => actions.onEdit(codelist)}
          />
          <TableRemoveButton onClick={() => actions.onDelete(codelist)} />
        </div>
      );
    },
  },
];

const TermsCell = ({ terms }: { terms: CodelistTemplate["templateTerms"] }) => {
  if (!terms?.length) {
    return <span className="text-sm text-gray-500">No terms</span>;
  }

  const termsContent = (
    <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-lg dark:border-gray-600 dark:bg-gray-800">
      <div className="max-h-60 space-y-2 overflow-y-auto">
        {terms?.map((term) => (
          <div key={term.id} className="text-sm">
            <div className="flex items-center gap-2">
              <span className="rounded bg-gray-100 px-2 py-1 font-mono text-xs text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                {term.submissionValue}
              </span>
              <span className="text-gray-700 dark:text-gray-300">
                {term.displayLabel}
              </span>
            </div>
            {term.definition && (
              <div className="ml-2 mt-1 text-xs italic text-gray-500 dark:text-gray-400">
                {term.definition}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Dropdown>
      <DropdownTrigger>
        <div className="group inline-flex cursor-pointer items-center gap-1.5 rounded-full bg-blue-50 px-3 py-1.5 text-sm font-medium text-blue-700 transition-all hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-800/40">
          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-xs font-bold text-white">
            {terms.length}
          </div>
          <span>Terms</span>
        </div>
      </DropdownTrigger>
      <DropdownContent className="w-96">{termsContent}</DropdownContent>
    </Dropdown>
  );
};
