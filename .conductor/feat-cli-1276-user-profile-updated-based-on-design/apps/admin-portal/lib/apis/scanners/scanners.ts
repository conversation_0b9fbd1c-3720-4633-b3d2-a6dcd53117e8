import BaseApi from "../base";
import type { MetadataParams } from "../types";
// import type { MetadataParams } from "../scanner-applications/types";
import type {
  AddScannerPayload,
  AppVersionsBarResponse,
  FleetOverviewResponse,
  Scanner,
  ScannerDiagnosticsResponse,
  ScannersListResponse,
  ScannerVersionResponse,
  UpdateScannerPayload,
} from "./types";

export class ScannersApi extends BaseApi {
  public constructor() {
    super("/scanners", true);
  }

  // GET: Retrieve a list of scanners
  public async list(params: MetadataParams): Promise<ScannersListResponse> {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<ScannersListResponse>(`?${paramUrl}`);
  }

  // GET: Retrieve a single scanner by ID
  public async get(id: string): Promise<Scanner> {
    return this.http.get<Scanner>(`/${id}`);
  }

  // GET: Retrieve the version information for a scanner by deviceId
  public async getVersion(deviceId: string): Promise<ScannerVersionResponse> {
    return this.http.get<ScannerVersionResponse>(`/version/${deviceId}`);
  }

  // POST: Create a new scanner
  public async create(data: AddScannerPayload): Promise<Scanner> {
    return this.http.post<Scanner>("/", data);
  }

  // PUT: Update an existing scanner by ID
  public async update(
    id: string,
    data: UpdateScannerPayload,
  ): Promise<Scanner> {
    return this.http.put<Scanner>(`/${id}`, data);
  }

  // GET: Retrieve fleet overview with pagination
  public async getFleetOverview(
    params: MetadataParams = {},
  ): Promise<FleetOverviewResponse> {
    return this.http.get<FleetOverviewResponse>("/fleet-overview", params);
  }

  // GET: Retrieve app versions distribution for bar chart
  public async getAppVersionsBar(): Promise<AppVersionsBarResponse> {
    return this.http.get<AppVersionsBarResponse>("/app-versions-bar");
  }
  public async getScannerDiagnostics(id: string) {
    return this.http.get<ScannerDiagnosticsResponse>(`/${id}/diagnostics`);
  }
}
