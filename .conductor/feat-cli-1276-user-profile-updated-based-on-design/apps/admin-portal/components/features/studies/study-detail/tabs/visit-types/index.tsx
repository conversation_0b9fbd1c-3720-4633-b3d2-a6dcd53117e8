"use client";

import { Card, ToggleSwitch } from "flowbite-react";
import { parseAsBoolean, useQueryState } from "nuqs";
import { useMemo, useState } from "react";
import { HiPlus } from "react-icons/hi";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { But<PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { VisitType } from "@/lib/apis/visit-types";

import { getColumns } from "./columns";
import { useStudyVisitTypes } from "./hooks/use-visit-types-queries";
import { ModalVisitType } from "./modal-visit-type";

export const VisitTypesTab = () => {
  const { data, isPending, isPlaceholderData } = useStudyVisitTypes();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedVisitType, setSelectedVisitType] = useState<VisitType | null>(
    null,
  );
  const [isActive, setIsActive] = useQueryState("isActive", parseAsBoolean);

  const handleViewType = (visitType: VisitType) => {
    setSelectedVisitType(visitType);
    setIsModalOpen(true);
  };

  const columns = useMemo(() => getColumns(handleViewType), []);

  const handleAddType = () => {
    setSelectedVisitType(null);
    setIsModalOpen(true);
  };

  const handleToggleActive = (checked: boolean) => {
    setIsActive(checked || null);
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="flex flex-col justify-between gap-2 p-4 md:flex-row md:items-center">
          <h2 className="text-lg font-semibold dark:text-gray-400">
            Visit Types
          </h2>
          <div className="flex items-center justify-end gap-2 sm:gap-4">
            <div className="flex items-center gap-1 sm:gap-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Just Show Active
              </span>
              <ToggleSwitch
                checked={isActive === true}
                onChange={handleToggleActive}
              />
            </div>
            <Button
              variant="primary"
              className=" w-fit px-0 py-1.5"
              onClick={handleAddType}
            >
              <HiPlus />
              Add Visit Type
            </Button>
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            {data && <Table data={data.results} columns={columns} />}
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </Card>
      {isModalOpen && (
        <ModalVisitType
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          visitType={selectedVisitType}
        />
      )}
    </>
  );
};
