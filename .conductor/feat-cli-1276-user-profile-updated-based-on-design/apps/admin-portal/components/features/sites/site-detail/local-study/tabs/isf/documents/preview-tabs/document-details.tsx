import { Dropdown, ToggleSwitch } from "flowbite-react";
import { Pencil } from "lucide-react";
import { useState } from "react";
import { FaClipboardCheck } from "react-icons/fa6";
import { LuFileWarning } from "react-icons/lu";

import { ExpirationStatusBadge } from "@/components/ui/badges/document-expiration-status";
import {
  DOCUMENT_STATUSES,
  DocumentStatusBadge,
} from "@/components/ui/badges/ebinder-document-badge";
import { Button } from "@/components/ui/button";
// import {
//   DatePickerPanel,
//   DatePickerTrigger,
//   DatePickerUI,
// } from "@/components/ui/form/date-picker/date-picker-ui";
import { InlineEdit } from "@/components/ui/inline-edit";
import { useDisclosure } from "@/hooks/use-disclosure";
import { EBinderUpdatePlaceholderPayload } from "@/lib/apis/essential-document-files/types";
import { cn, formatDate } from "@/lib/utils";

import { useUpdateDocument } from "../../hooks/use-isf-mutations";
import { useDocument } from "../../hooks/use-isf-queries";
import { ArtifactModal } from "../../placeholder-modal";

type Props = {
  documentId: string;
};

const STATUS_BUTTON = [
  {
    key: "attention",
    activeClass: "!border-orange-500 !bg-transparent !text-orange-500",
    icon: <LuFileWarning size={24} />,
  },
  {
    key: "reviewed",
    activeClass: "!border-blue-500 !bg-transparent !text-blue-500",
    icon: <FaClipboardCheck size={24} />,
  },
] as const;

export const DocumentDetails = ({ documentId }: Props) => {
  const { close, isOpen, open } = useDisclosure();
  const [showAllDetails, setShowAllDetails] = useState(false);
  const { data: document } = useDocument(documentId);

  const { mutateAsync: updateDocument, isPending: isUpdating } =
    useUpdateDocument(documentId);

  const handleUpdateDocument = async (
    payload: Omit<EBinderUpdatePlaceholderPayload, "id">,
  ) => {
    await updateDocument({
      ...payload,
      id: documentId,
    });
  };

  return (
    <>
      <div className="flex flex-col gap-2.5 rounded-lg p-4 sm:p-6">
        <div className="space-y-2.5">
          <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center">
            <InlineEdit
              value={document?.title ?? ""}
              inputClassName="text-2xl font-semibold"
              viewModeClassName="text-2xl font-semibold"
              editIconClassName="text-2xl font-semibold min-w-fit size-8"
              className="order-2 w-fit sm:order-1"
              onSave={(value) => {
                handleUpdateDocument({
                  title: value,
                });
              }}
            />
            <ToggleSwitch
              className="order-1 ml-auto flex-shrink-0 sm:order-2 sm:ml-0"
              checked={showAllDetails}
              label="Show All Details"
              onChange={setShowAllDetails}
            />
          </div>

          <div className="flex flex-col justify-end gap-2 sm:flex-row sm:items-center">
            {STATUS_BUTTON.map((button) => (
              <Button
                key={button.key}
                variant="outline"
                className={cn(
                  "px-5 py-2.5 text-sm  font-medium capitalize leading-5 disabled:border-gray-400 disabled:text-gray-400 [&:disabled]:pointer-events-auto [&:disabled]:!cursor-not-allowed",
                  (document?.isfStatus?.name || document?.tmfStatus?.name) ===
                    button.key && button.activeClass,
                )}
                isLoading={isUpdating}
                onClick={() =>
                  handleUpdateDocument({
                    status: button.key,
                    processingStatus: document?.processingStatus ?? "completed",
                  })
                }
                disabled={
                  (document?.isfStatus?.name || document?.tmfStatus?.name) ===
                  button.key
                }
              >
                {button.icon}
                {button.key}
              </Button>
            ))}
          </div>
        </div>

        <table className=" space-y-5">
          <tr>
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Type
            </td>
            <td className="pb-3 text-sm uppercase sm:pb-5 dark:text-white">
              {document?.currentVersion?.fileRecord.extension}
            </td>
          </tr>
          <tr>
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Status
            </td>
            <td className="pb-3 sm:pb-5 dark:text-white">
              <Dropdown
                label={
                  <DocumentStatusBadge
                    status={
                      document?.isfStatus?.name ||
                      document?.tmfStatus?.name ||
                      "placeholder"
                    }
                  />
                }
                size="sm"
                className="!bg-white !p-0 dark:!bg-gray-500 [&>ul]:!list-none"
                dismissOnClick={true}
                inline
                disabled={isUpdating}
              >
                {DOCUMENT_STATUSES.map((status) => (
                  <Dropdown.Item
                    key={status}
                    onClick={() =>
                      handleUpdateDocument({
                        status,
                      })
                    }
                    className={cn(
                      "flex items-center capitalize",
                      (document?.isfStatus?.name ||
                        document?.tmfStatus?.name) === status && "bg-gray-100",
                    )}
                  >
                    <DocumentStatusBadge status={status} />
                  </Dropdown.Item>
                ))}
              </Dropdown>
            </td>
          </tr>
          <tr className="group">
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Zone name
            </td>
            <td className="flex gap-x-2 pb-3 text-sm sm:pb-5 dark:text-white">
              {document?.category?.zoneName}
              <Pencil
                className="ml-2 size-4 cursor-pointer text-gray-400 opacity-0 transition-opacity group-hover:opacity-100"
                onClick={open}
              />
            </td>
          </tr>
          <tr className="group">
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Section name
            </td>
            <td className="flex gap-x-2 pb-3 text-sm sm:pb-5 dark:text-white">
              {document?.category?.sectionName}
              <Pencil
                className="ml-2 size-4 cursor-pointer text-gray-400 opacity-0 transition-opacity group-hover:opacity-100"
                onClick={open}
              />
            </td>
          </tr>
          <tr className="group">
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Artifact name
            </td>
            <td className="flex gap-x-2 pb-3 text-sm sm:pb-5 dark:text-white">
              {document?.category?.artifactName}
              <Pencil
                className="ml-2 size-4 cursor-pointer text-gray-400 opacity-0 transition-opacity group-hover:opacity-100"
                onClick={open}
              />
            </td>
          </tr>
          <tr>
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Artifact Number
            </td>
            <td className="pb-3 text-sm sm:pb-5 dark:text-white">
              {document?.category?.artifactNumber}
            </td>
          </tr>
          <tr>
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Description
            </td>
            <td className="pb-3 text-sm sm:pb-5">
              {" "}
              <InlineEdit
                className="w-full"
                viewModeClassName="w-fit"
                value={document?.description || ""}
                multiline
                onSave={async (value) => {
                  await handleUpdateDocument({
                    description: value,
                  });
                }}
              />
            </td>
          </tr>
          <tr>
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Created Date
            </td>
            <td className="pb-3 text-sm sm:pb-5 dark:text-white">
              {document?.createdDate &&
                formatDate(document?.createdDate, "LLL dd, yyyy")}
            </td>
          </tr>
          <tr>
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Version Number
            </td>
            <td className="pb-3 text-sm sm:pb-5 dark:text-white">
              {document?.currentVersion?.versionNumber?.join(".")}
            </td>
          </tr>
          <tr>
            <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
              Latest Version Date
            </td>
            <td className="pb-3 text-sm sm:pb-5 dark:text-white">
              {document?.currentVersion?.createdDate &&
                formatDate(
                  document?.currentVersion?.createdDate,
                  "LLL dd, yyyy",
                )}
            </td>
          </tr>

          {showAllDetails && (
            <>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  Expiration Date
                </td>
                <td className="pb-3 text-sm sm:pb-5">
                  {/* May 22, 2025 */}
                  {/* <DatePickerUI
                    defaultValue={document?.expiryDate}
                    disableClearBtn
                    onSave={async (date) => {
                      await handleUpdateDocument({
                        expiryDate: date?.toISOString() ?? null,
                      });
                    }}
                  >
                    <DatePickerTrigger className="group flex w-fit cursor-pointer">
                      <span className="dark:text-white">
                        {document?.expiryDate &&
                          formatDate(document?.expiryDate, "LLL dd, yyyy")}
                      </span>
                      <Pencil className="ml-2 size-4 text-gray-400 opacity-0 transition-opacity group-hover:opacity-100" />
                    </DatePickerTrigger>
                    <DatePickerPanel />
                  </DatePickerUI> */}
                </td>
              </tr>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  Expiration Status
                </td>
                <td className="pb-3 text-sm sm:pb-5">
                  <ExpirationStatusBadge
                    expirationDate={document?.expiryDate}
                  />
                </td>
              </tr>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  Document ID
                </td>
                <td className="pb-3 text-sm sm:pb-5 dark:text-white">
                  {document?.id}
                </td>
              </tr>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  Index
                </td>
                <td className="pb-3 text-sm sm:pb-5 dark:text-white">
                  {document?.folderPath}
                </td>
              </tr>
            </>
          )}
        </table>
      </div>

      {isOpen && document && (
        <ArtifactModal
          isOpen={isOpen}
          onClose={close}
          selectedFile={document}
        />
      )}
    </>
  );
};

// export const SaveContent = () => (
//   <div className="flex flex-col gap-5">
//     <div className="flex">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">Type</div>
//       <div className="w-2/3 text-sm uppercase">
//         {/* {document?.currentVersion?.extension} */}
//         CSV
//       </div>
//     </div>

//     <div className="flex items-center">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">Status</div>
//       <div className="w-2/3">
//         Expired
//         {/* <Dropdown
//           label={<EbinderDocumentStatusBadge variant={statusName} />}
//           size="sm"
//           className="!bg-white !p-0 [&>ul]:!list-none"
//           dismissOnClick={true}
//           inline
//           disabled={
//             (!can("update:own", "isf") && isNot("site")) ||
//             isUpdatingDocument
//           }
//         >
//           {validStates.map((status) => (
//             <Dropdown.Item
//               key={status}
//               onClick={() =>
//                 handleStatusChange(status as EbinderDocumentStatus)
//               }
//               className={cn(
//                 "flex items-center capitalize",
//                 statusName === status && "bg-gray-100",
//               )}
//             >
//               <EbinderDocumentStatusBadge
//                 variant={status as EbinderDocumentStatus}
//               />
//             </Dropdown.Item>
//           ))}
//         </Dropdown> */}
//       </div>
//     </div>

//     <div className="flex">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">Zone</div>
//       <div className="group flex w-2/3 text-sm">
//         <span>
//           {/* {document?.category?.zone} */}
//           Zone name
//         </span>
//         <Pencil
//           className="ml-2 size-4 cursor-pointer text-gray-400 opacity-0 transition-opacity group-hover:opacity-100"
//           // onClick={() => setShowUpdateArtifact(true)}
//         />
//       </div>
//     </div>

//     <div className="flex">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">Section</div>
//       <div className="group flex w-2/3 text-sm">
//         <span>
//           {/* {document?.category?.section} */}
//           Section Name
//         </span>

//         <Pencil
//           className="ml-2 size-4 cursor-pointer text-gray-400 opacity-0 transition-opacity group-hover:opacity-100"
//           // onClick={() => setShowUpdateArtifact(true)}
//         />
//       </div>
//     </div>

//     <div className="flex">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">
//         Artifact Name
//       </div>
//       <div className="group flex w-2/3 text-sm">
//         <span>{document?.title}</span>
//         <Pencil
//           className="ml-2 size-4 cursor-pointer text-gray-400 opacity-0 transition-opacity group-hover:opacity-100"
//           // onClick={() => setShowUpdateArtifact(true)}
//         />
//       </div>
//     </div>

//     <div className="flex">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">
//         Artifact Number
//       </div>
//       <div className="w-2/3 text-sm">
//         {/* {document?.category?.artifactNumber} */}
//         123
//       </div>
//     </div>

//     <div className="flex">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">Description</div>
//       <div className="w-2/3 text-sm">
//         <InlineEdit
//           className=""
//           viewModeClassName="w-fit"
//           // value={document?.description}
//           multiline
//           // onSave={async (value) => {
//           //   await handleInlineEditSubmit({
//           //     description: value,
//           //   });
//           // }}
//         />
//       </div>
//     </div>

//     <div className="flex">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">
//         Created Date
//       </div>
//       <div className="flex w-2/3 text-sm">
//         {/* {document?.createdDate && formatDate(document?.createdDate)} */}
//         May 22, 2025
//       </div>
//     </div>

//     <div className="flex">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">
//         Version Number
//       </div>
//       <div className="w-2/3 text-sm">
//         {/* {document?.currentVersion?.versionNumber?.join(".")} */}
//         123
//       </div>
//     </div>

//     <div className="flex">
//       <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">
//         Latest Version Date
//       </div>
//       <div className="w-2/3 text-sm">
//         {/* {document?.currentVersion?.createdDate &&
//           formatDate(document?.currentVersion?.createdDate)} */}
//         May 22, 2025
//       </div>
//     </div>

//     <>
//       <div className="flex">
//         <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">
//           Expiration Date
//         </div>
//         <div className="w-2/3 text-sm">
//           May 22, 2025
//           {/* <DatePickerUI
//               defaultValue={document?.expiryDate}
//               disableClearBtn
//               onSave={async (date) => {
//                 await handleInlineEditSubmit({
//                   expiryDate: date?.toISOString() ?? null,
//                 });
//               }}
//             >
//               <DatePickerTrigger className="group flex w-fit cursor-pointer">
//                 <span>
//                   {document?.expiryDate &&
//                     formatDate(document?.expiryDate)}
//                 </span>
//                 <Pencil className="ml-2 size-4 text-gray-400 opacity-0 transition-opacity group-hover:opacity-100" />
//               </DatePickerTrigger>
//               <DatePickerPanel />
//             </DatePickerUI> */}
//         </div>
//       </div>

//       <div className="flex">
//         <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">
//           Expiration Status
//         </div>
//         <div className="w-2/3 text-sm">
//           {/* {document?.expiryDate && ( */}
//           <ExpirationStatusBadge expirationDate={new Date()} />
//           {/* )} */}
//         </div>
//       </div>

//       <div className="flex">
//         <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">
//           Document ID
//         </div>
//         <div className="w-2/3 text-sm">Id</div>
//       </div>

//       <div className="flex">
//         <div className="w-1/3 text-sm font-medium text-gray-700 dark:text-gray-300">Index</div>
//         <div
//           className="w-2/3 truncate text-sm"
//           // title={document?.folderPath}
//         >
//           {/* {document?.folderPath} */}
//           Path
//         </div>
//       </div>
//     </>
//   </div>
// );
