import { DragEndEvent } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import { Card } from "flowbite-react/components/Card";
import { get } from "lodash";
import { useParams } from "next/navigation";
import { useMemo, useState } from "react";
import { CSVLink } from "react-csv";
import { CiExport, CiImport } from "react-icons/ci";
import { IoMdAdd } from "react-icons/io";

import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Procedure } from "@/lib/apis/procedures";
import { downloadBlob, generateEmptyCsv } from "@/lib/utils";

import { useStudy } from "../../../hooks/use-studies-queries";
import { getProcedureColumns } from "./columns";
import { useReorderProcedures } from "./hooks/use-procedures-mutations";
import { useProcedures } from "./hooks/use-procedures-queries";
import { ImportProceduresModal } from "./import-procedures-modal";
import { ModalProcedure } from "./modal-procedure";

export const ProceduresTab = () => {
  const studyId = useParams().id as string;
  const { data: study } = useStudy(studyId);

  const { data, isPending } = useProcedures(studyId);
  const { mutateAsync: reorderProcedures } = useReorderProcedures(studyId);

  const { close, isOpen, open } = useDisclosure();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedProcedure, setSelectedProcedure] = useState<Procedure | null>(
    null,
  );

  const exportCSVData = useMemo(() => {
    if (!data?.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Name", key: "name" },
      { label: "Description", key: "description" },
      { label: "Study Name", key: "study.name" },
      { label: "Study Id", key: "studyId" },
      { label: "Active", key: "isActive" },
      { label: "Created at", key: "createdDate" },
      { label: "Last updated at", key: "lastUpdatedDate" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((procedure) =>
        headers.map((header) => get(procedure, header.key)),
      ),
    ];
  }, [data]);

  const handleViewProcedure = (procedure: Procedure) => {
    setSelectedProcedure(procedure);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedProcedure(null);
    setIsModalOpen(false);
  };

  const handleDownloadTemplate = () => {
    const csv = generateEmptyCsv(["Name", "Description"]);
    downloadBlob(new Blob([csv]), "import-template.csv");
  };

  const handleDragEnd = (event: DragEndEvent) => {
    if (!event.over || event.active.id === event.over.id) return;
    const activeIndex = data?.results.findIndex(
      (item) => item.id === event.active.id,
    );
    const overIndex = data?.results.findIndex(
      (item) => item.id === event.over?.id,
    );
    const newOrderedProcedures = arrayMove(
      data?.results ?? [],
      activeIndex ?? 0,
      overIndex ?? 0,
    );

    reorderProcedures({
      studyId,
      newOrderedProcedures,
      orderedIds: newOrderedProcedures.map((item) => item.id),
    });
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex flex-col justify-between gap-4 p-4 pb-0 text-lg font-semibold sm:flex-row sm:items-center">
          <div className="dark:text-gray-400">Procedures</div>
          <div className="flex flex-1 flex-col gap-2 sm:flex-row sm:items-center sm:justify-end">
            <Button variant="primary" onClick={handleDownloadTemplate}>
              <CiImport size={18} />
              Download Import Template
            </Button>
            <CSVLink
              data={exportCSVData}
              filename={
                study?.name ? `${study.name}-procedures.csv` : "procedures.csv"
              }
            >
              <Button className="w-full" variant="primary">
                <CiExport size={18} />
                Export
              </Button>
            </CSVLink>
            <Button variant="primary" onClick={open}>
              <CiImport size={18} />
              Import
            </Button>
            <Button variant="primary" onClick={() => setIsModalOpen(true)}>
              <IoMdAdd size={18} />
              Add Procedure
            </Button>
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={getProcedureColumns(handleViewProcedure)} />
        ) : (
          <>
            <Table
              data={data?.results ?? []}
              draggable={{
                enable: true,
                handleDragEnd,
              }}
              columns={getProcedureColumns(handleViewProcedure)}
            />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>

      {isModalOpen && (
        <ModalProcedure
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          procedure={selectedProcedure}
        />
      )}
      {isOpen && <ImportProceduresModal isOpen={isOpen} onClose={close} />}
    </>
  );
};
