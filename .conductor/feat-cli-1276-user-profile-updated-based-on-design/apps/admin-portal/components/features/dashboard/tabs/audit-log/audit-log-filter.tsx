"use client";

import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, FormRef } from "@/components/ui/form";
import { DateRangePicker } from "@/components/ui/form/date-picker/date-range-picker";
import { Label } from "@/components/ui/form/label";
import { MultiSelect } from "@/components/ui/form/multi-select";
import { LazySelect } from "@/components/ui/lazy-select";
import { useInfiniteUsers } from "@/hooks/queries/use-infinite-users";
import { cn } from "@/lib/utils";

import { useAuditLogFilters } from "./hooks/use-audit-log-filters";
import {
  useAuditLogActions,
  useAuditLogResourceTypes,
} from "./hooks/use-audit-log-queries";

const filterSchema = z.object({
  userId: z.string().optional(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
  actions: z.array(z.string()).optional(),
  resourceTypes: z.array(z.string()).optional(),
});

type FilterFormValues = z.infer<typeof filterSchema>;

export const AuditLogFilter = () => {
  const [open, setOpen] = useState(false);
  const { userId, fromDate, toDate, actions, resourceTypes } =
    useAuditLogFilters();

  const filters = [
    !!userId,
    !!fromDate || !!toDate,
    !!(actions && actions.length > 0),
    !!(resourceTypes && resourceTypes.length > 0),
  ];
  const totalFilters = filters.filter(Boolean).length;

  return (
    <Dropdown open={open} onOpenChange={setOpen} placement="bottom-end">
      <DropdownTrigger>
        <Button variant="outline" className="relative !py-2">
          <ListFilter className="size-5" />
          {!!totalFilters && (
            <i className="bg-primary-600 absolute right-0 top-0 block size-5 rounded-full px-1 not-italic text-white md:right-4">
              {totalFilters}
            </i>
          )}
        </Button>
      </DropdownTrigger>
      <DropdownContent className="z-30 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <FilterContent setOpen={setOpen} />
      </DropdownContent>
    </Dropdown>
  );
};

const FilterContent = ({ setOpen }: { setOpen: (open: boolean) => void }) => {
  const ref = useRef<FormRef<typeof filterSchema>>(null);

  const {
    userId,
    fromDate,
    toDate,
    actions,
    resourceTypes,
    setUserId,
    setFromDate,
    setToDate,
    setActions,
    setResourceTypes,
    goToPage,
    clearAllFilters,
  } = useAuditLogFilters();

  const { data: actionsData } = useAuditLogActions();
  const { data: resourceTypesData } = useAuditLogResourceTypes();

  function onSubmit(data: FilterFormValues) {
    setUserId(data.userId || null);
    setFromDate(
      data.dateRange?.from ? data.dateRange.from.toLocaleDateString() : null,
    );
    setToDate(
      data.dateRange?.to ? data.dateRange.to.toLocaleDateString() : null,
    );
    setActions(data.actions && data.actions.length > 0 ? data.actions : null);
    setResourceTypes(
      data.resourceTypes && data.resourceTypes.length > 0
        ? data.resourceTypes
        : null,
    );
    goToPage(1);
    setOpen(false);
  }

  function onClear() {
    clearAllFilters();
    ref.current?.formHandler?.reset();
    setOpen(false);
  }

  const defaultValues = {
    userId: userId || "",
    dateRange: {
      from: fromDate ? new Date(fromDate) : undefined,
      to: toDate ? new Date(toDate) : undefined,
    },
    actions: actions || [],
    resourceTypes: resourceTypes || [],
  };

  const hasFilters =
    !!userId ||
    !!fromDate ||
    !!toDate ||
    !!(actions && actions.length > 0) ||
    !!(resourceTypes && resourceTypes.length > 0);

  return (
    <Form
      schema={filterSchema}
      mode="onChange"
      onSubmit={onSubmit}
      className="w-80 py-2 md:w-96 "
      ref={ref}
      defaultValues={defaultValues}
    >
      <div className="divide-y dark:divide-gray-500">
        <div className="px-3 py-2">
          <Label
            htmlFor="userId"
            className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
          >
            User
          </Label>
          <LazySelect
            name="userId"
            id="userId"
            searchPlaceholder="Search user..."
            useInfiniteQuery={useInfiniteUsers}
            getOptionLabel={(user) => `${user.firstName} ${user.lastName}`}
            getOptionValue={(user) => user.id}
            placeholder="Select user"
          />
        </div>
        <div className=" px-3 py-2">
          <Label
            htmlFor="resourceTypes"
            className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
          >
            Resource Types
          </Label>
          <MultiSelect
            name="resourceTypes"
            placeholder="Select resource types"
            options={resourceTypesData?.map((resourceType) => ({
              label: resourceType.title,
              value: resourceType.key,
            }))}
            searchable
            searchPlaceholder="Search resource types..."
          />
        </div>
        <div className=" px-3 py-2">
          <Label
            htmlFor="actions"
            className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
          >
            Actions
          </Label>
          <MultiSelect
            name="actions"
            placeholder="Select actions"
            options={actionsData?.map((action) => ({
              label: action.title,
              value: action.key,
            }))}
            searchable
            searchPlaceholder="Search actions..."
          />
        </div>

        <div className=" px-3 py-2">
          <Label
            htmlFor="dateRange"
            className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
          >
            Date Range
          </Label>
          <DateRangePicker
            name="dateRange"
            placeholder="Select date range"
            maxDate={new Date()}
          />
        </div>
      </div>

      <div className="flex justify-end gap-5 px-3  py-2">
        <Button
          variant="outline"
          className={cn(
            "w-full justify-center",
            !hasFilters && "pointer-events-none invisible",
          )}
          onClick={onClear}
          type="button"
        >
          Clear Filters
        </Button>
        <Button
          variant="primary"
          type="submit"
          className="w-full justify-center"
        >
          Apply Filters
        </Button>
      </div>
    </Form>
  );
};
