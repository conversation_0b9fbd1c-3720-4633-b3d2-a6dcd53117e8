import { useInfiniteQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";

import api from "@/lib/apis";
import type { CodelistStatus } from "@/lib/apis/codelist-definitions/types";

export const useInfiniteStudyCodelists = (
  search: string,
  status?: CodelistStatus,
  initialPageSize = 50,
) => {
  const { id: studyId } = useParams<{ id: string }>();

  return useInfiniteQuery({
    queryKey: ["infinite-codelists", studyId, search, status],
    queryFn: ({ pageParam = 1 }) =>
      api.codeListDefinition.list(studyId!, {
        page: pageParam,
        take: initialPageSize,
        filter: {
          name: search,
          ...(status && { status }),
        },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
    enabled: !!studyId,
  });
};
