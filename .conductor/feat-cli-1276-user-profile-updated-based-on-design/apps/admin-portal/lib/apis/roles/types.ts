import { RoleType } from "@/components/features/roles/default/role-modal";

import { ListBaseResponse } from "../types";

export type SubjectIdsParams = {
  subId: string;
  roleId: string;
};

export type Role = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  name: string;
  description: string;
  isActive: boolean;
  type: RoleType;
};
export type RoleWithExtendType = Omit<Role, "type"> & {
  type: RoleType | "admin";
};

export type ResponseRoleDetail = Role & {
  permissions: Permission[];
};

export type RoleListResponse = ListBaseResponse<Role>;

export type CreateRolePayload = {
  name: string;
  description: string;
  type: RoleType;
};

export type UpdateRolePayload = CreateRolePayload & {
  isActive: boolean;
  id: string;
};

export type RoleSubject = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  name: string;
  description: null | string;
};

export type SubjectListResponse = ListBaseResponse<RoleSubject>;

export type Permission = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  action: string;
  description: string;
  permissionSubject: RoleSubject;
  isActive: boolean;
  isAdmin: boolean;
};

export type PermissionListResponse = ListBaseResponse<Permission>;

export type FormattedRoleData = {
  selectedPermission: Permission[];
  allPermissions: Permission[];
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  name: string;
  description: null | string;
  type: RoleType;
};

export type PayloadAddPermissionToRole = {
  roleId: string;
  permissionId: string;
};

export type UpdatePermissionSubjectPayload = {
  id: string;
  description?: string;
};

export type UpdatePermissionPayload = {
  id: string;
  description?: string;
};
