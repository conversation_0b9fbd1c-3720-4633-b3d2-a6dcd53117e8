import { ROLE_MESSAGE_TYPES } from "@/components/features/settings/app-prompts/tabs/templates/message-modal";
import { PROMPT_TYPES } from "@/components/features/settings/app-prompts/tabs/templates/prompt-modal";

import type { ListBaseResponse } from "../types";
import { MODEL_PROVIDERS } from "./../../../components/features/settings/app-prompts/tabs/templates/prompt-modal";
type Message = {
  text: string;
};

type ChatHistory = {
  role: (typeof ROLE_MESSAGE_TYPES)[number];
  parts: Message[];
  isCacheable: boolean;
};
export type AddPromptPayload = {
  name: string;
  description?: string;
  modelProvider: (typeof MODEL_PROVIDERS)[number]["value"];
  model: string;
  key: (typeof PROMPT_TYPES)[number]["value"];
  templateContent: ChatHistory[];
  generationConfig?: {
    topK?: number;
    topP?: number;
    temperature?: number;
  };
  promptVariables?: Record<
    string,
    {
      type: string;
      computed: boolean;
      isCacheable: boolean;
    }
  >;
};

export type UpdatePromptPayload = AddPromptPayload & {
  id: string;
};

export type Prompt = AddPromptPayload & {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  isActive: boolean;
  version: number;
};

export type PromptListResponse = ListBaseResponse<Prompt>;
