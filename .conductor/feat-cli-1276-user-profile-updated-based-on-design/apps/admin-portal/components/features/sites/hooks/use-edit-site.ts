import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateSitePayload } from "@/lib/apis/sites/types";

import { USE_SITE_QUERY_KEY } from "./use-site";

export const useEditSite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: UpdateSitePayload & { id: string }) => {
      const { id, ...data } = payload;
      return api.sites.update(id, data);
    },
    onSuccess: (_, { id }) => {
      toast.success("Site updated successfully");
      queryClient.invalidateQueries({ queryKey: [USE_SITE_QUERY_KEY, id] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
