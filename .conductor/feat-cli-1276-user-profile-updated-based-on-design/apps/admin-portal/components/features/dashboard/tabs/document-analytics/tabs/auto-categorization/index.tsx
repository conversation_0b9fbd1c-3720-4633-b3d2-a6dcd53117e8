import React from "react";

import { AiPerformanceOverview } from "./ai-performance";
import { AiSuccessRateTrend } from "./ai-success-rate-trend";
import { AutoOverviewCategorizationFilters } from "./auto-overview-categorization-filters";
import { CategorizationMethodDistribution } from "./categorization-method-distribution";
import { CategorizationMethodTrends } from "./categorization-method-trends";

export const AutoCategorizationTab = () => {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-xl font-bold text-gray-900 sm:text-2xl dark:text-white">
          Auto Categorization
        </h1>
        <AutoOverviewCategorizationFilters />
      </div>

      <CategorizationMethodDistribution />

      <CategorizationMethodTrends />

      <AiPerformanceOverview />

      <AiSuccessRateTrend />
    </div>
  );
};
