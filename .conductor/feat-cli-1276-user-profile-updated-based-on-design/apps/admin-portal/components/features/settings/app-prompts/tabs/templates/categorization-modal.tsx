import { useMutation } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import { File as FileIcon } from "lucide-react";
import React, { useRef, useState } from "react";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormActions } from "@/components/ui/form";
import { FileDropzone } from "@/components/ui/form/file-drop-zone";
import { Label } from "@/components/ui/form/label";
import { Radio } from "@/components/ui/form/radio";
import { LazySelect } from "@/components/ui/lazy-select";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Table } from "@/components/ui/table";
import { useInfiniteCategoryVersion } from "@/hooks/queries/use-infinite-category-versions";
import { useDisclosure } from "@/hooks/use-disclosure";
import api from "@/lib/apis";
import {
  AutoCategorizePayload,
  AutoCategorizeResponse,
} from "@/lib/apis/artifact-categories";
import { ALLOWED_FILE_TYPES } from "@/lib/constants";

const CATEGORIZATION_TYPES = ["isf", "tmf"] as const;

const schema = z.object({
  artifactCategoryVersionId: z
    .string({
      invalid_type_error: "Category version is required",
      required_error: "Category version is required",
    })
    .min(1, "Category version is required"),
  files: z.array(z.instanceof(File)).min(1, "File is required"),
  type: z.string().default("isf"),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

type CategorizedDocument = AutoCategorizeResponse[string] & {
  name: string;
};

export const CategorizationModal = ({ isOpen, onClose }: Props) => {
  const formRef = useRef<FormActions<z.ZodType<z.infer<typeof schema>>>>(null);

  const { close, isOpen: isOpenDocument, open } = useDisclosure();
  const [categorizedDocument, setCategorizedDocument] = useState<
    CategorizedDocument[]
  >([]);
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (payload: AutoCategorizePayload) =>
      api.artifactCategories.autoCategorize(payload),
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    const res = await mutateAsync({
      artifactCategoryVersionId: data.artifactCategoryVersionId,
      filenames: data.files.map((file) => file.name),
      isTMF: data.type === "tmf",
    });
    const formattedData = Object.entries(res).map((entry) => ({
      name: entry[0],
      ...entry[1],
    })) as CategorizedDocument[];
    setCategorizedDocument(formattedData);
    open();
    // formRef.current?.formHandler.reset();
  };

  return (
    <>
      <WrapperModal
        isOpen={isOpen}
        onClose={onClose}
        title="Auto Categorization"
      >
        <Form
          defaultValues={{
            artifactCategoryVersionId: "",
            files: [],
            type: "isf",
          }}
          ref={formRef}
          schema={schema}
          onSubmit={onSubmit}
          className="space-y-4"
        >
          <div className="space-y-2">
            <Label htmlFor="files">Files</Label>
            <FileDropzone
              multiple
              name="files"
              acceptTypes={ALLOWED_FILE_TYPES}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="artifactCategoryVersionId">Category Version</Label>
            <LazySelect
              id="artifactCategoryVersionId"
              name="artifactCategoryVersionId"
              placeholder="Select a version..."
              useInfiniteQuery={useInfiniteCategoryVersion}
              getOptionLabel={(option) => option.version.toString()}
              getOptionValue={(option) => option.id}
            />
          </div>
          <div className="flex items-center gap-4">
            {CATEGORIZATION_TYPES.map((type) => (
              <div key={type} className="flex items-center gap-2">
                <Label htmlFor={type} className="uppercase">
                  {type}
                </Label>
                <Radio name="type" id={type} value={type} />
              </div>
            ))}
          </div>
          <div className="mt-4 flex flex-col justify-end gap-5 border-none pt-4 sm:flex-row">
            <CloseButton onClose={onClose} />
            <Button type="submit" variant="primary" isLoading={isPending}>
              Save
            </Button>
          </div>
        </Form>
      </WrapperModal>
      <WrapperModal
        isOpen={isOpenDocument}
        onClose={close}
        size="7xl"
        title="Categorized Document"
      >
        <Table columns={columns} data={categorizedDocument} />
      </WrapperModal>
    </>
  );
};

const columns: ColumnDef<CategorizedDocument>[] = [
  {
    header: "Document Name",
    accessorKey: "file.name",
    cell: ({ row }) => {
      const fileName = row.original?.name;

      if (!fileName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return (
        <div className="flex items-center gap-2">
          <FileIcon className="size-6 flex-shrink-0 text-blue-500" />
          <span className="text-xs font-bold">{fileName}</span>
        </div>
      );
    },
  },
  {
    header: "ISF Zone Name",
    accessorKey: "category.zoneName",
    cell: ({ row }) => {
      const zoneName = row.original?.category?.isfZoneName;

      if (!zoneName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{zoneName}</span>;
    },
  },
  {
    header: "ISF Section Name",
    accessorKey: "category.sectionName",
    cell: ({ row }) => {
      const sectionName = row.original?.category?.isfSectionName;

      if (!sectionName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{sectionName}</span>;
    },
  },
  {
    header: "ISF Record Group Name",
    accessorKey: "category.artifactName",
    cell: ({ row }) => {
      const artifactName = row.original?.category?.isfRecordGroupName;

      if (!artifactName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{artifactName}</span>;
    },
  },
  {
    header: "TMF Zone Name",
    accessorKey: "category.zoneName",
    cell: ({ row }) => {
      const zoneName = row.original?.category?.tmfZoneName;

      if (!zoneName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{zoneName}</span>;
    },
  },
  {
    header: "TMF Section Name",
    accessorKey: "category.sectionName",
    cell: ({ row }) => {
      const sectionName = row.original?.category?.tmfSectionName;

      if (!sectionName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{sectionName}</span>;
    },
  },
  {
    header: "TMF Record Group Name",
    accessorKey: "category.artifactName",
    cell: ({ row }) => {
      const artifactName = row.original?.category?.tmfRecordGroupName;

      if (!artifactName)
        return <span className="text-xs font-normal text-gray-500">N/A</span>;

      return <span className="text-xs font-normal">{artifactName}</span>;
    },
  },
];
