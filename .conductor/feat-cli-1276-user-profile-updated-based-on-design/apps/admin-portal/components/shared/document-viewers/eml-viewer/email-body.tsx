import { useEffect, useRef } from "react";

import { EmailContent } from ".";

interface EmailBodyProps {
  emailContent: EmailContent;
}

export const EmailBody = ({ emailContent }: EmailBodyProps) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (iframeRef.current && emailContent.html) {
      const iframe = iframeRef.current;

      // Wait for iframe to load before accessing its document
      const handleLoad = () => {
        const iframeDocument =
          iframe.contentDocument || iframe.contentWindow?.document;

        if (iframeDocument) {
          // Create HTML content with styles
          const htmlContent = `
            <!DOCTYPE html>
            <html>
              <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
              </head>
              <body class="dark:text-white">
                ${emailContent.html}
              </body>
            </html>
          `;

          try {
            iframeDocument.open();
            // Need to cast to any to avoid TypeScript warning about deprecated signature
            (iframeDocument as any).write(htmlContent);
            iframeDocument.close();

            // Apply dark mode class if needed
            const isDarkMode =
              document.documentElement.classList.contains("dark");
            if (isDarkMode && iframeDocument.documentElement) {
              iframeDocument.documentElement.classList.add("dark");
            }

            // Adjust iframe height to match content
            const updateHeight = () => {
              if (iframe.contentWindow?.document.body) {
                const height = iframe.contentWindow.document.body.scrollHeight;
                iframe.style.height = `${height}px`;
              }
            };

            // Initial height update
            updateHeight();

            // Set up observer for dynamic content changes
            const resizeObserver = new ResizeObserver(updateHeight);

            if (iframe.contentWindow?.document.body) {
              resizeObserver.observe(iframe.contentWindow.document.body);
            }

            // Clean up observer on unmount
            return () => {
              resizeObserver.disconnect();
            };
          } catch (error) {
            console.error("Error writing to iframe document:", error);
          }
        }
      };

      // Set up load event handler
      iframe.addEventListener("load", handleLoad);

      // If iframe is already loaded, call handler directly
      if (iframe.contentDocument?.readyState === "complete") {
        handleLoad();
      }

      return () => {
        iframe.removeEventListener("load", handleLoad);
      };
    }
  }, [emailContent.html]);

  return (
    <div className="email-body rounded-md border border-gray-100 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
      {emailContent.html ? (
        <iframe
          ref={iframeRef}
          title="Email content"
          className="w-full border-0 bg-transparent"
          sandbox="allow-same-origin"
          style={{ minHeight: "100px" }} // Ensure iframe has a minimum height before content loads
        />
      ) : emailContent.text ? (
        <div className="whitespace-pre-wrap text-gray-800 dark:text-gray-200">
          {emailContent.text}
        </div>
      ) : (
        <div className="italic text-gray-500 dark:text-gray-400">
          No content available
        </div>
      )}
    </div>
  );
};
