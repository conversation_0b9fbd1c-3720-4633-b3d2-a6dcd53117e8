import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateVisitSchedulePayload } from "@/lib/apis/visit-schedules";

import { USE_VISIT_SCHEDULE_QUERY_KEY } from "./use-visit-schedule";

export const useUpdateVisitSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateVisitSchedulePayload & { id: string }) => {
      const { id, ...rest } = payload;
      return api.visitSchedules.update(id, rest);
    },
    onSuccess: (_, variables) => {
      toast.success("Visit schedule updated successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_VISIT_SCHEDULE_QUERY_KEY, variables.id],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
