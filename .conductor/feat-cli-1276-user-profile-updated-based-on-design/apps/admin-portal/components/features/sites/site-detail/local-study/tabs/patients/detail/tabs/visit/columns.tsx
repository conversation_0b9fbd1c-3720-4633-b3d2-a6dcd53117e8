"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { Tooltip } from "flowbite-react";
import { TriangleAlert } from "lucide-react";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { VisitStatusBadge } from "@/components/ui/badges/visit-status-badge";
import type { Visit } from "@/lib/apis/patient-visits";
import { formatDate } from "@/lib/utils";

type Props = {
  onView: (data: Visit) => void;
};

export function generateColumnsVisit({ onView }: Props): ColumnDef<Visit>[] {
  const columns: ColumnDef<Visit>[] = [
    {
      header: "Visit Name",
      accessorKey: "name",
      cell: ({ row }) => {
        return (
          <button
            onClick={() => onView(row.original)}
            className="text-primary-500 cursor-pointer gap-1 whitespace-nowrap font-medium hover:underline"
          >
            {row.original.name}
          </button>
        );
      },
    },
    {
      header: "Visit Type",
      accessorKey: "visitType",
      cell: ({ row }) => {
        const value = row.original.visitType?.name as string;
        return <PillBadge>{value}</PillBadge>;
      },
    },
    {
      header: "Study Arm",
      accessorKey: "studyArm",
      cell: ({ row }) => {
        const value = row.original.studyArm?.name as string;
        return value || <span className="text-gray-400">N/A</span>;
      },
    },
    {
      header: "Expected Visit Day",
      accessorKey: "visitDay",
      cell: ({ row }) => {
        const data = row.original;

        return data.visitDay ? (
          `Day ${data.visitDay} (${data.visitWindowStart ?? 0} / ${data.visitWindowStart ?? 0})`
        ) : (
          <span className="text-gray-400">N/A</span>
        );
      },
    },

    {
      header: "Visit Date",
      accessorKey: "visitDate",
      id: "visit_Date",
      cell: ({ row }) => {
        const visit = row.original;

        if (!visit.visitDate) {
          return "N/A";
        }

        if (
          (visit.status.name === "Pending" ||
            visit.status.name === "Scheduled") &&
          new Date(visit.visitDate) < new Date()
        ) {
          return (
            <Tooltip content="Visit scheduled outside of protocol-defined visit window">
              <div className="flex cursor-pointer items-center gap-1 whitespace-nowrap text-red-500">
                <span>{formatDate(visit.visitDate)}</span>
                <TriangleAlert size={18} />
              </div>
            </Tooltip>
          );
        }

        return (
          <span className="whitespace-nowrap">
            {formatDate(visit.visitDate)}
          </span>
        );
      },
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }) => {
        const value = row.original.status?.name;
        return <VisitStatusBadge status={value} />;
      },
    },
    {
      header: "Actions",
      cell: function Cell({ row }) {
        return (
          <div className="flex gap-2">
            <TableViewButton
              type="button"
              onClick={() => {
                onView(row.original);
              }}
            />
          </div>
        );
      },
    },
  ];

  return columns;
}
