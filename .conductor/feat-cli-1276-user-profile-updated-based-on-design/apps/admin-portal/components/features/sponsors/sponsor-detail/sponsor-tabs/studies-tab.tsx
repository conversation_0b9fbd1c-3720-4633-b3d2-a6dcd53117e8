import { Card } from "flowbite-react";
import { useParams } from "next/navigation";

import { Table, TableLoading } from "@/components/ui/table";

import { useSponsorStudies } from "../hooks/use-sponsor-studies";
import { columns } from "./columns/studies";

export const StudiesTab = () => {
  const params = useParams();
  const { data, isPending } = useSponsorStudies(params.id as string);

  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 p-4 pb-0 text-lg font-semibold dark:text-gray-400">
        Studies
      </div>
      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <Table columns={columns} data={data?.results ?? []} />
      )}
    </Card>
  );
};
