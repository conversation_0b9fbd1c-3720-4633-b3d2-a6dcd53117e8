import { useQuery } from "@tanstack/react-query";

import { useSort } from "@/hooks/use-sort";
import api from "@/lib/apis";
import { MetadataParams, OrderDirection } from "@/lib/apis/types";

import { useFilterArtifactCategories } from "./use-filter-artifact-categories";

export const artifactCategoryKeys = {
  all: () => ["artifact-categories"] as const,

  allList: () => [...artifactCategoryKeys.all(), "list"] as const,
  list: (params: MetadataParams) =>
    [...artifactCategoryKeys.allList(), params] as const,
};

const orderArtifactCategories = {
  type: (orderDirection: OrderDirection) => [
    {
      orderBy: "tmfRefModel",
      orderDirection,
    },
    {
      orderBy: "isfRefModel",
      orderDirection,
    },
  ],
  documentNumber: (orderDirection: OrderDirection) => [
    {
      orderBy: "tmfZoneNumber",
      orderDirection,
    },
    {
      orderBy: "tmfSectionNumber",
      orderDirection,
    },
    {
      orderBy: "tmfRecordGroupNumber",
      orderDirection,
    },
    {
      orderBy: "isfZoneNumber",
      orderDirection,
    },
    {
      orderBy: "isfSectionNumber",
      orderDirection,
    },
    {
      orderBy: "isfRecordGroupNumber",
      orderDirection,
    },
  ],
  recordGroup: (orderDirection: OrderDirection) => [
    {
      orderBy: "tmfZoneName",
      orderDirection,
    },
    {
      orderBy: "tmfSectionName",
      orderDirection,
    },
    {
      orderBy: "tmfRecordGroupName",
      orderDirection,
    },
    {
      orderBy: "isfZoneName",
      orderDirection,
    },
    {
      orderBy: "isfSectionName",
      orderDirection,
    },
    {
      orderBy: "isfRecordGroupName",
      orderDirection,
    },
  ],
  recordType: (orderDirection: OrderDirection) => [
    {
      orderBy: "recordType",
      orderDirection,
    },
  ],
};

export const useLatestVersionArtifactCategories = () => {
  const { orderBy, orderDirection } = useSort();
  const {
    tmfZoneName,
    tmfSectionName,
    tmfRecordGroupName,
    isfZoneName,
    isfSectionName,
    isfRecordGroupName,
    recordType,
    alternativeNames,
    isISF,
    isTMF,
    version,
    isActive,
    versionLatest,
    requiresSignature,
    expires,
    inspectableRecord,
    includesPHI,
    page,
    take,
  } = useFilterArtifactCategories();

  const params = {
    page: page || undefined,
    take: take || 50,
    filter: {
      tmfZoneName: tmfZoneName || undefined,
      tmfSectionName: tmfSectionName || undefined,
      tmfRecordGroupName: tmfRecordGroupName || undefined,
      isfZoneName: isfZoneName || undefined,
      isfSectionName: isfSectionName || undefined,
      isfRecordGroupName: isfRecordGroupName || undefined,
      recordType: recordType || undefined,
      alternativeNames: alternativeNames || undefined,
      isTMF: typeof isTMF === "boolean" ? isTMF : undefined,
      isISF: typeof isISF === "boolean" ? isISF : undefined,
      version: version || undefined,
      isActive: typeof isActive === "boolean" ? isActive : undefined,
      versionLatest: versionLatest || undefined,
      requiresSignature:
        typeof requiresSignature === "boolean" ? requiresSignature : undefined,
      expires: typeof expires === "boolean" ? expires : undefined,
      inspectableRecord:
        typeof inspectableRecord === "boolean" ? inspectableRecord : undefined,
      includesPHI: typeof includesPHI === "boolean" ? includesPHI : undefined,
      orders: orderBy
        ? orderArtifactCategories[
            orderBy as keyof typeof orderArtifactCategories
          ]?.((orderDirection?.toUpperCase() as OrderDirection) ?? "DESC")
        : undefined,
    },
  };

  return useQuery({
    queryKey: artifactCategoryKeys.list(params),
    queryFn: () =>
      api.artifactCategories.listLatestVersions({
        ...params,
      }),
    placeholderData: (prevData) => prevData,
  });
};
