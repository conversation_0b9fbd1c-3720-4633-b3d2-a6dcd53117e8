import BaseApi from "../base";
import type { UpdateProtocolVisitSchedulePayload } from "./types";

class ProtocolVisitsApi extends BaseApi {
  constructor() {
    super("/protocol-visits", true);
  }

  public async updateVisitSchedule(
    payload: UpdateProtocolVisitSchedulePayload,
  ): Promise<void> {
    return this.http.put(`/${payload.id}`, payload);
  }
}

export const protocolVisits = new ProtocolVisitsApi();
export * from "./types";
