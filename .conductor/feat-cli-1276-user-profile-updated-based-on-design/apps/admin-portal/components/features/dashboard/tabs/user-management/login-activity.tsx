"use client";

import { Card, useThemeMode } from "flowbite-react";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Skeleton } from "@/components/ui/skeleton";

import {
  calculateDateRange,
  DateOptions,
  DateRangeFilter,
} from "../components";
import { useLoginActivities } from "./hooks/use-user-management-queries";

export const LoginActivity = () => {
  const { mode } = useThemeMode();

  const [selectedDateFilter, setSelectedDateFilter] = useState<
    DateOptions | ""
  >("today");

  const [selectedRange, setSelectedRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });

  const {
    data: loginActivities,
    isPending: isLoadingLoginActivities,
    isSuccess,
  } = useLoginActivities(calculateDateRange(selectedDateFilter, selectedRange));

  const handleFilterChange = (
    filter: DateOptions | "",
    customRange?: DateRange,
  ) => {
    setSelectedDateFilter(filter);
    if (filter === "custom" && customRange) {
      setSelectedRange(customRange);
    }
    if (!filter) {
      setSelectedRange({ from: undefined, to: undefined });
    }
  };

  return (
    <div>
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Login Activity
        </h3>
      </div>

      {isLoadingLoginActivities ? (
        <LoginActivitySkeleton />
      ) : (
        <Card className="[&>div]:p-4 sm:[&>div]:p-6">
          <div className="mb-4 flex justify-end">
            <DateRangeFilter
              selectedFilter={selectedDateFilter}
              onFilterChange={handleFilterChange}
              selectedRange={selectedRange}
            />
          </div>
          <div className="h-80">
            {!loginActivities?.length && isSuccess ? (
              <NoLoginActivity />
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={loginActivities}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{
                      fontSize: 12,
                    }}
                    stroke={mode === "dark" ? "#fff" : "#000"}
                    interval={"preserveStartEnd"}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                      });
                    }}
                  />
                  <YAxis
                    tick={{
                      fontSize: 12,
                    }}
                    stroke={mode === "dark" ? "#fff" : "#000"}
                    allowDecimals={false}
                  />
                  <Tooltip
                    labelFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString("en-US", {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      });
                    }}
                    formatter={(value) => [
                      `${value} ${+value === 1 ? "login" : "logins"}`,
                      "Login Count",
                    ]}
                  />
                  <Line
                    type="monotone"
                    dataKey="loginCount"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: "#3b82f6", strokeWidth: 2 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};

const LoginActivitySkeleton = () => (
  <Card>
    <div className="mb-4 flex justify-end">
      <Skeleton className="h-[42px] w-40" />
    </div>
    <Skeleton className="h-80 w-full" />
  </Card>
);

const NoLoginActivity = () => (
  <div className="grid h-full place-content-center">
    <p className="text-center text-gray-500 dark:text-gray-400">
      No login activity data available for the selected time period
    </p>
  </div>
);
