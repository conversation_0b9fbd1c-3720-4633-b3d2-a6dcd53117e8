import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const cohortKeys = {
  all: () => ["cohorts"] as const,
  allLists: (studyId: string) =>
    [...cohortKeys.all(), studyId, "list"] as const,
  list: (studyId: string, params?: MetadataParams) =>
    [...cohortKeys.allLists(studyId), params] as const,
};

export const useCohorts = (studyId: string) => {
  const { page } = usePagination();

  const params = {
    page,
  };

  return useQuery({
    queryKey: cohortKeys.list(studyId, params),
    queryFn: () => api.cohorts.getCohorts(studyId, params),
    placeholderData: (prevData) => prevData,
  });
};
