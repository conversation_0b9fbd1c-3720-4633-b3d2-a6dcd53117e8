replicaCount: ${REPLICAS}

image:
  repository: ${IMAGE_REPOSITORY}
  tag: ${VERSION}
  pullPolicy: IfNotPresent

service:
  type: NodePort
  port: 80
  containerPort: 3000

resources:
  limits:
    cpu: ${CPU}m
    memory: ${RAM}Mi
  requests:
    cpu: ${CPU}m
    memory: ${RAM}Mi

livenessProbe:
  httpGet:
    path: /
    port: 3000
    scheme: HTTP
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 5

readinessProbe:
  httpGet:
    path: /
    port: 3000
    scheme: HTTP
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 5

startupProbe:
  httpGet:
    path: /
    port: 3000
  failureThreshold: 30
  periodSeconds: 10
