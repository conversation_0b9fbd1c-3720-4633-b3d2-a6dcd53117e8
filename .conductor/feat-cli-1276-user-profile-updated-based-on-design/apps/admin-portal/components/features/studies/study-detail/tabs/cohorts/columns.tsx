import { ColumnDef } from "@tanstack/react-table";
import { MdOutlineEdit } from "react-icons/md";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import { CohortItem } from "@/lib/apis/cohorts";

type Params = {
  onEdit: (cohort: CohortItem) => void;
};

export const generateCohortColumns = ({
  onEdit,
}: Params): ColumnDef<CohortItem>[] => [
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <span
        onClick={() => onEdit(row.original)}
        role="button"
        className="text-primary-500 hover:underline"
      >
        {row.getValue("name")}
      </span>
    ),
  },
  {
    header: "Description",
    accessorKey: "description",
  },
  {
    accessorKey: "isActive",
    header: "Active",
    cell: ({ row }) => {
      const isActive = row.getValue("isActive");
      if (isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      return (
        <div className="flex gap-4">
          <button
            onClick={() => {
              onEdit({
                ...row.original,
              });
            }}
            className="flex items-center gap-1 text-xs text-blue-500 hover:text-blue-600 disabled:cursor-not-allowed disabled:text-gray-300"
          >
            <MdOutlineEdit className="h-4 w-4" />
            Edit
          </button>
        </div>
      );
    },
  },
];
