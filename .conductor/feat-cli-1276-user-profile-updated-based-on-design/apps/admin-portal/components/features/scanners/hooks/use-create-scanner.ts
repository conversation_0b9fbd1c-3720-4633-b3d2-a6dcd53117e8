import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddScannerPayload } from "@/lib/apis/scanners";

import { USE_SCANNERS_QUERY_KEY } from "./use-scanners";

export const useAddScanner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AddScannerPayload) => api.scanners.create(data),
    onSuccess: () => {
      toast.success("Site added successfully");
      queryClient.invalidateQueries({ queryKey: [USE_SCANNERS_QUERY_KEY] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
