import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddUserToGroupPayload } from "@/lib/apis/groups/types";

import { USE_GROUP_PROFILES_QUERY_KEY } from "./use-group-profiles";

export function useAddProfileToGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: AddUserToGroupPayload) =>
      api.groups.addProfileToGroup(payload),
    onSuccess: (_, { groupId }) => {
      toast.success("Profile added to group successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_GROUP_PROFILES_QUERY_KEY, groupId],
      });
    },
  });
}
