import { useParams } from "next/navigation";
import type { z } from "zod";

import { Form } from "@/components/ui/form";
import { Modal } from "@/components/ui/modal";
import type { Site } from "@/lib/apis/sites/types";

import { addSiteSchema, SiteForm } from "../default/modal-add-site";
import { useEditSite } from "../hooks/use-edit-site";

type ModalEditSiteProps = {
  isOpen: boolean;
  onClose: () => void;
  site?: Site;
};

const editSiteSchema = addSiteSchema.partial();

export const ModalEditSite = ({
  isOpen,
  onClose,
  site,
}: ModalEditSiteProps) => {
  const params = useParams();
  const siteId = params.id as string;
  const { mutateAsync: editSite, isPending: isEditing } = useEditSite();

  async function onSubmit(data: z.infer<typeof editSiteSchema>) {
    await editSite({ id: siteId, ...data });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Edit Site</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={editSiteSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
          defaultValues={{
            ...site,
            address: {
              ...site?.address,
              stateProvinceId: site?.address?.stateProvince.id,
              countryId: site?.address?.country.id,
            },
          }}
        >
          <SiteForm onClose={onClose} isSubmitting={isEditing} />
        </Form>
      </Modal.Body>
    </Modal>
  );
};
