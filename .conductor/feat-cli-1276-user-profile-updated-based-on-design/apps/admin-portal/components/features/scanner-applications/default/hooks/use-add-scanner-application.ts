import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddScannerApplicationPayload } from "@/lib/apis/scanner-applications";

export const useAddScannerApplication = () => {
  return useMutation({
    mutationFn: async (data: AddScannerApplicationPayload) => {
      return api.scannerApplications.create(data);
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
