import React from "react";

import { cn } from "@/lib/utils";

type FolderPlaceholderProps = {
  className?: string;
};

export const FolderPlaceholder: React.FC<FolderPlaceholderProps> = ({
  className,
}) => {
  return (
    <div
      className={cn(
        "h-11 w-full rounded-lg border-2 border-dashed border-purple-300 bg-purple-50 px-3 py-2 transition-all",
        className,
      )}
    >
      <div className="flex items-center gap-2.5">
        <div className="flex items-center">
          <div className="flex w-5 items-center">
            {/* Folder icon placeholder */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-purple-300"
            >
              <path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};
