import { useParams } from "next/navigation";

import { ContactCard } from "@/components/shared/contacts/contact-card";

import { useSponsorContacts } from "../../hooks/use-contacts";
import { AddContactCard } from "./add-contact";

export const ContactsTab = () => {
  const params = useParams();
  const sponsorId = params.id as string;
  const { data: contacts, isLoading: isLoadingContacts } =
    useSponsorContacts(sponsorId);

  if (isLoadingContacts) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <ContactCard key={index} contact={undefined} isLoading={true} />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
      {contacts?.results?.map((contact) => (
        <ContactCard key={contact.id} contact={contact} />
      ))}
      {!isLoadingContacts && <AddContactCard />}
    </div>
  );
};
