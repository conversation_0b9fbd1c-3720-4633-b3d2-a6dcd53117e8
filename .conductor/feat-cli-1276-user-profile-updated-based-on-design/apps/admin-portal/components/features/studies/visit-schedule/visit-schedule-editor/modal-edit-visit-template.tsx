import { useParams } from "next/navigation";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import type { VisitTemplate } from "@/lib/apis/visit-schedules";

import { useUpdateVisitTemplate } from "../hooks/use-update-visit-template";

const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
});

export type EditVisitTemplatePayload = z.infer<typeof schema>;

type ModalEditVisitTemplateProps = {
  isOpen: boolean;
  onClose: () => void;
  defaultValues: VisitTemplate;
};

export const ModalEditVisitTemplate = function ({
  isOpen,
  onClose,
  defaultValues,
}: ModalEditVisitTemplateProps) {
  const params = useParams();
  const visitScheduleId = params.visitId as string;
  const { mutateAsync: updateVisitTemplate, isPending: isUpdating } =
    useUpdateVisitTemplate();

  async function onSubmit(data: EditVisitTemplatePayload) {
    await updateVisitTemplate({
      visitTemplateId: defaultValues.id,
      id: visitScheduleId,
      ...data,
    });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>Edit Visit</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          formProps={{ shouldFocusError: false }}
          defaultValues={defaultValues}
          onSubmit={onSubmit}
        >
          <div className="grid grid-cols-1 gap-6">
            <div className="col-span-1 flex flex-col gap-2">
              <Label htmlFor="name">Name</Label>
              <InputField id="name" name="name" placeholder="Enter name..." />
            </div>
          </div>

          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button type="submit" variant="primary" isLoading={isUpdating}>
              Save Changes
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
