import type { ColumnDef } from "@tanstack/react-table";
import { MdDelete } from "react-icons/md";

import { ActivityProcedure } from "@/lib/apis/activities/types";

export const generateActivityProceduresColumns = (
  isPublished?: boolean,
  onRemove?: (id: string) => void,
) => {
  const columns: ColumnDef<ActivityProcedure>[] = [
    { header: "Name", accessorKey: "name" },
    {
      header: "Description",
      accessorKey: "description",
    },
    ...(onRemove
      ? [
          {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => {
              return (
                <div className="flex gap-2">
                  <button
                    type="button"
                    disabled={isPublished}
                    className="flex items-center gap-1 text-red-500"
                    onClick={() => onRemove(row.original.id)}
                  >
                    <MdDelete className="h-4 w-4" />
                    Remove
                  </button>
                </div>
              );
            },
          } as ColumnDef<ActivityProcedure>,
        ]
      : []),
  ];

  return columns;
};
