import { Plus } from "lucide-react";
import { useParams } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Document } from "@/lib/apis/essential-document-files/types";
import { Folder } from "@/lib/apis/isf-folders";

import { useDeleteFolder } from "../hooks/use-isf-mutations";
import { useFolders } from "../hooks/use-isf-queries";
import { FolderModal } from "./folder-modals";
import { FolderNode } from "./folder-node";

type Props = {
  draggingFolder: Folder | null;
  draggingFile: Document | null;
  isMovingFolder: boolean;
  isMovingDocument: boolean;
  activePath: string;
  setActivePath: React.Dispatch<React.SetStateAction<string>>;
};

export const FolderColumn = ({
  draggingFolder,
  isMovingFolder,
  draggingFile,
  isMovingDocument,
  activePath,
  setActivePath,
}: Props) => {
  const params = useParams();
  const siteId = params.id as string;
  const studyId = params.studyId as string;
  const [folderId, setFolderId] = useQueryState("folderId", parseAsString);
  const {
    data: folders,
    isLoading: isLoadingFolder,
    isSuccess,
  } = useFolders(studyId, siteId);
  const [selectedFolder, setSelectedFolder] = useState<Folder | null>(null);
  const { mutate: deleteFolder, isPending: isDeleting } = useDeleteFolder({
    siteId,
    studyId,
  });

  const {
    isOpen: isFolderModalOpen,
    open: onFolderModalOpen,
    close: onFolderModalClose,
  } = useDisclosure();

  const handleOnDelete = (folder: Folder) => {
    if (folder.fileCount) {
      return toast.error("Can not delete a folder that contains files");
    }
    deleteFolder(folder.id);
  };

  const handleEdit = (data: Folder) => {
    setSelectedFolder(data);
    onFolderModalOpen();
  };

  useEffect(() => {
    if (folderId) return;
    if (isSuccess) {
      const isExistFolder = !!folders?.[0]?.id;

      setFolderId(isExistFolder ? folders[0].id : null);
      setActivePath(isExistFolder ? `/${folders[0].name}` : "");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [folders, isSuccess, setFolderId]);

  return (
    <>
      <div className="flex w-full flex-col overflow-hidden rounded-lg border border-gray-300 md:h-full dark:border-gray-500 [&>div:last-child]:flex-1">
        <div className="p-5 pb-2.5">
          <Button
            className="[&>span]:!px-0"
            variant="outline"
            onClick={onFolderModalOpen}
          >
            <Plus className="size-4" />
            <span className="min-w-0 truncate">New Folder</span>
          </Button>
        </div>

        {isLoadingFolder ? (
          <FolderSkeleton />
        ) : (
          <LoadingWrapper
            isLoading={isMovingFolder || isMovingDocument || isDeleting}
          >
            <div className="overflow-hidden">
              {folders?.map((folder) => (
                <FolderNode
                  key={folder.id}
                  folder={folder}
                  level={0}
                  draggingFolder={draggingFolder}
                  draggingFile={draggingFile}
                  path={`/${folder.name}`}
                  activePath={activePath}
                  setActivePath={setActivePath}
                  onDelete={handleOnDelete}
                  onEdit={handleEdit}
                  rootPath={`/${folder.name}`}
                />
              ))}
            </div>
          </LoadingWrapper>
        )}
      </div>
      <FolderModal
        selectedFolder={selectedFolder}
        isOpen={isFolderModalOpen}
        onClose={() => {
          onFolderModalClose();
          setSelectedFolder(null);
        }}
      />
    </>
  );
};

const FolderSkeleton = () => (
  <div className="flex-1">
    {[1, 2, 3, 4].map((i) => (
      <div key={i} className="py-2 pl-4 pr-3">
        <Skeleton className="h-7 w-full " />
      </div>
    ))}
  </div>
);
