import { Tabs, TabsItem } from "@/components/ui/tabs";
import type { <PERSON>anner } from "@/lib/apis/scanners/types";

import { ScannerApplicationsTab } from "./scanner-applications-tab";
import { ScannerModelsTab } from "./scanner-models-tab";

type ScannerTabsProps = {
  scanner: Scanner | undefined;
};

export const ScannerTabs = ({ scanner }: ScannerTabsProps) => {
  return (
    <div>
      <Tabs>
        <TabsItem title="Scanner Model">
          <ScannerModelsTab modelId={scanner?.model?.id} />
        </TabsItem>
        <TabsItem title="Scanner Applications">
          <ScannerApplicationsTab
            currentVersionId={scanner?.currentVersion?.id}
            targetVersionId={scanner?.targetVersion?.id}
          />
        </TabsItem>
      </Tabs>
    </div>
  );
};
