import { Search } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useC<PERSON><PERSON><PERSON>, useFormContext } from "react-hook-form";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useInfiniteFields } from "@/hooks/queries/use-infinite-fields";
import { useDebounce } from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

type Field = {
  id: string;
  fieldName: string;
  displayLabel: string;
  fieldType: string;
  [key: string]: any;
};

type Props = {
  isOpen: boolean;
  onClose: () => void;
  columnIndex: number;
};

const FieldSkeleton = () => (
  <div className="flex items-center justify-between border-b border-gray-100 p-4 last:border-b-0">
    <div className="flex-1">
      <Skeleton className="h-4 w-3/4" />
    </div>
    <Skeleton className="ml-3 h-6 w-16" />
  </div>
);

export const FieldSelectionModal = ({
  isOpen,
  onClose,
  columnIndex,
}: Props) => {
  const { control } = useFormContext();

  const { field: controllerField } = useController({
    control,
    name: `config.columns.${columnIndex}.field`,
  });

  const [search, setSearch] = useState("");
  const [selectedFieldState, setSelectedFieldState] = useState<Field | null>(
    null,
  );
  const debouncedSearch = useDebounce(search);
  const observerTarget = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isPending } =
    useInfiniteFields(debouncedSearch, "PUBLISHED");

  const allFields = useMemo(() => {
    return data?.pages.flatMap((page) => page.results) || [];
  }, [data]);

  // Setup IntersectionObserver for infinite scrolling
  useEffect(() => {
    if (!isOpen) return;

    const timeoutId = setTimeout(() => {
      const scrollContainer = scrollContainerRef.current;
      const target = observerTarget.current;

      if (!scrollContainer || !target) return;

      const observer = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (
            entry.isIntersecting &&
            !isFetchingNextPage &&
            hasNextPage &&
            !debouncedSearch
          ) {
            fetchNextPage();
          }
        },
        {
          root: scrollContainer,
          rootMargin: "0px",
          threshold: 0.1,
        },
      );

      observer.observe(target);

      return () => {
        observer.disconnect();
      };
    }, 300);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, debouncedSearch, isOpen]);

  const handleFieldClick = (field: Field) => {
    setSelectedFieldState(field);
  };

  const handleSave = () => {
    if (selectedFieldState) {
      // Store the entire field object using useController's onChange
      controllerField.onChange(selectedFieldState);

      onClose();
    }
  };

  return (
    <WrapperModal isOpen={true} onClose={onClose} title="Select Field Variable">
      <div className="space-y-4">
        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search fields..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full rounded-lg border border-gray-300 bg-white py-3 pl-10 pr-4 text-sm shadow-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
          />
        </div>

        {/* Fields List */}
        <div
          ref={scrollContainerRef}
          className="max-h-96 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-sm"
        >
          {isPending ? (
            // Initial loading skeletons
            <>
              <FieldSkeleton />
              <FieldSkeleton />
              <FieldSkeleton />
            </>
          ) : allFields.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-gray-500">
              <Search className="mb-2 h-8 w-8 text-gray-400" />
              <p className="text-sm font-medium">No fields found</p>
              <p className="mt-1 text-xs text-gray-400">
                Try adjusting your search terms
              </p>
            </div>
          ) : (
            <>
              {allFields.map((field) => {
                const isSelected =
                  selectedFieldState?.id === field.id ||
                  (controllerField.value?.id === field.id &&
                    !selectedFieldState);

                return (
                  <div
                    key={field.id}
                    onClick={() => handleFieldClick(field)}
                    className={cn(
                      "group flex cursor-pointer items-center gap-3 p-3 transition-colors hover:bg-gray-50",
                      isSelected && "bg-blue-50",
                    )}
                  >
                    {/* Radio button style indicator */}
                    <div
                      className={cn(
                        "flex h-4 w-4 items-center justify-center rounded-full border-2 transition-colors",
                        isSelected
                          ? "border-blue-600 bg-blue-600"
                          : "border-gray-300 group-hover:border-gray-400",
                      )}
                    >
                      {isSelected && (
                        <div className="h-1.5 w-1.5 rounded-full bg-white" />
                      )}
                    </div>

                    {/* Field info */}
                    <div className="min-w-0 flex-1">
                      <div className="truncate text-sm font-medium text-gray-900">
                        {field.displayLabel}
                      </div>
                    </div>

                    {/* Field type badge */}
                    <div className="rounded bg-gray-100 px-2 py-1 font-mono text-xs text-gray-500">
                      {field.fieldType}
                    </div>
                  </div>
                );
              })}

              {isFetchingNextPage && (
                <>
                  <FieldSkeleton />
                  <FieldSkeleton />
                  <FieldSkeleton />
                </>
              )}

              {/* Observer target for infinite scroll */}
              {hasNextPage && !debouncedSearch && (
                <div ref={observerTarget} className="h-1" />
              )}
            </>
          )}
        </div>
      </div>

      <div className="mt-6 flex flex-col justify-end gap-4 sm:flex-row">
        <CloseButton onClose={onClose} />
        <Button
          variant="primary"
          onClick={handleSave}
          disabled={!selectedFieldState}
        >
          Save
        </Button>
      </div>
    </WrapperModal>
  );
};
