import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { GrStatusGood } from "react-icons/gr";
import { MdBlock } from "react-icons/md";

import {
  TableEditButton,
  TableGenericButton,
} from "@/components/shared/table-action-buttons";
import { ActiveStatusBadge } from "@/components/ui/badges";
import type { GroupProfile } from "@/lib/apis/groups/types";

export const generateColumns = (
  onToggleStatus: (profile: GroupProfile) => void,
) => {
  const columns: ColumnDef<GroupProfile>[] = [
    {
      accessorKey: "user",
      header: "User",
      cell: ({ row }) => {
        const profile = row.original.profile;

        return (
          <Link
            className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
            href={`/users/${profile?.userId}`}
          >
            {profile?.user.firstName} {profile?.user.lastName}
          </Link>
        );
      },
    },
    {
      accessorKey: "profile.name",
      header: "Profile Name",
    },
    {
      accessorKey: "profile.isActive",
      header: "Status",
      cell: ({ row }) => (
        <ActiveStatusBadge isActive={row.original.profile.isActive} />
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const profile = row.original;
        const isActive = profile.profile.isActive;

        return (
          <div className="flex gap-2">
            <TableGenericButton
              type="button"
              onClick={() => onToggleStatus(profile)}
              className={
                isActive
                  ? "text-red-500 hover:text-red-600"
                  : "text-green-500 hover:text-green-600"
              }
            >
              {isActive ? "Disable" : "Enable"}
              {isActive ? <MdBlock /> : <GrStatusGood />}
            </TableGenericButton>
          </div>
        );
      },
    },
  ];

  return columns;
};
