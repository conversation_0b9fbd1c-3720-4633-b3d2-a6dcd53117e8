import { Card } from "flowbite-react";
import React from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { Table, TableLoading } from "@/components/ui/table";

import { columns } from "./columns";

export const FormsTab = () => {
  const isPending = false;
  const isPlaceholderData = false;
  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="mb-4 flex items-center justify-between p-4 pb-0">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Forms Templates
        </h3>
        <Button variant="primary">
          <IoMdAdd className="h-4 w-4" /> New Form
        </Button>
      </div>

      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table columns={columns} data={[]} />
        </LoadingWrapper>
      )}
    </Card>
  );
};
