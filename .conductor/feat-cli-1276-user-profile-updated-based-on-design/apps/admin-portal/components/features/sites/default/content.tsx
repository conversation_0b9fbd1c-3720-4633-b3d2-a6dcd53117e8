"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { Site } from "@/lib/apis/sites";

import { useSites } from "../hooks/use-sites";
import { columns } from "./columns";
import { ModalAddSite } from "./modal-add-site";

const BREADCRUMB_ITEMS = [{ label: "Sites" }];

const SitesPageContent = () => {
  const { data, isPending } = useSites();
  const [isAddSiteModalOpen, setIsAddSiteModalOpen] = useState(false);

  const exportCSVData = useMemo(() => {
    if (!data || !data.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Name", key: "name" },
      { label: "Status", key: "status" },
      { label: "City", key: "address.city" },
      { label: "State / Province", key: "address.stateProvince.name" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((site) => [
        ...headers.slice(0, -2).map((header) => {
          if (header.key === "status") {
            return site.isActive ? "Active" : "Inactive";
          }
          return site[header.key as keyof Site];
        }),
        site.address?.city,
        site.address?.stateProvince?.name,
      ]),
    ];
  }, [data]);

  return (
    <>
      <div className="space-y-4">
        <Breadcrumb items={BREADCRUMB_ITEMS} />
        <PageHeader>Sites</PageHeader>
        <Card className="[&>div]:p-0">
          <HeaderActions
            data={exportCSVData}
            buttonText="Add Site"
            filename="sites.csv"
            onButtonClick={() => setIsAddSiteModalOpen(true)}
          />
          {isPending ? (
            <TableLoading columns={columns} />
          ) : (
            <>
              <Table data={data?.results ?? []} columns={columns} />
              {data?.metadata && (
                <TableDataPagination metadata={data.metadata} />
              )}
            </>
          )}
        </Card>
      </div>
      <ModalAddSite
        isOpen={isAddSiteModalOpen}
        onClose={() => setIsAddSiteModalOpen(false)}
      />
    </>
  );
};

export default SitesPageContent;
