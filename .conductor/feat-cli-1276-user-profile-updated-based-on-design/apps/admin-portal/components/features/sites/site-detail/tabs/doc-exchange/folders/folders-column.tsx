import { FolderIcon, Plus } from "lucide-react";
import { useParams } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";
import React, { useEffect, useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useDisclosure } from "@/hooks/use-disclosure";
import {
  DocExchangeDocument,
  DocExchangeFolder,
} from "@/lib/apis/doc-exchange/types";

import {
  useDeleteFolder,
  useUnarchiveFolder,
} from "../hooks/use-doc-exchange-mutations";
import { useFolders } from "../hooks/use-doc-exchange-queries";
import { FolderModal } from "./folder-modals";
import { FolderNode } from "./folder-node";

type Props = {
  draggingFolder: DocExchangeFolder | null;
  draggingFile: DocExchangeDocument | null;
  isMovingFolder: boolean;
  isMovingDocument: boolean;
  activePath: string;
  setActivePath: React.Dispatch<React.SetStateAction<string>>;
};

export const FolderColumn = ({
  draggingFolder,
  isMovingFolder,
  draggingFile,
  isMovingDocument,
  activePath,
  setActivePath,
}: Props) => {
  const params = useParams();
  const siteId = params.id as string;
  const [folderId, setFolderId] = useQueryState("folderId", parseAsString);
  const {
    data: folders,
    isLoading: isLoadingFolder,
    isSuccess,
  } = useFolders(siteId);
  const [selectedFolder, setSelectedFolder] =
    useState<DocExchangeFolder | null>(null);
  const {
    mutateAsync: deleteFolder,
    isPending: isDeleting,
    isSuccess: isDeleted,
    reset,
  } = useDeleteFolder(siteId);
  const { mutateAsync: unDeleteFolder, isPending: isUnDeleting } =
    useUnarchiveFolder(siteId);
  const {
    isOpen: isFolderModalOpen,
    open: onFolderModalOpen,
    close: onFolderModalClose,
  } = useDisclosure();

  const {
    isOpen: isConfirmModalOpen,
    open: onConfirmModalOpen,
    close: onConfirmModalClose,
  } = useDisclosure();

  const handleOnDelete = async (folder: DocExchangeFolder) => {
    if (!folder.fileCount) {
      await deleteFolder({
        id: folder.id,
      });
      return reset();
    }

    onConfirmModalOpen();
    setSelectedFolder(folder);
  };

  const handleUnDelete = async () => {
    if (!selectedFolder) return;
    await unDeleteFolder(selectedFolder.id);
    handleCloseConfirm();
  };

  const handleEdit = (data: DocExchangeFolder) => {
    setSelectedFolder(data);
    onFolderModalOpen();
  };

  const handleCloseConfirm = () => {
    onConfirmModalClose();
    setSelectedFolder(null);
    reset();
  };

  const handleConfirm = async () => {
    if (!selectedFolder) return;
    await deleteFolder({
      id: selectedFolder.id,
      isHideToast: true,
    });
    // handleCloseConfirm();
  };

  useEffect(() => {
    if (folderId) return;
    if (isSuccess) {
      const isExistFolder = !!folders?.[0]?.id;

      setFolderId(isExistFolder ? folders[0].id : null);
      setActivePath(isExistFolder ? `/${folders[0].name}` : "");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [folders, isSuccess, setFolderId]);

  return (
    <>
      <div className="flex w-full flex-col overflow-hidden rounded-lg border border-gray-300 pb-2 md:h-full dark:border-gray-500 [&>div:last-child]:flex-1">
        <div className="p-5 pb-2.5">
          <Button
            className="[&>span]:!px-0"
            variant="outline"
            onClick={onFolderModalOpen}
          >
            <Plus className="size-4" />
            <span className="min-w-0 truncate">New Folder</span>
          </Button>
        </div>

        {isLoadingFolder ? (
          <FolderSkeleton />
        ) : (
          <LoadingWrapper
            isLoading={isMovingFolder || isMovingDocument || isDeleting}
          >
            <div className="overflow-hidden">
              {folders?.map((folder) => (
                <FolderNode
                  key={folder.id}
                  folder={folder}
                  level={0}
                  draggingFolder={draggingFolder}
                  draggingFile={draggingFile}
                  path={`/${folder.name}`}
                  activePath={activePath}
                  setActivePath={setActivePath}
                  onDelete={handleOnDelete}
                  onEdit={handleEdit}
                  rootPath={`/${folder.name}`}
                />
              ))}
            </div>
          </LoadingWrapper>
        )}
      </div>
      <FolderModal
        selectedFolder={selectedFolder}
        isOpen={isFolderModalOpen}
        onClose={() => {
          onFolderModalClose();
          setSelectedFolder(null);
        }}
      />
      <ConfirmModal
        onConfirm={isDeleted ? handleUnDelete : handleConfirm}
        isOpen={isConfirmModalOpen}
        onClose={handleCloseConfirm}
        isLoading={isDeleting || isUnDeleting}
        confirmLabel={isDeleted ? "Undo" : "Delete"}
        title="Delete Folder"
        size="md"
      >
        {isDeleted ? (
          <span className="dark:text-white">Folder Deleted</span>
        ) : (
          <div className="space-y-2 dark:text-white">
            <div className="flex items-center gap-4 rounded-lg border border-red-500 px-4 py-2 font-medium">
              <FolderIcon className={"h-5 w-5"} /> {selectedFolder?.name}
            </div>
            <p className="text-center">
              Are you sure you want to delete this folder and its{" "}
              {selectedFolder?.fileCount} files?
            </p>
          </div>
        )}
      </ConfirmModal>
    </>
  );
};

const FolderSkeleton = () => (
  <div className="flex-1">
    {[1, 2, 3, 4].map((i) => (
      <div key={i} className="py-2 pl-4 pr-3">
        <Skeleton className="h-7 w-full " />
      </div>
    ))}
  </div>
);
