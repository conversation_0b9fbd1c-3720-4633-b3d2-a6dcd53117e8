import { Label as FlowbiteLabel } from "flowbite-react";

import { cn } from "@/lib/utils";

import { useRequiredFields } from "./form";

type Props = React.ComponentProps<typeof FlowbiteLabel> & {
  required?: boolean;
};

export const Label = ({
  className,
  required,
  children,
  htmlFor,
  ...props
}: Props) => {
  const requiredFields = useRequiredFields();
  const isRequired = htmlFor && requiredFields.includes(htmlFor);

  return (
    <FlowbiteLabel
      {...props}
      htmlFor={htmlFor}
      className={cn("leading-5.25 text-sm font-medium", className)}
    >
      {children}
      {(required || isRequired) && <span className="ml-1 text-red-500">*</span>}
    </FlowbiteLabel>
  );
};
