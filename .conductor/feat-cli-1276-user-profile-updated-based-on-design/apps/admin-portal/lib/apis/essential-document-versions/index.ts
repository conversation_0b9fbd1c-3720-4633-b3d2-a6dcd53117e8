import { MetadataParams } from "@/lib/apis/types";

import Base<PERSON><PERSON> from "../base";
import {
  EBinderActivityResponse,
  EbinderUploadPlaceholderPayload,
  EbinderUploadPlaceholderResponse,
} from "./types";

class EssentialDocumentVersionsApi extends BaseApi {
  constructor() {
    super("/essential-document-versions", true);
  }

  async createVersion(payload: EbinderUploadPlaceholderPayload) {
    const { id, ...rest } = payload;
    return this.http.post<EbinderUploadPlaceholderResponse>(
      `/binder/${id}/signed-url-upload`,
      rest,
    );
  }

  async getDocumentActivities({
    id,
    params,
  }: {
    id: string;
    params: MetadataParams;
  }) {
    const queryParams = params ? this.generateQueryParams(params) : "";

    return this.http.get<EBinderActivityResponse>(
      `/${id}/activities?${queryParams}`,
    );
  }
}

export const essentialDocumentVersions = new EssentialDocumentVersionsApi();
