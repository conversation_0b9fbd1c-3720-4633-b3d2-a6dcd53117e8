import BaseA<PERSON> from "../base";
import type { MetadataParams } from "../types";
import type {
  AddAssignmentPayload,
  AddStudyToAssignmentPayload,
  Assignment,
  AssignmentListResponse,
  AssignmentStudy,
  AssignmentStudyListResponse,
  AssignmentUserListResponse,
  UpdateAssignmentPayload,
} from "./types";

class AssignmentsApi extends BaseApi {
  constructor() {
    super("/assignments", true);
  }

  public async list(params: MetadataParams): Promise<AssignmentListResponse> {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<AssignmentListResponse>(`?${paramUrl}`);
  }

  public async get(id: string): Promise<Assignment> {
    return this.http.get<Assignment>(`/${id}`);
  }

  public async create(payload: AddAssignmentPayload): Promise<Assignment> {
    return this.http.post<Assignment>("/", payload);
  }

  public async update(
    id: string,
    payload: UpdateAssignmentPayload,
  ): Promise<Assignment> {
    return this.http.put<Assignment>(`/${id}`, payload);
  }

  public async getUsers(id: string): Promise<AssignmentUserListResponse> {
    return this.http.get<AssignmentUserListResponse>(`/${id}/users`);
  }

  public async getStudies(id: string): Promise<AssignmentStudyListResponse> {
    return this.http.get<AssignmentStudyListResponse>(`/${id}/studies`);
  }

  public async addStudy(
    id: string,
    payload: AddStudyToAssignmentPayload,
  ): Promise<AssignmentStudy> {
    return this.http.post<AssignmentStudy>(`/${id}/studies`, payload);
  }

  public async removeStudy(id: string, studyId: string): Promise<void> {
    return this.http.delete(`/${id}/studies/${studyId}`);
  }
}

export const assignments = new AssignmentsApi();
export * from "./types";
