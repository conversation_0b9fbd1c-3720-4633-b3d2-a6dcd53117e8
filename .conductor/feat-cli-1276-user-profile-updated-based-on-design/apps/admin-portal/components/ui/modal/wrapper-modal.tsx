import { Modal as FlowbiteModal } from "flowbite-react";
import { ComponentProps } from "react";

import { Modal } from "@/components/ui/modal";
import { cn } from "@/lib/utils";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title: React.ReactNode;
  children: React.ReactNode;
  className?: string;
} & Omit<ComponentProps<typeof FlowbiteModal>, "title">;

export const WrapperModal = function ({
  isOpen,
  onClose,
  title,
  children,
  className,
  size = "2xl",
  ...rest
}: Props) {
  return (
    <Modal
      show={isOpen}
      onClose={onClose}
      size={size}
      {...rest}
      // className={cn("!overflow-hidden", className)}
    >
      <Modal.Header>{title}</Modal.Header>
      <Modal.Body>{children}</Modal.Body>
    </Modal>
  );
};
