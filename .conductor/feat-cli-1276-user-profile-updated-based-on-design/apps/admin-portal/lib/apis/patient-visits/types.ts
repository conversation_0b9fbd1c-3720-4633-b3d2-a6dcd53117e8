import { VisitStatus } from "@/components/ui/badges/visit-status-badge";

import { Epoch } from "../epochs/types";
import { PatientVisitDocument } from "../essential-documents";
import type { Patient } from "../patients";
import { StudyArm } from "../study-arms/types";
import type { ListBaseResponse } from "../types";

type VisitType = {
  id: string;
  name: string;
};

export type Visit = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  visitDate: string;
  visitDay: string;
  visitWindowStart: string;
  visitWindowEnd: string;
  studyArmId?: string;
  studyArm?: StudyArm;
  status: {
    id: string;
    name: VisitStatus;
    description: string;
  };
  statusId?: string;
  epochId?: string;
  epoch?: Epoch;
  visit: Visit;
  visitNumber: number;
  description: string;
  documentCount: number;
  patient?: Patient;
  visitType: VisitType;
  visitTemplate: {
    id: string;
    isVisit: boolean;
  };
  isVisit: boolean;
  previousPatientVisitId: string | null;
  visitOrder: number;
  protocolVisitId: string;
  activities?: VisitActivity[];
};

export type VisitListResponse = ListBaseResponse<Visit>;
export type PatientVisitDocumentListResponse =
  ListBaseResponse<PatientVisitDocument>;

export type VisitActivity = {
  id: string;
  name: string;
  description?: string;
  activityProcedures: ActivityProcedure[];
  isCustom?: boolean;
  procedures: ActivityProcedure[];
  order: number | null;
};

export type ActivityProcedure = {
  id: string;
  name: string;
  procedure: Procedure;
};

export type Procedure = {
  id: string;
  name: string;
};

export type ReOrderVisitActivities = {
  activityIds: string[];
};
