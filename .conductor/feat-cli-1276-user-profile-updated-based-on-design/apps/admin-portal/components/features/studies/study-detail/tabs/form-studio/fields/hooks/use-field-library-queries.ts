import { useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { parseAsString } from "nuqs";
import { useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const fieldLibraryKeys = {
  all: () => ["field-library"] as const,
  allLists: () => [...fieldLibraryKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...fieldLibraryKeys.allLists(), params] as const,
  allDetails: () => [...fieldLibraryKeys.all(), "detail"] as const,
  detail: (id: string) => [...fieldLibraryKeys.allDetails(), id] as const,
};

export const useFieldLibrary = () => {
  const studyId = useParams().id as string;
  const { page, take } = usePagination();
  const { orderBy, orderDirection } = useSort();
  const { search } = useSearch();
  const [fieldType] = useQueryState("fieldType", parseAsString);
  const [status] = useQueryState("status", parseAsString);

  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      search: search || undefined,
      fieldType: fieldType || undefined,
      status: status || undefined,
      studyId: studyId || undefined,
    },
  };

  return useQuery({
    queryKey: fieldLibraryKeys.list(params),
    queryFn: () => api.fieldLibrary.list(params),
    placeholderData: (prev) => prev,
  });
};

export const useFieldLibraryItem = (id: string) => {
  return useQuery({
    queryKey: fieldLibraryKeys.detail(id),
    queryFn: () => api.fieldLibrary.get(id),
    enabled: !!id,
  });
};
