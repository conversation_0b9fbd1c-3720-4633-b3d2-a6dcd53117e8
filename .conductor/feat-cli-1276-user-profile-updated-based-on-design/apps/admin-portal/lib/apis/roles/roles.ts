import <PERSON><PERSON><PERSON> from "../base";
import { MetadataParams } from "../types";
import {
  CreateRolePayload,
  PayloadAddPermissionToRole,
  PermissionListResponse,
  ResponseRoleDetail,
  Role,
  RoleListResponse,
  SubjectListResponse,
  UpdatePermissionPayload,
  UpdatePermissionSubjectPayload,
  UpdateRolePayload,
} from "./types";

export class Role<PERSON><PERSON> extends BaseApi {
  constructor() {
    super("/roles", true);
  }

  public async getRoles(params?: MetadataParams) {
    const queryParams = params ? this.generateQueryParams(params) : "";
    return this.http.get<RoleListResponse>(`/?${queryParams}`);
  }

  public async getRole(id: string) {
    return this.http.get<ResponseRoleDetail>(`/${id}`);
  }

  public async addRole(data: CreateRolePayload) {
    return this.http.post<Role>("/", data);
  }

  public async updateRole(data: UpdateRolePayload) {
    return this.http.put<Role>(`/${data.id}`, data);
  }

  public async deleteRole(id: string) {
    return this.http.patch<Role>(`/${id}/archive`);
  }

  public async getSubjects(params: MetadataParams) {
    const queryParams = params ? this.generateQueryParams(params) : "";

    return this.http.get<SubjectListResponse>(
      `/permissions/subjects?${queryParams}`,
    );
  }

  public async updateSubjects(payload: UpdatePermissionSubjectPayload) {
    return this.http.patch<SubjectListResponse>(
      `/permissions/subjects/${payload.id}`,
      payload,
    );
  }

  public async getPermissions(params?: MetadataParams) {
    const queryParams = params ? this.generateQueryParams(params) : "";
    return this.http.get<PermissionListResponse>(`/permissions?${queryParams}`);
  }

  public async updatePermission(payload: UpdatePermissionPayload) {
    return this.http.patch<PermissionListResponse>(
      `/permissions/${payload.id}`,
      payload,
    );
  }

  public async addPermission({
    permissionId,
    roleId,
  }: PayloadAddPermissionToRole) {
    return this.http.post<{
      message: string;
    }>(`/${roleId}/permissions`, {
      permissionId,
    });
  }

  public async deletePermission({
    permissionId,
    roleId,
  }: PayloadAddPermissionToRole) {
    return this.http.delete<{
      message: string;
    }>(`/${roleId}/permissions/${permissionId}`);
  }
}
