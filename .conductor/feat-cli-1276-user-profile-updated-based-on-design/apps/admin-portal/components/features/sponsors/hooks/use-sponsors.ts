import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const USE_SPONSORS_QUERY_KEY = "sponsors";

export const useSponsors = (params?: MetadataParams, ignoreFilter = false) => {
  const { page, take } = usePagination();
  const { search } = useSearch();
  const searchParams = useSearchParams();
  const nonActive = searchParams.get("non-active") === "true";

  return useQuery({
    queryKey: [USE_SPONSORS_QUERY_KEY, page, take, search],
    queryFn: () =>
      api.sponsors.list({
        page,
        take,
        ...params,
        filter: ignoreFilter
          ? undefined
          : { name: search, isActive: nonActive ? undefined : true },
      }),
    placeholderData: (prevData) => prevData,
  });
};

export const useInfiniteSponsors = (search: string, initialPageSize = 10) => {
  const searchParams = useSearchParams();
  const nonActive = searchParams.get("non-active") === "true";

  return useInfiniteQuery({
    queryKey: ["infinite-sponsors", search, nonActive],
    queryFn: ({ pageParam = 1 }) =>
      api.sponsors.list({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search, isActive: nonActive ? undefined : true },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
