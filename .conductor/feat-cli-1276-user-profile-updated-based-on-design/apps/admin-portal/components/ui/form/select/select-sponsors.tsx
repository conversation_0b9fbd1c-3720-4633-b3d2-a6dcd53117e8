import { useQuery } from "@tanstack/react-query";
import type { ComponentProps } from "react";

import api from "@/lib/apis";

import { Select } from "./select";

export const SelectSponsors = (props: ComponentProps<typeof Select>) => {
  const { data } = useQuery({
    queryKey: ["sponsors"],
    queryFn: () => api.sponsors.list({ take: 200 }),
  });

  return (
    <Select
      {...props}
      placeholder="Select sponsor"
      options={
        data?.results?.map((sponsor) => ({
          label: sponsor.name,
          value: sponsor.id,
        })) || []
      }
    />
  );
};
