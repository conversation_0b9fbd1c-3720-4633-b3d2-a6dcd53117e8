import { useQuery } from "@tanstack/react-query";
import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const visitTypeKeys = {
  all: ["visit-types"] as const,

  allList: () => [...visitTypeKeys.all, "list"] as const,
  list: (params?: MetadataParams) =>
    [...visitTypeKeys.allList(), "list", params] as const,
};

export const useVisitTypes = () => {
  const { page } = usePagination();
  const [isActive] = useQueryState("isActive", parseAsBoolean);
  const [studyId] = useQueryState("studyId", parseAsString);

  const params = {
    page,
    filter: {
      isActive: isActive,
      studyId,
    },
  };

  return useQuery({
    queryKey: visitTypeKeys.list(params),
    queryFn: () => api.visitTypes.list(params),
    placeholderData: (prevData) => prevData,
  });
};
