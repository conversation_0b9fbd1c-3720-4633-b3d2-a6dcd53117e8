import <PERSON><PERSON><PERSON> from "../base";
import { MetadataParams } from "../types";
import type {
  AddProtocolContentPayload,
  AddProtocolEncounterPayload,
  AddProtocolEpochPayload,
  AddProtocolStudyArmPayload,
  ArchiveProtocolEpochPayload,
  ArchiveProtocolStudyArmPayload,
  EditProtocolContentPayload,
  Protocol,
  ProtocolContent,
  ProtocolEncounter,
  ProtocolEpochsListResponse,
  ProtocolStudyArmsListResponse,
  ReorderProtocolEncountersPayload,
  ReorderProtocolEpochsPayload,
  UpdateProtocolEncountersPayload,
  UpdateProtocolEpochsPayload,
  UpdateProtocolPayload,
  UpdateProtocolStudyArmsPayload,
} from "./types";

class ProtocolsApi extends BaseApi {
  constructor() {
    super("/protocols", true);
  }

  public async get(id: string) {
    return this.http.get<Protocol>(`/${id}`);
  }

  public async signedUploadUrl(id: string): Promise<{ url: string }> {
    return this.http.get<{ url: string }>(`/${id}/protocol-signed-url-upload`);
  }

  public async addContent(payload: AddProtocolContentPayload) {
    return this.http.post(`/${payload.protocolId}/content`, payload);
  }

  public async editContent(payload: EditProtocolContentPayload) {
    return this.http.put(`/${payload.protocolId}/content`, payload);
  }

  public async getContent(id: string) {
    return this.http.get<ProtocolContent>(`/${id}/content`);
  }

  public async download(id: string) {
    return this.http.get<{ url: string }>(
      `/${id}/protocol-signed-url-download`,
    );
  }

  public async addEncounter(
    payload: AddProtocolEncounterPayload,
  ): Promise<void> {
    return this.http.post(`/${payload.protocolId}/encounters`, payload);
  }

  public async addEpoch(protocolId: string, payload: AddProtocolEpochPayload) {
    return this.http.post(`/${protocolId}/epochs`, payload);
  }

  public async addStudyArm(payload: AddProtocolStudyArmPayload): Promise<void> {
    return this.http.post(`/${payload.protocolId}/study-arms`, payload);
  }
  public async updateProtocol(
    id: string,
    payload: UpdateProtocolPayload,
  ): Promise<Protocol> {
    return this.http.put(`/${id}`, payload);
  }

  public async updateProtocolsEncounter(
    payload: UpdateProtocolEncountersPayload,
  ): Promise<void> {
    return this.http.put(
      `/${payload.protocolId}/encounters/${payload.id}`,
      payload,
    );
  }

  public async updateProtocolsStudyArm(
    payload: UpdateProtocolStudyArmsPayload,
  ): Promise<void> {
    return this.http.put(
      `/${payload.protocolId}/study-arms/${payload.id}`,
      payload,
    );
  }

  public async updateProtocolsEpoch(payload: UpdateProtocolEpochsPayload) {
    return this.http.put(
      `/${payload.protocolId}/epochs/${payload.id}`,
      payload,
    );
  }

  public async getProtocolEncounters(id: string): Promise<ProtocolEncounter[]> {
    return this.http.get<ProtocolEncounter[]>(`/${id}/encounters`);
  }

  public async getProtocolEpochs(
    id: string,
    params?: MetadataParams,
  ): Promise<ProtocolEpochsListResponse> {
    const paramUrl = params ? this.generateQueryParams(params) : "";
    return this.http.get<ProtocolEpochsListResponse>(
      `/${id}/epochs?${paramUrl}`,
    );
  }

  public async getProtocolStudyArms(
    id: string,
    params?: MetadataParams,
  ): Promise<ProtocolStudyArmsListResponse> {
    const paramUrl = params ? this.generateQueryParams(params) : "";
    return this.http.get<ProtocolStudyArmsListResponse>(
      `/${id}/study-arms?${paramUrl}`,
    );
  }

  public async getProtocolEncounterById(
    protocolId: string,
    encounterId: string,
  ): Promise<ProtocolEncounter> {
    return this.http.get<ProtocolEncounter>(
      `/${protocolId}/encounters/${encounterId}`,
    );
  }

  public async publishProtocol(id: string): Promise<void> {
    return this.http.patch(`/${id}/publish`, { isPublished: true });
  }

  public async reorderProtocolEncounters(
    payload: ReorderProtocolEncountersPayload,
  ): Promise<void> {
    return this.http.put(`/${payload.encounterId}/encounters/reorder`, payload);
  }

  public async reorderProtocolEpochs(
    payload: ReorderProtocolEpochsPayload,
  ): Promise<void> {
    return this.http.put(`/${payload.epochId}/epochs/reorder`, payload);
  }

  public async archiveProtocolStudyArm(
    payload: ArchiveProtocolStudyArmPayload,
  ): Promise<void> {
    return this.http.put(
      `/${payload.protocolId}/study-arms/${payload.studyArmId}`,
    );
  }

  public async archiveProtocolEncounter(
    protocolId: string,
    encounterId: string,
  ): Promise<void> {
    return this.http.delete(`/${protocolId}/encounters/${encounterId}`);
  }

  public async archiveProtocolEpoch(
    payload: ArchiveProtocolEpochPayload,
  ): Promise<void> {
    return this.http.delete(`/${payload.protocolId}/epochs/${payload.epochId}`);
  }
  public async exportScheduleOfActivity(protocolId: string) {
    return this.http.get<Blob>(`/${protocolId}/schedule-of-activities/export`);
  }
}

export const protocols = new ProtocolsApi();
export * from "./types";
