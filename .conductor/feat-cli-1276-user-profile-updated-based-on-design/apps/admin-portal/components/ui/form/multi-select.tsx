"use client";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import { Check, ChevronDown, Search, X } from "lucide-react";
import React from "react";
import { useEffect, useMemo, useState } from "react";
import { get, useController, useFormContext } from "react-hook-form";
import { useDebounceValue } from "usehooks-ts";

import { cn } from "@/lib/utils";

export type MultiSelectProps = {
  name: string;
  shouldShowError?: boolean;
  placeholder?: string;
  options?: Readonly<
    { label: React.ReactNode; value: string; disabled?: boolean }[]
  >;
  onChange?: (value: string[]) => void;
  className?: string;
  dependentFieldNames?: string[];
  searchable?: boolean;
  searchPlaceholder?: string;
  readOnly?: boolean;
  disabled?: boolean;
};

export const MultiSelect = ({
  name,
  shouldShowError = true,
  placeholder = "Select options",
  options = [],
  onChange,
  className,
  disabled,
  dependentFieldNames,
  searchable = false,
  searchPlaceholder = "Search...",
  readOnly = false,
}: MultiSelectProps) => {
  const { setValue, watch, control } = useFormContext();
  const {
    field,
    formState: { errors },
  } = useController({
    name,
    control,
  });
  const [isOpen, setIsOpen] = useState(false);
  const [debouncedSearch, setDebouncedSearch] = useDebounceValue("", 300);

  const isDisabled = disabled || field.disabled;

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: (open) => {
      setIsOpen(open);
      setDebouncedSearch("");
      if (!open) field.onBlur();
    },
    placement: "bottom-start",
    middleware: [
      offset(8),
      flip({
        fallbackPlacements: ["top-start"],
        fallbackStrategy: "bestFit",
        padding: 1,
        crossAxis: false,
      }),
      shift({
        padding: 1,
      }),
      size({
        apply({ rects, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
          });
        },
        padding: 1,
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context, { enabled: !readOnly && !isDisabled }),
    useDismiss(context),
  ]);

  // Filter options based on search term
  const filteredOptions = useMemo(() => {
    if (!searchable || !debouncedSearch) {
      return options;
    }
    return options.filter((option) =>
      String(option.label)
        .toLowerCase()
        .includes(debouncedSearch.toLowerCase()),
    );
  }, [options, debouncedSearch, searchable]);

  const errorObj = get(errors, name);
  const errorMessage = errorObj?.message?.valueOf();
  const hasError = typeof errorMessage === "string";

  const selectedOptions = options.filter((option) =>
    (field.value || []).includes(option.value),
  );

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    field.onChange([]);
    field.onBlur();
    onChange?.([]);
  };

  const handleOptionToggle = (optionValue: string) => {
    const currentValue = field.value || [];
    const isSelected = currentValue.includes(optionValue);
    const newValue = isSelected
      ? currentValue.filter((opt: string) => opt !== optionValue)
      : [...currentValue, optionValue];

    field.onChange(newValue);
    onChange?.(newValue);
    field.onBlur();
  };

  useEffect(() => {
    const { unsubscribe } = watch((_, { name: fieldName }) => {
      if (fieldName && dependentFieldNames?.includes(fieldName)) {
        setValue(name, [], {
          shouldValidate: true,
        });
      }
    });
    return () => unsubscribe();
  }, [watch]);

  return (
    <div className={cn("relative", className)}>
      <div
        ref={refs.setReference}
        {...getReferenceProps()}
        className="relative w-full"
      >
        <input
          type="text"
          readOnly
          id={name}
          className={cn(
            "flex w-full items-center justify-between rounded-lg border border-gray-300 bg-gray-50 py-2.5 pl-2.5 pr-[55px] text-left text-sm focus:outline-none focus:ring-2 dark:border-gray-600",
            "cursor-pointer transition-all duration-200 ease-in-out disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",
            hasError
              ? "border-red-500 focus:ring-red-500 dark:border-red-500 dark:focus:ring-red-500"
              : "focus:ring-blue-500 dark:focus:ring-blue-500",
            readOnly && "cursor-default",
          )}
          value={
            selectedOptions.length
              ? selectedOptions.map((option) => String(option.label)).join(", ")
              : ""
          }
          placeholder={placeholder}
          disabled={isDisabled}
        />

        {/* Icons */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-2.5">
          {selectedOptions.length > 0 && !readOnly && !isDisabled ? (
            <button
              type="button"
              onClick={handleClear}
              className="mr-1 rounded p-0.5 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <X className="h-4 w-4 text-gray-400" />
            </button>
          ) : null}
          <ChevronDown
            className={cn(
              "h-5 w-5 shrink-0 text-gray-400 transition-transform duration-200 dark:text-gray-400",
              isOpen ? "rotate-180 transform" : "",
              isDisabled && "opacity-50",
            )}
          />
        </div>
      </div>

      {hasError && shouldShowError && (
        <span className="text-sm text-red-500 dark:text-red-400">
          {errorMessage}
        </span>
      )}

      {/* Dropdown Panel */}
      {isOpen && (
        <FloatingPortal>
          <FloatingFocusManager context={context} modal={false}>
            <div
              ref={refs.setFloating}
              style={floatingStyles}
              {...getFloatingProps()}
              className="z-50 rounded-lg border border-gray-200 bg-white shadow-lg dark:border-gray-600 dark:bg-gray-700"
            >
              {/* Search Input */}
              {searchable && (
                <div className="border-b border-gray-200 p-2 dark:border-gray-600">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder={searchPlaceholder}
                      onChange={(e) => setDebouncedSearch(e.target.value)}
                      className="w-full rounded-md border border-gray-300 bg-white py-2 pl-9 pr-3 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400"
                      autoFocus
                    />
                  </div>
                </div>
              )}

              {/* Options List */}
              <ul className="select-options max-h-60 overflow-y-auto py-1">
                {filteredOptions.length === 0 ? (
                  <li className="flex h-10 items-center justify-center px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                    {searchable && debouncedSearch
                      ? "No results found"
                      : "No options available"}
                  </li>
                ) : (
                  filteredOptions.map((option) => {
                    const isSelected = (field.value || []).includes(
                      option.value,
                    );
                    return (
                      <li
                        key={option.value}
                        className={cn(
                          "flex w-full cursor-pointer items-center justify-between px-3 py-2 text-left text-sm",
                          "hover:bg-gray-100 dark:hover:bg-gray-600",
                          option.disabled &&
                            "cursor-not-allowed text-red-400 dark:text-red-500",
                          isSelected
                            ? "bg-blue-50 text-blue-600 dark:bg-blue-600/20 dark:text-blue-400"
                            : "text-gray-900 dark:text-gray-200",
                        )}
                        onClick={() =>
                          !option.disabled && handleOptionToggle(option.value)
                        }
                        role="option"
                        aria-selected={isSelected}
                        aria-disabled={option.disabled}
                      >
                        <span className="block truncate">{option.label}</span>
                        {isSelected && (
                          <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        )}
                      </li>
                    );
                  })
                )}
              </ul>
            </div>
          </FloatingFocusManager>
        </FloatingPortal>
      )}
    </div>
  );
};
