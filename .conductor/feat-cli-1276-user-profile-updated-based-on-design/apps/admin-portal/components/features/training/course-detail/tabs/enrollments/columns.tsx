import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import {
  TableRemoveButton,
  TableViewButton,
} from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { ChapterProgress, Enrollment } from "@/lib/apis/training-modules";
import { formatDate } from "@/lib/utils";

const ENROLLMENT_STATUSES = {
  not_started: {
    variant: "warning",
    label: "Not Started",
  },
  in_progress: {
    variant: "primary",
    label: "In Progress",
  },
  completed: {
    variant: "success",
    label: "Completed",
  },
  cancelled: {
    variant: "danger",
    label: "Cancelled",
  },
} as const;

export const generateEnrollmentColumns = ({
  onView,
  onDelete,
}: {
  onView: (data: Enrollment) => void;
  onDelete: (data: Enrollment) => void;
}): ColumnDef<Enrollment>[] => [
  {
    header: "First Name",
    accessorKey: "user.firstName",
    cell: ({ row }) => {
      return (
        <Link
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
          href={`/users/${row.original.userId}`}
        >
          {row.original.user.firstName}
        </Link>
      );
    },
  },
  {
    header: "Last Name",
    accessorKey: "user.lastName",
    cell: ({ row }) => {
      return (
        <Link
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
          href={`/users/${row.original.userId}`}
        >
          {row.original.user.lastName}
        </Link>
      );
    },
  },
  {
    header: "Group Name",
    accessorKey: "profile.currentGroup.name",
  },
  {
    header: "Status",
    cell: ({ row }) => {
      return (
        <PillBadge
          variant={
            ENROLLMENT_STATUSES[row.original.status].variant ?? "default"
          }
        >
          {ENROLLMENT_STATUSES[row.original.status].label}
        </PillBadge>
      );
    },
  },
  {
    header: "Start Date",
    cell: ({ row }) => {
      return row.original.startedAt
        ? formatDate(row.original.startedAt, "yyyy-MM-dd")
        : "N/A";
    },
  },
  {
    header: "Completed Date",
    cell: ({ row }) => {
      return row.original.completedAt
        ? formatDate(row.original.completedAt, "yyyy-MM-dd")
        : "N/A";
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-4">
          <TableViewButton
            type="button"
            onClick={() => {
              onView?.(row.original);
            }}
          />
          <TableRemoveButton onClick={() => onDelete(row.original)} />
        </div>
      );
    },
  },
];

export const chapterProgressColumns: ColumnDef<ChapterProgress>[] = [
  {
    header: "Chapter",
    accessorKey: "chapter.name",
  },
  {
    header: "Status",
    cell: ({ row }) => {
      return (
        <PillBadge
          variant={
            ENROLLMENT_STATUSES[row.original.status].variant ?? "default"
          }
        >
          {ENROLLMENT_STATUSES[row.original.status].label}
        </PillBadge>
      );
    },
  },
  {
    header: "Start Date",
    cell: ({ row }) => {
      return row.original.startedAt
        ? formatDate(row.original.startedAt, "yyyy-MM-dd")
        : "N/A";
    },
  },
  {
    header: "Completed Date",
    cell: ({ row }) => {
      return row.original.completedAt
        ? formatDate(row.original.completedAt, "yyyy-MM-dd")
        : "N/A";
    },
  },
];
