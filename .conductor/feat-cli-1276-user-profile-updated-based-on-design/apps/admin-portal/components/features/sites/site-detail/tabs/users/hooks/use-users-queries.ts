import { skipToken, useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const userKeys = {
  all: () => ["site-detail-users-tab"] as const,

  allLists: () => [...userKeys.all(), "lists"] as const,
  list: (params?: MetadataParams) => [...userKeys.allLists(), params] as const,

  customPermissions: (profileId: string) =>
    [...userKeys.all(), "custom-permission", profileId] as const,
};

export const useUsersByGroupId = (id?: string) => {
  const { page, take } = usePagination();

  const params = {
    page,
    take,
  };

  return useQuery({
    queryKey: id ? userKeys.list(params) : [],
    queryFn: id ? () => api.groups.profiles(id) : skipToken,
    placeholderData: (prev) => prev,
  });
};

export const useUserCustomPermission = (profileId?: string) => {
  return useQuery({
    queryKey: profileId ? userKeys.customPermissions(profileId) : [],
    queryFn: profileId
      ? () => api.users.getUserCustomPermission(profileId)
      : skipToken,
  });
};
