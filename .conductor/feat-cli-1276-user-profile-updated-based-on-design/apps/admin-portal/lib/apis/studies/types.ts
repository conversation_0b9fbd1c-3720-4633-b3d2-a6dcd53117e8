import { PatientStatus } from "@/components/ui/badges/patient-status-badge";
import type { ListBaseResponse } from "@/lib/apis/types";

import type { Contact } from "../contacts";
import type { Protocol, ProtocolEncounter } from "../protocols/types";
import type { Site } from "../sites/types";
import type { Sponsor } from "../sponsors/types";
import { StudyArm } from "../study-arms/types";
import type { VisitSchedule } from "../visit-schedules/types";

export type SiteStudy = {
  pin: string;
  siteId: string;
  studyId: string;
  site: Site;
  localStudies: Study[];
  name: string;
};

export type StudyStatuses = (typeof STUDY_STATUSES)[number]["value"];

export const STUDY_STATUSES_MAP: Record<StudyStatuses, string> = {
  preSelection: "Pre Selection",
  startup: "Startup",
  enrollment: "Enrollment",
  followUp: "Follow Up",
  closed: "Closed",
};

export const STUDY_STATUSES = [
  {
    label: "Pre Selection",
    value: "preSelection",
  },
  {
    label: "Startup",
    value: "startup",
  },
  {
    label: "Enrollment",
    value: "enrollment",
  },
  {
    label: "Follow Up",
    value: "followUp",
  },
  {
    label: "Closed",
    value: "closed",
  },
] as const;

export type Study = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  displayName: string | null;
  title: string;
  CtgId: string;
  studyMedicalId: string;
  isActive: boolean;
  archived: boolean;
  numberOfVisits: number;
  logoLocation: string;
  sponsor: Sponsor;
  site: Site;
  enrolledPatients: number;
  patientTarget: number;
  numberOfSites: number;
  status: string;
  totalVisits: number;
  completedVisits: number;
  siteId?: string;
  startDate: string;
  endDate: string;
  principalInvestigator: string;
  nct: string;
  studyCode: string;
  cro: string;
  phase: string;
  studyId: string;
  template?: boolean;
  numberOfQCSteps: number;
  artifactCategoryVersionId: string;
  localStudies: LocalStudy[];
};
export type LocalStudy = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  siteId: string;
  studyId: string;
  protocolId: string;
  pin: string;
  shortId: string;
  status: string;
  patientTarget: number;
  principalInvestigator: string;
  subInvestigator: string | null;
  siteNumber: string | null;
  isScaffolded: boolean;
  site: {
    id: string;
    createdDate: string;
    lastUpdatedDate: string;
    name: string;
    displayName: string | null;
    phone: string;
    email: string;
    logoLocation: string | null;
    isActive: boolean;
    archived: boolean;
    groupId: string;
    shortId: string;
  };
};

export type LocalStudyDetail = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  sponsorId: string;
  name: string;
  displayName: string | null;
  title: string;
  nct: string | null;
  studyCode: string;
  isActive: boolean;
  archived: boolean;
  logoLocation: string | null;
  shortId: string;
  startDate: string | null;
  endDate: string | null;
  description: string | null;
  primaryEndpoint: string | null;
  secondaryEndpoint: string | null;
  cro: string | null;
  isScaffolded: boolean;
  studyType: string | null;
  interventionType: string | null;
  blinding: string | null;
  randomization: boolean | null;
  phase: string | null;
  artifactCategoryVersionId: string;
  sponsor: Sponsor;
  siteId: string;
  studyId: string;
  protocolId: string;
  pin: string;
  status: StudyStatuses;
  patientTarget: number;
  principalInvestigator: string;
  subInvestigator: string | null;
  siteNumber: string;
  site: {
    id: string;
    createdDate: string;
    lastUpdatedDate: string;
    name: string;
    displayName: string | null;
    phone: string;
    email: string;
    logoLocation: string | null;
    isActive: boolean;
    archived: boolean;
    groupId: string;
    shortId: string;
  };
  study: Study;
  localStudyId: string;
  enrolledPatients: number;
  completedVisits: number;
  numberOfSites: number;
  totalVisits: number;
};

export type LocalStudyListResponse = ListBaseResponse<LocalStudyDetail>;

export type StudyListResponse = ListBaseResponse<Study>;
export type StudySitesListResponse = ListBaseResponse<Study>;
export type StudySitesResponse = ListBaseResponse<SiteStudy>;
export type StudyContactsResponse = ListBaseResponse<Contact>;
export type StudyVisitSchedulesResponse = ListBaseResponse<VisitSchedule>;
export type StudyProtocolsResponse = ListBaseResponse<Protocol>;
export type CreateStudyPayload = {
  sponsorId: string;
  name: string;
  studyCode: string;
  phase?: string;
  startDate?: string;
  endDate?: string;
  description?: string;
  nct?: string;
  primaryEndpoint?: string;
  studyType?: string;
  interventionType?: string;
  blinding?: string;
  randomization?: boolean;
  numberOfQCSteps: number;
  artifactCategoryVersionId: string;
};

export type UpdateStudyPayload = Partial<CreateStudyPayload>;
export type UpdateEncounterPayload = Partial<ProtocolEncounter>;

export type AddStudyContactPayload = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: {
    addressLine: string;
    city: string;
    stateProvinceId: string;
    zipPostalCode: string;
    countryId: string;
  };
};

export type AddStudyProtocolPayload = Omit<
  Protocol,
  "id" | "hasContent" | "status"
>;

export type AddVisitSchedulePayload = {
  name: string;
  publishDate?: string;
};

export type Patient = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  name2: string | null;
  firstName: string;
  lastName: string;
  shortId: string;
  dateOfBirth: string;
  enrollmentDate: string;
  notes: string | null;
  status: PatientStatus;
  isActive: boolean;
  archived: boolean;
  sex: string;
  studyId: string;
  siteId: string;
  currentProtocolId: string;
  studyArmId: string;
  dayOneStartDate: string;
  site: Site;
  currentProtocol: Protocol;
  studyArm: StudyArm;
  study: Study;
};

export type PatientListResponse = ListBaseResponse<Patient>;

export type AssignableUser = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  userId: string;
  currentGroupId: string;
  isActive: boolean;
  hasCustomPermissions: boolean;
  shortId: string;
  user: User;
};
type User = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  isActive: boolean;
  status: string; // example: "active"
  emailVerified: boolean;
  lastLogin: string | null;
  loginAttempts: number;
  loginCount: number;
  currentSignInIp: string | null;
  currentProfileId: string;
  createdDate: string;
  lastUpdatedDate: string;
};

export type AssignableUserListResponse = ListBaseResponse<AssignableUser>;

export type CreateStudyTaskPayload = {
  name: string;
  priority: string;
  status: string;
  description: string;
  dueDate: string;
  siteId: string;
  studyId: string;
  patientId?: string;
  assignedToProfileId?: string;
};
