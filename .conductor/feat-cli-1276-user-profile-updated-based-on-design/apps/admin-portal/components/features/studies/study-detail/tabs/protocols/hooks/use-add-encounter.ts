import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { AddProtocolEncounterPayload } from "@/lib/apis/protocols/types";

import { USE_PROTOCOL_ENCOUNTERS_QUERY_KEY } from "../hooks/use-protocol-encounters";

export const useAddProtocolEncounter = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: AddProtocolEncounterPayload) =>
      api.protocols.addEncounter(payload),

    onSuccess: (_, variables) =>
      queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTERS_QUERY_KEY, variables.protocolId],
      }),
    onError: (err) => toast.error(err?.message || "Failed to add visit"),
    onSettled: (_, err) => !err && toast.success("Add visit successfully"),
  });
};
