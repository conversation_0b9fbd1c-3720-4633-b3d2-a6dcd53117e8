import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { use<PERSON>orm<PERSON><PERSON>x<PERSON>, useWatch } from "react-hook-form";
import { z } from "zod";

import { useStudyProtocols } from "@/components/features/studies/study-detail/tabs/protocols/hooks/use-protocols-queries";
import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form, InputField, Select } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteStudies } from "@/hooks/queries/use-infinite-studies";
import { STUDY_STATUSES } from "@/lib/apis/studies";

import { useSiteAddStudy } from "../hooks/use-site-add-study";

const addStudySchema = z.object({
  studyId: z
    .string({ required_error: "Study is required" })
    .min(1, "Study is required"),
  status: z
    .string({ required_error: "Status is required" })
    .min(1, "Status is required"),
  patientTarget: z.coerce
    .number({ required_error: "Patient target is required" })
    .min(1, "Patient target must be greater than 0"),
  principalInvestigator: z
    .string({ required_error: "Principal investigator is required" })
    .min(1, "Principal investigator is required"),
  subInvestigator: z.string().optional(),
  siteNumber: z
    .string()
    .max(30, "Maximum allowed length is 30 characters")
    .optional(),
  protocolId: z
    .string({ required_error: "Protocol is required" })
    .min(1, "Protocol is required"),
});

type ModalAddStudyProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddStudy = ({ isOpen, onClose }: ModalAddStudyProps) => {
  const { mutateAsync: addStudy, isPending: isAdding } = useSiteAddStudy();
  const { id } = useParams();
  async function onSubmit(data: z.infer<typeof addStudySchema>) {
    await addStudy({ ...data, id: id as string });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Study</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={addStudySchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
          defaultValues={{
            studyId: "",
            status: "",
            patientTarget: 1,
            principalInvestigator: "",
            subInvestigator: "",
            siteNumber: "",
            protocolId: "",
          }}
        >
          <StudyForm onClose={onClose} isSubmitting={isAdding} />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type StudyFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
};

const StudyForm = ({ onClose, isSubmitting }: StudyFormProps) => {
  const { control } = useFormContext();
  const studyId = useWatch({ control, name: "studyId" });
  const { data: protocols } = useStudyProtocols(studyId);

  return (
    <>
      <div className="grid gap-3 sm:grid-cols-2 sm:gap-6">
        <div className="space-y-1 sm:col-span-2">
          <Label htmlFor="studyId">Study</Label>
          <LazySelect
            id="studyId"
            name="studyId"
            placeholder="Select study"
            searchPlaceholder="Search studies..."
            useInfiniteQuery={useInfiniteStudies}
            getOptionLabel={(study) => `${study.name} (${study.studyCode})`}
            getOptionValue={(study) => study.id}
            params={[]}
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="protocolId">Protocol</Label>
          <Select
            id="protocolId"
            name="protocolId"
            placeholder="Select protocol"
            options={protocols?.results.map((protocol) => ({
              label: protocol.name,
              value: protocol.id,
            }))}
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="status">Status</Label>
          <Select
            id="status"
            name="status"
            placeholder="Select status"
            options={STUDY_STATUSES.map((status) => ({
              label: status.label,
              value: status.value,
            }))}
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="patientTarget">Patient Target</Label>
          <InputNumber
            min={1}
            id="patientTarget"
            name="patientTarget"
            placeholder="Enter patient target..."
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="principalInvestigator">Principal Investigator</Label>
          <InputField
            id="principalInvestigator"
            name="principalInvestigator"
            placeholder="Enter principal investigator..."
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="subInvestigator">Sub Investigator</Label>
          <InputField
            id="subInvestigator"
            name="subInvestigator"
            placeholder="Enter sub investigator..."
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="siteNumber">Site Number</Label>
          <InputField
            id="siteNumber"
            name="siteNumber"
            placeholder="Enter site number..."
          />
        </div>
      </div>
      <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </>
  );
};
