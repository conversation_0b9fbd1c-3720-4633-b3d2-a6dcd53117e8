import { CiEdit } from "react-icons/ci";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { But<PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Protocol, ProtocolEncounter } from "@/lib/apis/protocols";
import { useEncounterStore } from "@/stores/encounte-store";

import { useProtocol } from "../../hooks/use-protocols-queries";
import { ModalEditEncounter } from "./modal-edit-encounter";
import { useProtocolEncounter } from "./protocol-encounter-detail/hooks/use-protocol-encounter";
import { EncounterTabs } from "./protocol-encounter-detail/tabs";

type ModalEncounterDetailProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedProtocol: Protocol | null;
};

export const ModalEncounterDetail = function ({
  isOpen,
  onClose,
  selectedProtocol,
}: ModalEncounterDetailProps) {
  const { currentEncounter } = useEncounterStore();
  const { isOpen: isOpenEditModal, open, close } = useDisclosure();
  const { data: protocol } = useProtocol(selectedProtocol?.id);

  const { data: encounter, isLoading: isLoadingEncounter } =
    useProtocolEncounter(selectedProtocol?.id, currentEncounter?.id);
  return (
    <Modal
      show={isOpen}
      onClose={onClose}
      className="overflow-y-auto [&>div]:max-w-7xl"
    >
      <Modal.Header>Visit Detail</Modal.Header>
      <Modal.Body className="dark:bg-gray-900">
        <div className="flex flex-col gap-4 ">
          <div className="flex items-center justify-between">
            <div className="text-lg font-bold dark:text-gray-300">
              {encounter?.name ?? "N/A"}
            </div>
            <Button
              disabled={protocol?.isPublished}
              variant="primary"
              onClick={open}
            >
              <CiEdit />
              Edit Visit
            </Button>
          </div>
          {isLoadingEncounter ? (
            <EncounterContentSkeleton />
          ) : (
            <OverviewCard
              className="[&>div]:p-3 sm:[&>div]:p-4"
              title="Overview"
            >
              <div className="grid grid-cols-2 gap-4 lg:grid-cols-3">
                <OverviewItem label="Name" value={encounter?.name ?? "N/A"} />
                <OverviewItem
                  label="Published Date"
                  value={encounter?.publishedDate ?? "N/A"}
                />
                <OverviewItem
                  label="Visit Type"
                  value={encounter?.visitType?.name ?? "N/A"}
                />
              </div>
            </OverviewCard>
          )}
          <EncounterTabs
            totalActivities={encounter?.totalActivities}
            protocolId={selectedProtocol?.id as string}
          />
        </div>
        <ModalEditEncounter
          encounter={encounter as ProtocolEncounter}
          isOpen={isOpenEditModal}
          onClose={close}
          protocolId={selectedProtocol?.id as string}
        />
      </Modal.Body>
    </Modal>
  );
};

const EncounterContentSkeleton = () => {
  return (
    <div className="flex flex-col">
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
        <Skeleton className="mb-3 h-6 w-24" />
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-40" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </div>
    </div>
  );
};
