/* eslint-disable @next/next/no-img-element */
"use client";
import { SignIn } from "@clerk/nextjs";
import { Dropdown } from "flowbite-react";
import Image from "next/image";
import { useEffect, useState } from "react";

import { cn } from "@/lib/utils";

export const SignInPageContent = () => {
  return (
    <div className="flex h-full min-h-screen w-full">
      <div className="flex h-screen flex-1 flex-col items-center justify-center bg-white dark:bg-gray-800">
        <div className="lg:px-18 relative flex h-full w-full flex-col justify-center space-y-6 px-8 sm:px-12">
          <SwitchCountries />
          <div className="flex items-center justify-center [&_input]:rounded-md">
            <SignIn />
          </div>
        </div>
      </div>
      <div className="relative hidden h-screen flex-1 md:block">
        <img
          // src="/images/authentication/right-content.png"
          // alt="right-content"
          src="/images/authentication/right-content-admin.png"
          alt="right-content"
          className="h-full w-full object-cover"
        />
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <img src="/images/authentication/large-logo.svg" alt="logo" />
        </div>
      </div>
    </div>
  );
};

const SwitchCountries = () => {
  const [selectedCountry, setSelectedCountry] = useState("us");

  useEffect(() => {
    if (window !== undefined) {
      const isUsa = window.location.href.includes(".us.");

      if (isUsa) {
        setSelectedCountry("us");
      } else {
        setSelectedCountry("ca");
      }
    }
  }, []);

  function changeCountry(country: string) {
    const url = window.location.href;

    if (country === "us" && url.includes(".ca.")) {
      window.location.replace(url.replace(".ca.", ".us."));
      return;
    }

    if (country === "ca" && url.includes(".us.")) {
      window.location.replace(url.replace(".us.", ".ca."));
    }
  }

  return (
    <div
      className={cn(
        "[&>button]:border [&>button]:border-gray-300 [&>button]:!ring-blue-500 [&>button]:dark:border-gray-600",
        "[&>button]:bg-gray-100 [&>button]:hover:!bg-gray-100 [&>button]:dark:bg-gray-700 [&>button]:dark:hover:!bg-gray-700",
        "[&>button]:text-black [&>button]:dark:text-white",
        "[&_li>button]:gap-2",
        "lg:right-18 !important absolute right-8 top-16 flex justify-end sm:right-12 ",
      )}
    >
      <Dropdown
        key={selectedCountry}
        label={
          <SelectedCountry
            key={selectedCountry}
            selectedCountry={selectedCountry}
          />
        }
        dismissOnClick={false}
      >
        <Dropdown.Item onClick={() => changeCountry("us")}>
          <Image
            src="/images/flags/us.svg"
            alt="USA flag"
            width={28}
            height={20}
          />{" "}
          <span>USA</span>
        </Dropdown.Item>
        <Dropdown.Item onClick={() => changeCountry("ca")}>
          <Image
            src="/images/flags/ca.svg"
            alt="Canada flag"
            width={28}
            height={20}
          />{" "}
          <span>Canada</span>
        </Dropdown.Item>
      </Dropdown>
    </div>
  );
};

export const SelectedCountry = ({
  selectedCountry,
}: {
  selectedCountry: string;
}) => {
  if (selectedCountry === "ca") {
    return (
      <div className="flex items-center gap-x-2">
        <Image
          src="/images/flags/ca.svg"
          alt="Canada flag"
          width={28}
          height={20}
        />
        <span>Canada</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-x-2">
      <Image src="/images/flags/us.svg" alt="USA flag" width={28} height={20} />
      <span>USA</span>
    </div>
  );
};
