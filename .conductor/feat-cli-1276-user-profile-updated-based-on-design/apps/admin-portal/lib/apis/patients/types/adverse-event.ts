import type { ListBaseResponse } from "../../types";

export type AdverseEvent = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  title: string;
  patientId: string;
  startDate: string;
  endDate: string;
  reportedDate: string;
  severity: number;
  actionTaken: string;
  otherActions: string;
  medication: string;
  relationshipToStudyDrug: string;
  outcome: string;
  seriousness: string;
};

export type CreateAdverseEventPayload = {
  title: string;
  startDate: string;
  endDate: string;
  reportedDate: string;
  severity: number;
  actionTaken: string;
  otherActions: string;
  medication: string;
  relationshipToStudyDrug: string;
  outcome: string;
  seriousness: string;
};

export type AdverseEventListResponse = ListBaseResponse<AdverseEvent>;
