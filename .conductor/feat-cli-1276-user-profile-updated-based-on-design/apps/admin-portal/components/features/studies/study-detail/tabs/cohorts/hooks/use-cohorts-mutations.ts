import { useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { CohortPayload } from "@/lib/apis/cohorts";

import { cohortKeys } from "./use-cohorts-queries";

export const useAddCohort = () => {
  const params = useParams();
  const studyId = params.id as string;

  return useMutation({
    mutationFn: (data: CohortPayload) => api.cohorts.addCohort(data),
    onError: (error) => toast.error(error.message || "Failed to add cohort"),
    onSettled: (_, err) => !err && toast.success("Cohort added successfully"),
    meta: {
      awaits: cohortKeys.allLists(studyId),
    },
  });
};

export const useUpdateCohort = () => {
  const params = useParams();
  const studyId = params.id as string;

  return useMutation({
    mutationFn: (data: CohortPayload & { id: string }) =>
      api.cohorts.updateCohort(data),
    onError: (error) => toast.error(error.message || "Failed to update cohort"),
    onSettled: (_, err) => !err && toast.success("Cohort updated successfully"),
    meta: {
      awaits: cohortKeys.allLists(studyId),
    },
  });
};

export const useDeleteCohort = () => {
  const params = useParams();
  const studyId = params.id as string;

  return useMutation({
    mutationFn: (id: string) => api.cohorts.deleteCohort(id),
    onError: (error) => toast.error(error.message || "Failed to delete cohort"),
    onSettled: (_, err) => !err && toast.success("Cohort deleted successfully"),
    meta: {
      awaits: cohortKeys.allLists(studyId),
    },
  });
};
