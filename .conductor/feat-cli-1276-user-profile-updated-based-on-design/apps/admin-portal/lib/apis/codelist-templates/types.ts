import type { ListBaseResponse } from "@/lib/apis/types";

export type CodelistTemplateStatus = "DRAFT" | "PUBLISHED" | "ARCHIVED";

export type CodelistTemplateTermPayload = {
  submissionValue: string;
  displayLabel: string;
  definition?: string;
  displayOrder: number;
};

export type CodelistTemplateTerm = CodelistTemplateTermPayload & {
  codelistTemplateId: string;
  createdDate: string;
  id: string;
  lastUpdatedDate: string;
  synonyms: any;
  sourceCode: null | string;
};

export type CodelistTemplate = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  label: string;
  templateIdentifier: string;
  codelistId?: string;
  description?: string;
  status: CodelistTemplateStatus;
  version: number;
  templateTerms: CodelistTemplateTerm[];
  sourceCode: null | string;
  sourceOntology: null | string;
  sourceVersion: null | string;
  isExtensible: boolean;
};

export type CodelistTemplateListResponse = ListBaseResponse<CodelistTemplate>;

export type CreateCodelistTemplatePayload = {
  name: string;
  label: string;
  templateIdentifier: string;
  description?: string;
  terms: CodelistTemplateTermPayload[];
};

export type UpdateCodelistTemplatePayload = CreateCodelistTemplatePayload & {
  id: string;
};
