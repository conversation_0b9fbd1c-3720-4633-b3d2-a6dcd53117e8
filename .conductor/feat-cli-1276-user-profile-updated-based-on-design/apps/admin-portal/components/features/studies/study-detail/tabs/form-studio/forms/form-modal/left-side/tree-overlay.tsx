import { Folder as FolderIcon } from "lucide-react";

import { cn } from "@/lib/utils";

type Props = {
  folderName: string;
  questionCount?: number;
};

export const TreeOverlay = ({ folderName, questionCount }: Props) => (
  <div
    className={cn(
      "flex items-center gap-2 rounded-md border bg-white px-3 py-2 shadow-lg",
      "pointer-events-none opacity-90",
    )}
  >
    <FolderIcon className="h-5 w-5 text-purple-700" />
    <span className="text-sm font-medium">{folderName}</span>
    {typeof questionCount === "number" && (
      <span className="ml-2 rounded-full bg-purple-500 px-2 py-0.5 text-xs text-white">
        {questionCount}
      </span>
    )}
  </div>
);
