import { useDraggable } from "@dnd-kit/core";
import { ColumnDef } from "@tanstack/react-table";
import { GripVertical } from "lucide-react";

import { PillBadge } from "@/components/ui/badges/pill-badge";

import { Question } from "./use-questions-queries";

export const generateQuestionColumns = (): ColumnDef<Question>[] => [
  {
    id: "drag-handle",
    header: "",
    cell: function DragHandler({ row }) {
      const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
        id: `${row.original.id}`,
        data: {
          ...row.original,
          type: "question",
        },
      });

      return (
        <div
          ref={setNodeRef}
          {...attributes}
          {...listeners}
          className="grid cursor-grab place-content-center"
          style={{ opacity: isDragging ? 0.5 : 1 }}
        >
          <GripVertical size={20} className="text-gray-500" />
        </div>
      );
    },
    enableSorting: false,
    meta: { width: "40px" },
  },
  {
    header: "Question",
    accessorKey: "title",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span>{row.original.title}</span>
        {row.original.isRequired && (
          <span className="text-sm text-red-500">*</span>
        )}
      </div>
    ),
  },
  {
    header: "Field Type",
    accessorKey: "fieldType",
    cell: ({ row }) => (
      <PillBadge variant="default">{row.original.fieldType}</PillBadge>
    ),
  },
];
