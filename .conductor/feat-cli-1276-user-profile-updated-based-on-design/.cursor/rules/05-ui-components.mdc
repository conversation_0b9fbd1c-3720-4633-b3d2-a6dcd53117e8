---
description:
globs:
alwaysApply: true
---

# UI Components and Design System Guidelines

## Core Libraries
- Use Flowbite React as the base component library
- Use Tailwind CSS for styling
- Use clsx + tailwind-merge for conditional class names
- Use cva for component variants

## Component Structure
- Create small, reusable components
- Follow atomic design principles
- Implement proper component composition
- Use proper prop naming conventions
- Document component props with TypeScript types
- Provide sensible default props

## Styling
- Use Tailwind CSS for styling
- Follow the project's design system
- Use the theme variables for colors, spacing, etc.
- Implement responsive design for all components
- Use proper Tailwind utility classes
- Avoid custom CSS when possible
- Use Tailwind's configuration for extending the design system

## Accessibility
- Ensure all components are accessible
- Use semantic HTML elements
- Provide proper ARIA attributes
- Ensure keyboard navigation works
- Test with screen readers
- Support dark mode properly

## Forms
- Use consistent form components
- Implement proper form validation
- Show appropriate error messages
- Use proper label associations
- Implement proper form layouts
- Support all form control states (focus, error, disabled, etc.)

## Tables
- Use TanStack Table for complex tables
- Implement proper sorting and filtering
- Show appropriate loading states
- Implement proper pagination
- Handle empty states gracefully
- Support responsive tables

## Modals and Dialogs
- Use the Modal component from Flowbite
- Implement proper focus management
- Ensure keyboard accessibility
- Handle modal stacking properly
- Implement proper animations

## Tooltips and Popovers
- Use the Tooltip component from Flowbite
- Ensure proper positioning
- Handle overflow properly
- Implement proper animations
- Ensure accessibility

## Icons
- Use React Icons library
- Use consistent icon sizes
- Ensure proper accessibility for icons
- Use icons consistently across the application

## Animation
- Use framer-motion for complex animations
- Use Tailwind's transition utilities for simple animations
- Ensure animations are not distracting
- Respect user preferences for reduced motion
- Implement proper loading animations
