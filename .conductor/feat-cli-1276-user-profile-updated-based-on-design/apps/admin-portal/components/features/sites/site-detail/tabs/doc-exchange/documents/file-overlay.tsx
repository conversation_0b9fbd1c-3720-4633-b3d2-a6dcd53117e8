import { DOCUMENT_ICONS, DocumentType } from "@/components/icons/doc-icons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { DocExchangeDocument } from "@/lib/apis/doc-exchange/types";

type Props = {
  document: DocExchangeDocument;
};
const variants = {
  completed: "success",
  pending: "warning",
  failed: "danger",
} as const;

export const FileOverlay = ({ document }: Props) => {
  return (
    <div className="flex w-fit max-w-[360px] cursor-grabbing items-center gap-2 rounded-lg border border-purple-500 bg-white px-4 sm:max-w-fit md:gap-4">
      {
        DOCUMENT_ICONS[
          document?.fileRecord.extension as Exclude<DocumentType, "default">
        ]
      }
      <div className="flex min-w-0 flex-1 items-center gap-4 rounded-lg py-2 md:gap-10">
        <span className="min-w-0 flex-1 select-none truncate whitespace-nowrap text-sm font-medium leading-5">
          {document.title}
        </span>
        <PillBadge variant={variants[document.status]} className="capitalize">
          {document.status}
        </PillBadge>
      </div>
    </div>
  );
};
