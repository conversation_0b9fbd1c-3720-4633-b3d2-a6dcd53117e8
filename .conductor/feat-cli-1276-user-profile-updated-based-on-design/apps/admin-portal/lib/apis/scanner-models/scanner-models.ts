import Base<PERSON>pi from "../base";
import type { MetadataParams } from "../types";
import type {
  AddScannerModelPayload,
  ScannerModel,
  ScannerModelsListResponse,
  UpdateScannerModelPayload,
} from "./types";

export class ScannerModelsApi extends BaseApi {
  public constructor() {
    super("/scanner-models", true);
  }

  // GET: Retrieve a list of scanner models
  public async list(
    params: MetadataParams,
  ): Promise<ScannerModelsListResponse> {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<ScannerModelsListResponse>(`?${paramUrl}`);
  }

  // GET: Retrieve a single scanner model by ID
  public async get(id: string): Promise<ScannerModel> {
    return this.http.get<ScannerModel>(`/${id}`);
  }

  // POST: Create a new scanner model
  public async create(data: AddScannerModelPayload): Promise<ScannerModel> {
    return this.http.post<ScannerModel>("/", data);
  }

  // PUT: Update an existing scanner model by ID
  public async update(
    id: string,
    data: UpdateScannerModelPayload,
  ): Promise<ScannerModel> {
    return this.http.put<ScannerModel>(`/${id}`, data);
  }
}
