import { FileRecord } from "../../../models/file-record";
import type { ListBaseResponse } from "../../types";

export type Consent = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  title: string;
  consentedDate: string;
  status: string;
  consentTypeId: string;
  consentType: string;
  sourceDocumentId: string;
  sourceDocument: {
    id: string;
    fileRecordId: string;
    fileRecord?: FileRecord;
  };
  patientId: string;
  hmacKey: string;
  effectiveDate?: string;
  expiresDate?: string;
};

export type ConsentListResponse = ListBaseResponse<Consent>;

export enum ConsentTypeEnum {
  INFORMED_CONSENT_FORM = "informed_consent_form",
  ASSENT_FORM = "assent_form",
  PARENTAL_GUARDIAN_CONSENT = "parental_guardian_consent",
  RECONSENT_FORM = "re_consent_form",
  SHORT_FORM_CONSENT = "short_form_consent",
  SURROGATE_CONSENT = "surrogate_consent",
  GENETIC_CONSENT = "genetic_consent",
  DATA_SPECIMEN_USE_CONSENT = "data_specimen_use_consent",
  WITHDRAWAL_CONSENT = "withdrawal_consent",
  END_OF_STUDY_CONSENT = "end_of_study_consent",
}

export const CONSENT_TYPES = Object.values(ConsentTypeEnum);
export type ConsentType = (typeof CONSENT_TYPES)[number];

export const CONSENT_TYPE_LABELS: Record<ConsentTypeEnum, string> = {
  [ConsentTypeEnum.INFORMED_CONSENT_FORM]: "Informed Consent Form (ICF)",
  [ConsentTypeEnum.ASSENT_FORM]: "Assent Form",
  [ConsentTypeEnum.PARENTAL_GUARDIAN_CONSENT]:
    "Parental or Guardian Consent Form",
  [ConsentTypeEnum.RECONSENT_FORM]: "Re-Consent Form",
  [ConsentTypeEnum.SHORT_FORM_CONSENT]: "Short Form Consent",
  [ConsentTypeEnum.SURROGATE_CONSENT]: "Surrogate Consent Form",
  [ConsentTypeEnum.GENETIC_CONSENT]: "Genetic Consent Form",
  [ConsentTypeEnum.DATA_SPECIMEN_USE_CONSENT]: "Data and Specimen Use Consent",
  [ConsentTypeEnum.WITHDRAWAL_CONSENT]: "Withdrawal of Consent Form",
  [ConsentTypeEnum.END_OF_STUDY_CONSENT]: "End-of-Study Consent Form",
};

export type ConsentSignedUrlUploadPayload = {
  title: string;
  consentedDate: string;
  expiresDate: string | null;
  consentType: string;
  status: string;
  originationType: string;
  type: string;
  extension: string;
};

export type UpdateConsentPayload = Partial<ConsentSignedUrlUploadPayload>;

export type ConsentSignedUploadResponse = {
  consent: Consent;
  uploadUrl: string;
};
