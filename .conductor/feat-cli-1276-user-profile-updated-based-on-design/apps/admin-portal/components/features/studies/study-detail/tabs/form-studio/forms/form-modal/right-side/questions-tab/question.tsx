import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  Accordion,
  AccordionContent,
  AccordionPanel,
  AccordionTitle,
} from "flowbite-react";
import { Copy, GripVertical } from "lucide-react";

import { Tabs, TabsItem } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

import { Field } from "./field";

const accordionTheme = {
  root: {
    base: "divide-y-2 border-2 divide-gray-200 border-gray-200 dark:divide-gray-600 dark:border-gray-600",
  },
  content: {
    base: "first:rounded-t-lg last:rounded-b-lg dark:bg-gray-700",
  },
  title: {
    base: "flex w-full gap-2 items-center justify-between py-5 px-2.5 text-left font-medium text-gray-500 first:rounded-t-lg last:rounded-b-lg dark:text-gray-400",
    flush: {
      off: "hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:hover:bg-gray-600 dark:focus:ring-gray-600",
      on: "bg-transparent dark:bg-transparent",
    },
    open: {
      off: "",
      on: "bg-gray-100 text-gray-900 dark:bg-gray-600 dark:text-white",
    },
    heading: "flex flex-1 items-center gap-2",
  },
};

type Props = {
  id: string;
};

export const Question = ({ id }: Props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: transform ? CSS.Translate.toString(transform) : undefined,
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "rounded-xl border border-gray-200 p-4",
        isDragging && "z-50 opacity-50",
      )}
    >
      <Accordion collapseAll theme={accordionTheme}>
        <AccordionPanel theme={accordionTheme}>
          <AccordionTitle theme={accordionTheme.title}>
            <span
              className="cursor-grab rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
              {...attributes}
              {...listeners}
            >
              <GripVertical />
            </span>
            What is Flowbite?
            <span className="bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200 ml-auto ml-auto rounded-full px-3 py-1 text-sm">
              Single
            </span>
          </AccordionTitle>
          <AccordionContent theme={accordionTheme.content}>
            <Tabs
              theme={{
                tabpanel: "py-0",
              }}
              className="gap-0 [&>button]:flex-1 [&_button[role=tab]]:py-2.5"
            >
              <TabsItem title={"Question"}>
                <div className="p-4">
                  <div>
                    <span className="text-xs">Question ID</span>
                    <div className="flex items-center gap-2.5">
                      <span className="py-2.5 pl-3">
                        form002.Q_studyidentifier
                      </span>
                      <button>
                        <Copy />
                      </button>
                    </div>
                  </div>
                </div>
              </TabsItem>
              <TabsItem title={"Fields"}>
                <div className="space-y-5 p-4">
                  <Field />
                </div>
              </TabsItem>
            </Tabs>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
    </div>
  );
};
