"use client";

import { Card } from "flowbite-react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableLoading } from "@/components/ui/table";

import { columns, Form } from "./columns";
import FormModal from "./form-modal/form-modal";

export const FormsTab = () => {
  // Mock data - replace with actual data fetching
  const forms: Form[] = [
    {
      id: "1",
      name: "Informed Consent Form",
      type: "EDC",
      status: "Published",
      editedBy: "Dr. <PERSON>",
      lastEdited: "2 days ago",
      attachedTo: ["Visit 1", "Screening"],
      description: "Primary consent form for study participation",
      version: "v2.1",
    },
    {
      id: "2",
      name: "Adverse Event Report",
      type: "eSource",
      status: "Draft",
      editedBy: "Dr. <PERSON>",
      lastEdited: "1 week ago",
      attachedTo: ["Unscheduled Visit"],
      description: "Form for reporting adverse events during study",
      version: "v1.3",
    },
    {
      id: "3",
      name: "Patient Demographics",
      type: "EDC",
      status: "Published",
      editedBy: "Dr. <PERSON>",
      lastEdited: "3 days ago",
      attachedTo: ["Visit 1", "Baseline"],
      description: "Baseline demographic information collection",
      version: "v1.0",
    },
    {
      id: "4",
      name: "Quality of Life Survey",
      type: "eSource",
      status: "Draft",
      editedBy: "Dr. Robert Wilson",
      lastEdited: "5 days ago",
      attachedTo: ["Visit 2", "Visit 4", "V1", "Follow-up"],
      description: "Patient-reported quality of life assessment",
      version: "v1.8",
    },
    {
      id: "5",
      name: "Medical History Form",
      type: "EDC",
      status: "Published",
      editedBy: "Dr. Lisa Anderson",
      lastEdited: "1 day ago",
      attachedTo: ["Screening"],
      description: "Comprehensive medical history questionnaire",
      version: "v2.0",
    },
  ];

  const isPending = false; // Replace with actual loading state
  const isPlaceholderData = false; // Replace with actual placeholder state

  return (
    <>
      <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Forms
          </h3>
          <Button variant="primary">
            <IoMdAdd className="h-4 w-4" /> New Form
          </Button>
        </div>

        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={forms} />
          </LoadingWrapper>
        )}
      </Card>
      <FormModal />
    </>
  );
};
