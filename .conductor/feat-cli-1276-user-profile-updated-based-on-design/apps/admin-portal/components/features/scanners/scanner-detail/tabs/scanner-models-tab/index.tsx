import { Card } from "flowbite-react";
import { useState } from "react";

import { useScannerModel } from "@/components/features/scanner-models/default/hooks/use-scanner-model";
import { ModalScannerModel } from "@/components/features/scanner-models/modal-scanner-models";
import { Table, TableLoading } from "@/components/ui/table";
import type { ScannerModel } from "@/lib/apis/scanner-models";

import { generateScannerModelColumns } from "../columns/scanner-models";

type ScannerModelsTabProps = {
  modelId: string | undefined;
};

export const ScannerModelsTab = ({ modelId }: ScannerModelsTabProps) => {
  const { data: scannerModels, isLoading } = useScannerModel(modelId as string);
  const [selectedModel, setSelectedModel] = useState<ScannerModel | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const columns = generateScannerModelColumns((model) => {
    setSelectedModel(model);
    setIsModalOpen(true);
  });

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedModel(null);
  };

  if (isLoading) {
    return (
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Scanner Model</div>
        </div>
        <TableLoading columns={columns} />
      </Card>
    );
  }

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Scanner Model</div>
        </div>
        <Table columns={columns} data={scannerModels ? [scannerModels] : []} />
      </Card>

      {isModalOpen && (
        <ModalScannerModel
          isOpen={isModalOpen}
          onClose={handleModalClose}
          model={selectedModel}
        />
      )}
    </>
  );
};
