import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddStudyToGroupPayload } from "@/lib/apis/groups/types";

import { USE_GROUP_STUDIES_QUERY_KEY } from "./use-group-studies";

export function useAddStudyToGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: AddStudyToGroupPayload) =>
      api.groups.addStudyToGroup(payload),
    onSuccess: (_, { groupId }) => {
      toast.success("Study added to group successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_GROUP_STUDIES_QUERY_KEY, groupId],
      });
    },
    onError: (error) => {
      toast.error(error.message || "Failed to add study to group");
    },
  });
}
