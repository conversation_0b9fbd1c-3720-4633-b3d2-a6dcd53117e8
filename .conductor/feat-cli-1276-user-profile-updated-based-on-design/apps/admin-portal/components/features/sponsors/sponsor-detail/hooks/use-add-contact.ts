import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddSponsorContactPayload } from "@/lib/apis/sponsors/types";

import { USE_SPONSOR_CONTACTS } from "./use-contacts";

export const useAddSponsorContact = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: AddSponsorContactPayload & { sponsorId: string }) => {
      const { sponsorId, ...rest } = payload;
      return api.sponsors.addContact(sponsorId, rest);
    },
    onSuccess: () => {
      toast.success("Contact added successfully");
      queryClient.invalidateQueries({ queryKey: [USE_SPONSOR_CONTACTS] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
