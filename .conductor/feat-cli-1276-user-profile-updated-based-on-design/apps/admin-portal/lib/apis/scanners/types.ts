import type { ScannerApplication } from "../scanner-applications/types";
import type { ScannerModel } from "../scanner-models/types";
import type { ListBaseResponse } from "../types";

export type Scanner = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  displayName: string;
  description: string | null;
  isActive: boolean;
  siteId: string;
  assignmentId: string;
  model: ScannerModel;
  currentVersion: ScannerApplication | null;
  targetVersion: ScannerApplication | null;
  diagnostics: string | null;
  lastUpdate: string | null;
  rebootFrequency: number | null;
  rebootHour: number | null;
  rebootMinute: number | null;
  lastReboot: string | null;
  timeZone: string;
  activatedDate: string | null;
  macAddress: string | null;
  ipAddress: string | null;
  isWifi: boolean | null;
  deviceId: string | null;
  updateFrequency?: number | null;
};

export type ScannersListResponse = ListBaseResponse<Scanner>;

export type AddScannerPayload = {
  displayName: string;
  description?: string;
  isActive?: boolean;
  siteId?: string;
  assignmentId?: string;
  modelId?: string;
  currentVersionId?: string;
  targetVersionId?: string;
  rebootFrequency?: number;
  rebootHour?: number;
  rebootMinute?: number;
  updateFrequency?: number;
};

export type UpdateScannerPayload = Partial<AddScannerPayload>;

export type ScannerVersionResponse = {
  id: string;
  currentVersion?: ScannerApplication;
  targetVersion?: ScannerApplication;
};

export type FleetOverviewScanner = {
  displayName: string;
  siteName: string;
  modelName: string;
  ipAddress: string;
  isActive: boolean;
  lastCheckIn: string;
  currentAppVersion: string;
  targetAppVersion: string;
  id: string;
};

export type FleetOverviewResponse = ListBaseResponse<FleetOverviewScanner>;

export type AppVersionDistribution = {
  versionNumber: string;
  count: number;
};

export type AppVersionsBarResponse = AppVersionDistribution[];

export type ScannerDiagnosticsResponse = {
  scannerId: string;
  diagnostics: Record<string, any>;
};
