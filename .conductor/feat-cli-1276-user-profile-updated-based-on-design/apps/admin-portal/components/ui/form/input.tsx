"use client";
import type { TextInputProps } from "flowbite-react";
import { TextInput } from "flowbite-react";
import { forwardRef } from "react";
import { Controller, get, useFormContext } from "react-hook-form";
import type { Merge, SetRequired } from "type-fest";

import { cn } from "@/lib/utils";

export type InputFieldProps = Merge<
  SetRequired<TextInputProps, "name">,
  {
    shouldShowError?: boolean;
    positiveNumbersOnly?: boolean;
  }
>;

const InputField: React.ForwardRefExoticComponent<
  InputFieldProps & React.RefAttributes<HTMLInputElement>
> = forwardRef<HTMLInputElement, InputFieldProps>(
  (
    { name, shouldShowError = true, positiveNumbersOnly = false, ...props },
    ref,
  ) => {
    const { control } = useFormContext();

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorObj = get(errors, name);
          const errorMessage = errorObj?.message?.valueOf();
          const hasError = typeof errorMessage === "string";
          const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            if (positiveNumbersOnly) {
              if (e.target.type === "number") {
                const value = parseFloat(e.target.value);
                if (!isNaN(value) && value < 0) {
                  e.target.value = "0";
                }
              } else if (/^-\d*\.?\d*$/.test(e.target.value)) {
                e.target.value = e.target.value.replace("-", "");
              }
            }
            field.onChange(e);
          };

          return (
            <div>
              <TextInput
                {...field}
                {...props}
                ref={ref}
                onChange={handleChange}
                min={
                  positiveNumbersOnly && props.type === "number" ? 0 : undefined
                }
                className={cn(
                  // "read-only:[&_input]:bg-gray-200 dark:read-only:[&_input]:bg-gray-500",
                  props.className,
                  hasError &&
                    "[&_input]:!border-red-500 [&_input]:!ring-red-500",
                )}
              />
              {hasError && shouldShowError && (
                <span className="text-sm text-red-500">{errorMessage}</span>
              )}
            </div>
          );
        }}
      />
    );
  },
);

InputField.displayName = "InputField";
export { InputField };
