import type { ListBaseResponse } from "../types";

export type ScannerModel = {
  id: string;
  companyName: string;
  modelName: string;
  description: string | null;
};

export type ScannerModelsListResponse = ListBaseResponse<ScannerModel>;

export type AddScannerModelPayload = {
  companyName: string;
  modelName: string;
  description?: string;
};

export type UpdateScannerModelPayload = Partial<AddScannerModelPayload>;
