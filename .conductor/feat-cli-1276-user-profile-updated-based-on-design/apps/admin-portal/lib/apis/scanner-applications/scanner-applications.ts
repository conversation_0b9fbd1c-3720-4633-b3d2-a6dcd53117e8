import <PERSON><PERSON><PERSON> from "../base";
import type { MetadataParams } from "../types";
import type {
  AddScannerApplicationPayload,
  ScannerApplication,
  ScannerApplicationGetUploadApkPayload,
  ScannerApplicationsListResponse,
  UpdateScannerApplicationPayload,
} from "./types";

export class ScannerApplicationsApi extends BaseApi {
  public constructor() {
    super("/scanner-applications", true);
  }

  // GET: Retrieve a list of scanner applications
  public async list(
    params: MetadataParams,
  ): Promise<ScannerApplicationsListResponse> {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<ScannerApplicationsListResponse>(`?${paramUrl}`);
  }

  // GET: Retrieve a single scanner application by ID
  public async get(id: string): Promise<ScannerApplication> {
    return this.http.get<ScannerApplication>(`/${id}`);
  }

  // POST: Create a new scanner application
  public async create(
    data: AddScannerApplicationPayload,
  ): Promise<ScannerApplication> {
    return this.http.post<ScannerApplication>("/", data);
  }

  // PUT: Update an existing scanner application by ID
  public async update(
    id: string,
    data: UpdateScannerApplicationPayload,
  ): Promise<ScannerApplication> {
    return this.http.put<ScannerApplication>(`/${id}`, data);
  }
  public async getUploadApk(
    id: string,
    payload: ScannerApplicationGetUploadApkPayload,
  ): Promise<{ url: string }> {
    return this.http.post<{ url: string }>(
      `/${id}/apk-signed-url-upload`,
      payload,
    );
  }

  public async downloadApk(id: string) {
    return this.http.get<{ url: string }>(`/${id}/apk-signed-url-download`);
  }
}
