import BaseApi from "../base";
import type { MetadataParams } from "../types";
import type {
  CreateFolderPayload,
  EbinderDocumentListResponse,
  Folder,
  FolderBinderListResponse,
  MoveFolderPayload,
  UpdateFolderPayload,
} from "./types";

class IsfFoldersApi extends BaseApi {
  constructor() {
    super("/isf-folders", true);
  }

  public async folders(studyId: string, siteId: string) {
    return this.http.get<Folder[]>(`/study/${studyId}/site/${siteId}`);
  }

  public async folder(id: string) {
    return this.http.get<Folder>(`/${id}`);
  }
  public async createFolder(payload: CreateFolderPayload) {
    return this.http.post<Folder>("/", payload);
  }

  public async moveFolder(payload: MoveFolderPayload) {
    return this.http.patch<Folder>(`/${payload.folderId}/move`, payload);
  }

  public async updateFolder(id: string, payload: UpdateFolderPayload) {
    return this.http.patch<Folder>(`/${id}/name`, payload);
  }

  public async folderBinders(id: string, params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<FolderBinderListResponse>(
      `/${id}/binders?${paramUrl}`,
    );
  }

  public async ebinderDocuments(id: string, params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<EbinderDocumentListResponse>(
      `/${id}/binders?${paramUrl}`,
    );
  }

  public async deleteFolder(id: string) {
    return this.http.delete(`/${id}`);
  }
}

export const isfFolders = new IsfFoldersApi();

export * from "./types";
