import { capitalize } from "lodash";
import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { TASK_STATUSES } from "@/components/ui/badges/task-status-badge";
import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, FormActions, Select } from "@/components/ui/form";
import { DateRangePicker } from "@/components/ui/form/date-picker/date-range-picker";
import { Label } from "@/components/ui/form/label";
import { cn } from "@/lib/utils";

import { useTaskFilter } from "./hooks/use-task-filter";

const TASK_PRIORITIES = ["high", "medium", "low"] as const;

const schema = z.object({
  status: z.string().optional(),
  priority: z.string().optional(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
});

export const TaskFilters = () => {
  const [open, setOpen] = useState(false);

  return (
    <Dropdown open={open} onOpenChange={setOpen} placement="bottom-end">
      <DropdownTrigger>
        <Button variant="outline">
          <div className="flex items-center justify-center gap-x-2">
            <ListFilter className="size-5" />
            <div>Filter</div>
          </div>
        </Button>
      </DropdownTrigger>
      <DropdownContent className="z-10 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <FilterContent setOpen={setOpen} />
      </DropdownContent>
    </Dropdown>
  );
};

const FilterContent = ({ setOpen }: { setOpen: (open: boolean) => void }) => {
  const { status, setStatus, priority, setPriority, from, setFrom, to, setTo } =
    useTaskFilter();

  const formRef = useRef<FormActions<z.ZodType<z.infer<typeof schema>>>>(null);

  function onSubmit(data: z.infer<typeof schema>) {
    setStatus(data.status || null);
    setPriority(data.priority || null);

    setFrom(
      data.dateRange?.from ? data.dateRange.from.toLocaleDateString() : null,
    );
    setTo(data.dateRange?.to ? data.dateRange.to.toLocaleDateString() : null);

    setOpen(false);
  }

  function onClear() {
    setStatus(null);
    setPriority(null);
    setFrom(null);
    setTo(null);
    formRef.current?.formHandler?.reset();
  }

  const dateRange =
    from || to
      ? {
          from: from ? new Date(from) : undefined,
          to: to ? new Date(to) : undefined,
        }
      : undefined;

  const defaultValues = {
    status: status ?? "",
    priority: priority ?? "",
    dateRange,
  };

  const hasFilters = !!status || !!priority || !!from || !!to;

  return (
    <div className="">
      <Form
        schema={schema}
        mode="onChange"
        onSubmit={onSubmit}
        className="min-w-96"
        ref={formRef}
        defaultValues={defaultValues}
      >
        <div className="flex flex-col divide-y">
          {/* Status */}
          <div className="px-[15px] pb-5 pt-2.5">
            <Label
              htmlFor="status"
              className="mb-2 block text-base font-normal leading-6 text-gray-700"
            >
              Status
            </Label>
            <Select
              id="status"
              name="status"
              className="h-fit w-full"
              options={TASK_STATUSES.map((status) => ({
                label: status.label,
                value: status.value,
              }))}
              placeholder="Select Status"
            />
          </div>

          {/* Priority */}
          <div className="px-[15px] pb-5 pt-2.5">
            <Label
              htmlFor="priority"
              className="mb-2 block text-base font-normal leading-6 text-gray-700"
            >
              Priority
            </Label>
            <Select
              id="priority"
              name="priority"
              className="h-fit w-full"
              options={TASK_PRIORITIES.map((priority) => ({
                label: capitalize(priority),
                value: priority,
              }))}
              placeholder="Select Priority"
            />
          </div>

          {/* Due Date Range */}
          <div className="px-[15px] pb-5 pt-2.5">
            <Label
              htmlFor="dateRange"
              className="mb-2 block text-base font-normal leading-6 text-gray-700"
            >
              Due Date
            </Label>
            <DateRangePicker
              name="dateRange"
              placeholder="Select due date range"
            />
          </div>
        </div>

        <div className="flex justify-end gap-5 px-[15px] pb-5 pt-2.5">
          <Button
            variant="outline"
            className={cn(
              "w-full justify-center",
              !hasFilters && "pointer-events-none invisible",
            )}
            onClick={() => {
              onClear();
            }}
            type="button"
          >
            Clear Filters
          </Button>
          <Button
            variant="primary"
            type="submit"
            className="w-full justify-center"
          >
            Apply Filters
          </Button>
        </div>
      </Form>
    </div>
  );
};
