import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { theme, Tooltip } from "flowbite-react";
import {
  Eye,
  PenLine,
  Save,
  Settings,
  ShieldQuestion,
  Variable,
  X,
} from "lucide-react";
import React from "react";
import { FormProvider, useForm } from "react-hook-form";
import { twMerge } from "tailwind-merge";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import { Tabs, TabsItem } from "@/components/ui/tabs";

import { LogicTab } from "./logic-tab/logic-tab";
import { QuestionsTab } from "./questions-tab/questions-tab";
import { SettingsTab } from "./settings-tab/settings-tab";

const formSchema = z.object({
  title: z
    .string({ required_error: "Form name is required" })
    .min(1, "Form name is required"),
  description: z.string().optional(),
  folderIds: z.array(z.string()).optional(),
});

type FormData = z.infer<typeof formSchema>;

const FORM_TABS = [
  {
    title: "Settings",
    icon: Settings,
    component: <SettingsTab />,
  },
  {
    title: "Questions",
    icon: ShieldQuestion,
    component: <QuestionsTab />,
  },
  {
    title: "Logic",
    icon: Variable,
    component: <LogicTab />,
  },
] as const;

export const FormBuilder = () => {
  const methods = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "Untitled form",
      description: "",
      folderIds: [],
    },
  });

  const { watch } = methods;
  const formTitle = watch("title");
  const formDescription = watch("description");

  return (
    <FormProvider {...methods}>
      <div className="flex h-full flex-col rounded-lg border border-gray-300 bg-gray-50 dark:border-gray-500 dark:bg-gray-800">
        <div className="p-4">
          <div className="flex items-center justify-between gap-4">
            <h3 className="text-xl font-bold sm:text-2xl lg:text-3xl dark:text-white">
              {formTitle || "Untitled form"}
            </h3>
            <button className="rounded-full bg-blue-500 p-2.5 text-white hover:bg-blue-600">
              <X size={20} />
            </button>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            {formDescription || "Design and configure your form"}
          </p>
        </div>

        <div className="grid min-h-0 flex-1 grid-rows-[1fr] ">
          <Tabs
            theme={{
              tabpanel: "h-full py-0",
              tabitemcontainer: {
                base: "min-h-0 flex-1",
              },
            }}
            className="min-h-0 flex-1 gap-0 [&>:first-child]:flex-none [&_button[role=tab]]:w-[200px]"
          >
            {FORM_TABS.map((tab) => (
              <TabsItem
                key={tab.title.toLocaleLowerCase()}
                icon={tab.icon}
                title={tab.title}
              >
                <div className="h-full overflow-hidden p-4">
                  {tab.component}
                </div>
              </TabsItem>
            ))}
          </Tabs>
        </div>

        <div className="flex items-center justify-between border-t border-gray-300 p-4">
          <Tooltip content="Preview">
            <Button className="!p-2.5 [&_span]:!p-0" variant="outline">
              <Eye size={24} />
            </Button>
          </Tooltip>

          <div className="flex gap-2">
            <Button variant="outline" color="gray">
              Cancel
            </Button>
            <Button variant="outline">
              <PenLine /> Save as Draft
            </Button>
            <Button variant="primary">
              <Save /> Save
            </Button>
          </div>
        </div>
      </div>
    </FormProvider>
  );
};
