import BaseApi from "../../base";
import type { IsfTemplateProtocolList } from "./types";

class IsfTemplateProtocols extends BaseApi {
  constructor() {
    super("/protocol-isf-templates", true);
  }

  // : Promise<StudyListResponse>
  public async getExistingTemplates(): Promise<IsfTemplateProtocolList> {
    // public async list(params: MetadataParams): Promise<StudyListResponse> {

    return this.http.get<IsfTemplateProtocolList>(``);
  }

  public async buildFromExisting(
    id: string,
    siteId: string,
    studyId: string,
  ): Promise<{ url: string }> {
    return this.http.post<{ url: string }>(
      `/build/isf/${id}/site/${siteId}/study/${studyId}`,
    );
  }
  public async uploadFromJson(
    siteId: string,
    studyId: string,
    file: Blob,
    name: string,
  ): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append("fileType", file.type);
    formData.append("extension", ".json");
    formData.append("file", file);
    formData.append("name", name);

    return this.http.post<{ url: string }>(
      `/upload/site/${siteId}/study/${studyId}`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data", // Use FormData for file uploads
        },
      },
    );
  }
}

export const IsfTemplateProtocolApi = new IsfTemplateProtocols();
export * from "./types";
