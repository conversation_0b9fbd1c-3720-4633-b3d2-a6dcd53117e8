"use client";
import { Card } from "flowbite-react";
import React, { useMemo, useState } from "react";
import { HiPlus } from "react-icons/hi";

import { <PERSON>Field } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { ISFRefModel } from "@/lib/apis/isf-ref-models";

import { generateISFColumns } from "./columns";
import { useDeleteISFRefModel } from "./hooks/use-isf-ref-model-mutations";
import { useISFRefModels } from "./hooks/use-isf-ref-model-queries";
import { ISFModal } from "./isf-modal";

export const ISFReferenceModelsTab = () => {
  const { isOpen, close, open } = useDisclosure();

  const [selectedISFModel, setSelectedISFModel] = useState<ISFRefModel | null>(
    null,
  );
  const { data, isPending, isPlaceholderData } = useISFRefModels();
  const { mutate, isPending: isDeleting } = useDeleteISFRefModel();
  const handleCloseModal = () => {
    close();
    setSelectedISFModel(null);
  };

  const columns = useMemo(
    () =>
      generateISFColumns({
        onView: (data) => {
          setSelectedISFModel(data);
          open();
        },
      }),
    [open],
  );

  return (
    <>
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          ISF Reference Models
        </h2>
      </div>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-between gap-4 p-4">
          <div>
            <SearchField placeholder="Search..." />
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData || isDeleting}>
              <TableData data={data?.results ?? []} columns={columns} />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
      <ISFModal
        isOpen={isOpen}
        onClose={handleCloseModal}
        selectedISFModel={selectedISFModel}
      />
    </>
  );
};
