import { ActivityType } from "@/components/ui/badges/activity-badge";

import { EbinderDocument } from "../isf-folders";
import { ListBaseResponse } from "../types";

export type EbinderUploadPlaceholderPayload = {
  id: string;
  extension: string;
  fileType: string;
  originationType: string;
  comment?: string;
};

export type EbinderUploadPlaceholderResponse = {
  id: string;
  uploadUrl: string;
};

export type EBinderActivity = {
  id: string;
  action: ActivityType;
  createdDate: string;
  lastUpdatedDate: string;
  profile?: {
    id: string;
    name: string;
    user?: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
};

export type EBinderActivityResponse = ListBaseResponse<EBinderActivity>;

export type EBinderDocumentVersion = EbinderDocument["currentVersion"];
