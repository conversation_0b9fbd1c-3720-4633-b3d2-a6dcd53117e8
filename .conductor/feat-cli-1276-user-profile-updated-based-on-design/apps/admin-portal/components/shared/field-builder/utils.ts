import { z } from "zod";

import { Codelist } from "@/lib/apis/codelist-definitions/types";
import { CodelistTemplate } from "@/lib/apis/codelist-templates/types";

export type NormalizedCodelistTerm = {
  id: string;
  displayLabel: string;
  submissionValue: string;
};

export type NormalizedCodelist = {
  id: string;
  name: string;
  label: string;
  terms: NormalizedCodelistTerm[];
};

export const normalizeCodelist = (
  codelist: Codelist | CodelistTemplate,
): NormalizedCodelist => {
  if ("templateIdentifier" in codelist) {
    // TypeScript knows this is CodelistTemplate
    return {
      id: codelist.id,
      name: codelist.name,
      label: codelist.label,
      terms: codelist.templateTerms.map((term) => ({
        id: term.id,
        displayLabel: term.displayLabel,
        submissionValue: term.submissionValue,
      })),
    };
  } else {
    // TypeScript knows this is Codelist
    return {
      id: codelist.id,
      name: codelist.name,
      label: codelist.label,
      terms: codelist.terms.map((term) => ({
        id: term.id,
        displayLabel: term.displayLabel,
        submissionValue: term.submissionValue,
      })),
    };
  }
};

export const createErrorMessages = (label: string) => ({
  invalid_type_error: `${label} is required`,
  required_error: `${label} is required`,
});

export const createStringSchema = (
  label: string,
  config?: { maxLength?: number },
) => {
  let schema = z
    .string(createErrorMessages(label))
    .min(1, `${label} is required`);

  if (config?.maxLength) {
    schema = schema.max(
      config.maxLength,
      `Maximum ${config.maxLength} characters allowed`,
    );
  }

  return schema;
};

export const createNumberSchema = (
  label: string,
  config?: { min?: number; max?: number },
) => {
  let schema = z.number(createErrorMessages(label));

  if (typeof config?.min === "number") {
    schema = schema.min(config.min, `Minimum value is ${config.min}`);
  }

  if (typeof config?.max === "number") {
    schema = schema.max(config.max, `Maximum value is ${config.max}`);
  }

  return schema;
};

export const createDateSchema = (label: string) => {
  return z.date(createErrorMessages(label));
};

export const createArraySchema = (label: string) => {
  return z.array(z.string()).min(1, "Please select at least one option");
};

export const createPlaceholder = (label: string, action = "Enter") =>
  `${action} ${label.toLowerCase()}...`;

export const createLayoutClass = (direction?: string) =>
  `flex gap-4 ${direction === "vertical" ? "flex-col" : "flex-row"}`;
