import { Card } from "flowbite-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useMemo, useState } from "react";
import { Hi<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HiX } from "react-icons/hi";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { But<PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Prompt } from "@/lib/apis/app-prompts";

import { CategorizationModal } from "./categorization-modal";
import { generatePromptColumns } from "./columns";
import { ConfirmDeleteModal } from "./confirm-delete-modal";
import { FilterModal } from "./filter-modal";
import { useFilterPrompt } from "./hooks/use-filter-prompts";
import { useActivatePrompt } from "./hooks/use-protocol-prompt-mutations";
import { usePromptTemplates } from "./hooks/use-protocol-prompt-query";
import PromptModal from "./prompt-modal";
export type ExtendedPrompt = Prompt & {
  isEditing?: boolean;
};

const PromptTemplateTab = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { active, name, version, model, key, modelProvider } =
    useFilterPrompt();

  const {
    isOpen: isOpenFilterModal,
    close: onCloseFilterModal,
    open: onOpenFilterModal,
  } = useDisclosure();

  const {
    isOpen: isOpenPromptModal,
    close: onClosePromptModal,
    open: onOpenPromptModal,
  } = useDisclosure();

  const {
    isOpen: isOpenConfirmModal,
    close: onCloseConfirmModal,
    open: onOpenConfirmModal,
  } = useDisclosure();

  const {
    isOpen: isOpenTestModal,
    close: onCloseTestModal,
    open: onOpenTestModal,
  } = useDisclosure();

  const [selectedPrompt, setSelectedPrompt] = useState<ExtendedPrompt | null>(
    null,
  );

  const { data, isPending, isPlaceholderData } = usePromptTemplates();

  const { mutate: activatePrompt, isPending: isActivating } =
    useActivatePrompt();

  const handleCloseModal = () => {
    onClosePromptModal();
    setSelectedPrompt(null);
  };

  const handleCloseConfirmModal = () => {
    onCloseConfirmModal();
    setSelectedPrompt(null);
  };

  const columns = useMemo(
    () =>
      generatePromptColumns({
        onActivate: (data) => {
          activatePrompt(data.id);
        },
        onTest: () => {
          onOpenTestModal();
        },
        onView: (data) => {
          setSelectedPrompt({ ...data, isEditing: true });
          onOpenPromptModal();
        },
      }),
    [activatePrompt, onOpenPromptModal, onOpenTestModal],
  );

  const appliedFilter = [
    { label: "Version", value: version, key: "version" },
    { label: "Model", value: model, key: "model" },
    { label: "Model Provider", value: modelProvider, key: "provider" },
    { label: "Key", value: key, key: "key" },
    { label: "Active", value: active, key: "active" },
    { label: "Name", value: name, key: "name" },
  ];

  const handleRemoveAFilter = (key: string) => {
    const currentParams = new URLSearchParams(
      Array.from(searchParams.entries()),
    );

    currentParams.delete(key);
    currentParams.set("page", "1");
    const query = currentParams ? `?${currentParams.toString()}` : "";
    router.push(`${pathname}${query}`);
  };
  const renderTable = () => {
    if (isPending) return <TableLoading columns={columns} />;
    return (
      <>
        <LoadingWrapper isLoading={isPlaceholderData || isActivating}>
          <TableData data={data?.results ?? []} columns={columns} />
        </LoadingWrapper>
        {data?.metadata && <TableDataPagination metadata={data.metadata} />}
      </>
    );
  };
  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="space-y-2 p-4">
          <div className="flex flex-col items-start justify-between gap-4  sm:flex-row sm:items-center">
            <Button
              onClick={onOpenFilterModal}
              variant="outline"
              className=" w-fit"
            >
              <HiFilter />
              <span className="relative">Filter</span>
            </Button>
          </div>
          <div className="flex max-w-full flex-wrap items-center gap-2 overflow-hidden">
            {appliedFilter.map((filter) => {
              if (!filter.value) return null;
              return (
                <div
                  key={filter.key}
                  className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                >
                  <span className="max-w-[160px] truncate">
                    {filter.label} :{" "}
                    {typeof filter.value === "boolean" ? "Yes" : filter.value}
                  </span>
                  <button
                    onClick={() => handleRemoveAFilter(filter.key)}
                    className="ml-1 flex-shrink-0 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    <HiX className="h-4 w-4" />
                  </button>
                </div>
              );
            })}
          </div>
        </div>
        {renderTable()}
      </Card>
      {isOpenFilterModal && (
        <FilterModal isOpen={isOpenFilterModal} onClose={onCloseFilterModal} />
      )}
      {isOpenPromptModal && (
        <PromptModal
          selectedPrompt={selectedPrompt}
          isOpen={isOpenPromptModal}
          onClose={handleCloseModal}
        />
      )}
      {isOpenConfirmModal && (
        <ConfirmDeleteModal
          onClose={handleCloseConfirmModal}
          isOpen={isOpenConfirmModal}
          selectedPrompt={selectedPrompt as ExtendedPrompt}
        />
      )}
      <CategorizationModal
        isOpen={isOpenTestModal}
        onClose={onCloseTestModal}
      />
    </>
  );
};

export default PromptTemplateTab;
