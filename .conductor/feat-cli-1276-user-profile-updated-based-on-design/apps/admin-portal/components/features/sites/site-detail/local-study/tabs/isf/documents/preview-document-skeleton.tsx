import { <PERSON><PERSON>, <PERSON><PERSON> } from "flowbite-react";
import React from "react";

import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

const TAB_WIDTH = {
  1: "sm:w-28 w-[90px]",
  2: "sm:w-20 w-[60px]",
  3: "sm:w-32 w-[108px]",
};

export const PreviewDocumentSkeleton = () => {
  return (
    <Modal size="7xl" show={true}>
      <Modal.Header className="border-b-0 !py-0 [&>button]:hidden [&>h3]:w-full">
        <div className="flex flex-col  justify-between gap-y-4 pt-3 lg:flex-row">
          <div className="order-2 flex items-center gap-5 lg:order-1">
            <Skeleton className="h-7 w-14 rounded-2xl" />
            <p className="flex gap-3">
              <Skeleton className="size-5" />
              <Skeleton className="h-5 w-40" />
            </p>
          </div>

          <div className="flex flex-1 flex-col justify-between sm:flex-row">
            <div className="order-2 flex gap-2 sm:order-1">
              {([1, 2, 3] as const).map((tab) => (
                <button
                  key={tab}
                  className={cn(
                    "cursor-pointer border-b-4 border-transparent px-2 py-2.5 text-sm font-medium capitalize leading-5 sm:px-5",
                    tab === 1 &&
                      "text-primary-500 border-primary-500 font-semibold",
                  )}
                >
                  <Skeleton className={cn("h-5", TAB_WIDTH[tab])} />
                </button>
              ))}
            </div>
            <div className="order-1 flex items-center justify-end gap-3 sm:order-2">
              <Skeleton className="size-11" />

              <Skeleton className="size-6" />
            </div>
          </div>
        </div>
      </Modal.Header>
      <Modal.Body className="grid max-h-[70vh] min-h-[70vh] grid-rows-1 gap-x-2 overflow-hidden border-none p-4 lg:grid-cols-2">
        <div className="hidden h-full w-full place-content-center overflow-y-auto lg:grid">
          <Spinner size="xl" color="blue" className="fill-primary-500" />
        </div>
        <div className="overflow-y-auto rounded-lg border dark:border-gray-400">
          <div className="flex flex-col gap-2.5 rounded-lg p-4 sm:p-6">
            <div className="space-y-2.5">
              <div className="flex items-center justify-between gap-3">
                <Skeleton className="h-8 w-44" />
                <Skeleton className="h-6 w-40" />
              </div>

              <div className="flex items-center justify-end gap-2">
                <Skeleton className="h-[46px] w-[158px]" />
                <Skeleton className="h-[46px] w-40" />
              </div>
            </div>

            <table className=" space-y-5">
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-10" />
                </td>
                <td className="pb-3 text-sm uppercase sm:pb-5 dark:text-white">
                  <Skeleton className="h-5 w-20" />
                </td>
              </tr>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-14" />
                </td>
                <td className="pb-3 sm:pb-5 dark:text-white">
                  <Skeleton className="h-5 w-32" />
                </td>
              </tr>
              <tr className="group">
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-20" />
                </td>
                <td className="flex gap-x-2 pb-3 text-sm sm:pb-5 dark:text-white">
                  <Skeleton className="h-5 w-52" />
                </td>
              </tr>
              <tr className="group">
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-20" />
                </td>
                <td className="flex gap-x-2 pb-3 text-sm sm:pb-5 dark:text-white">
                  <Skeleton className="h-5 w-52" />
                </td>
              </tr>
              <tr className="group">
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-20" />
                </td>
                <td className="flex gap-x-2 pb-3 text-sm sm:pb-5 dark:text-white">
                  <Skeleton className="h-5 w-52" />
                </td>
              </tr>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-20" />
                </td>
                <td className="pb-3 text-sm sm:pb-5 dark:text-white">
                  <Skeleton className="h-5 w-14" />
                </td>
              </tr>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-20" />
                </td>
                <td className="pb-3 text-sm sm:pb-5">
                  <Skeleton className="h-5 w-60" />
                </td>
              </tr>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-24" />
                </td>
                <td className="pb-3 text-sm sm:pb-5 dark:text-white">
                  <Skeleton className="h-5 w-24" />
                </td>
              </tr>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-24" />
                </td>
                <td className="pb-3 text-sm sm:pb-5 dark:text-white">
                  <Skeleton className="h-5 w-9" />
                </td>
              </tr>
              <tr>
                <td className="w-[1%] whitespace-nowrap pb-3 pr-3 align-top text-sm font-medium text-gray-700 sm:pb-5 sm:pr-4 dark:text-gray-300">
                  <Skeleton className="h-5 w-32" />
                </td>
                <td className="pb-3 text-sm sm:pb-5 dark:text-white">
                  <Skeleton className="h-5 w-24" />
                </td>
              </tr>
            </table>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};
