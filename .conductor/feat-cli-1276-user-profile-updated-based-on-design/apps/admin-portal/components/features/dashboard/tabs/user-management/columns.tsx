import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { User } from "@/lib/apis/user-management";

export const userColumns: ColumnDef<User>[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "name",
    header: "Name",
  },
];

export const usersNeverLoginColum: ColumnDef<User>[] = [
  {
    accessorKey: "id",
    header: "ID",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        href={`/users/${row.original.id}`}
      >
        {row.original.id}
      </Link>
    ),
  },
  {
    accessorKey: "name",
    header: "Email",
  },
];
