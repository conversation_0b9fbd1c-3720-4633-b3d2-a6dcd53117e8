import { use<PERSON>ara<PERSON> } from "next/navigation";
import { Dispatch, SetStateAction } from "react";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteStudiesByGroup } from "@/hooks/queries/use-infinite-studies";
import { Profile } from "@/lib/apis/groups/types";

import { useAddUserCustomPermission } from "./hooks/use-user-profiles-mutations";
import { useUserCustomPermission } from "./hooks/use-user-profiles-queries";

const addStudySchema = z.object({
  studyId: z
    .string({ required_error: "Please select a study" })
    .min(1, "Please select a study"),
});

type ModalAddStudyProps = {
  groupId: string;
  isOpen: boolean;
  selectedProfile: Profile;
  setSelectedProfile: Dispatch<SetStateAction<Profile | null>>;

  onClose: () => void;
};

export const ModalAddStudy = ({
  groupId,
  isOpen,
  onClose,
  selectedProfile,
  setSelectedProfile,
}: ModalAddStudyProps) => {
  const userId = useParams().id as string;
  const { mutateAsync, isPending } = useAddUserCustomPermission(userId);
  const { data: activePermission } = useUserCustomPermission(
    userId,
    selectedProfile.id,
  );
  const handleSubmit = async (values: z.infer<typeof addStudySchema>) => {
    const selectedOption = JSON.parse(values.studyId);

    if (!selectedOption) return;

    await mutateAsync(
      {
        studyId: selectedOption.studyId,
        profileId: selectedProfile.id,
      },
      {
        onSuccess: (response) => {
          setSelectedProfile((prev) =>
            prev
              ? {
                  ...prev,
                  hasCustomPermissions: response.hasCustomStudyPermissions,
                }
              : null,
          );
        },
      },
    );
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Study</Modal.Header>
      <Modal.Body>
        <Form
          schema={addStudySchema}
          onSubmit={handleSubmit}
          className="space-y-4"
        >
          {/* Optimized LazySelect */}
          <LazySelect
            name="studyId"
            id="studyId"
            placeholder="Select study"
            searchPlaceholder="Search studies..."
            useInfiniteQuery={useInfiniteStudiesByGroup}
            params={[groupId]}
            getOptionLabel={(option) =>
              `${option.study.name} (${option.site?.name || "N/A"})`
            }
            getOptionValue={(option) => JSON.stringify(option)}
            mapData={(studies) =>
              studies.filter(
                (study) =>
                  !activePermission?.accessibleStudies.some(
                    (p) => p.id === study.studyId,
                  ),
              )
            }
          />

          {/* Modal Footer */}
          <div className="flex flex-col justify-end gap-4 sm:flex-row">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              disabled={isPending}
              variant="primary"
              isLoading={isPending}
              enabledForDirty
              disabledForInvalid
            >
              Add Study
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
