import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddSiteStudyPayload } from "@/lib/apis/sites";

import { USE_SITE_STUDIES_QUERY_KEY } from "./use-site-studies";

export const useSiteAddStudy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: AddSiteStudyPayload & { id: string }) => {
      const { id, ...payload } = data;
      return api.sites.addStudy(id, payload);
    },
    onSuccess: (_, { id }) => {
      toast.success("Study added successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_SITE_STUDIES_QUERY_KEY, id],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
