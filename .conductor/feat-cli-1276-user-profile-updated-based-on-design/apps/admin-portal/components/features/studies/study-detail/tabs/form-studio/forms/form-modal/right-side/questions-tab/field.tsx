import {
  Accordion,
  AccordionContent,
  AccordionPanel,
  AccordionTitle,
} from "flowbite-react";
import { Copy } from "lucide-react";

import { Tabs, TabsItem } from "@/components/ui/tabs";

import {
  DateTimeFormat,
  DecimalFormat,
  IntegerFormat,
  TextFormat,
} from "./field-formats";

const accordionTheme = {
  root: {
    base: "divide-y-2 border-2 divide-gray-200 border-gray-200 dark:divide-gray-600 dark:border-gray-600",
  },
  content: {
    base: "first:rounded-t-lg last:rounded-b-lg dark:bg-gray-700",
  },
  title: {
    base: "flex w-full gap-2 items-center justify-between py-5 px-2.5 text-left font-medium text-gray-500 first:rounded-t-lg last:rounded-b-lg dark:text-gray-400",
    flush: {
      off: "hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:hover:bg-gray-600 dark:focus:ring-gray-600",
      on: "bg-transparent dark:bg-transparent",
    },
    open: {
      off: "",
      on: "bg-gray-100 text-gray-900 dark:bg-gray-600 dark:text-white",
    },
    heading: "flex flex-1 items-center gap-2",
  },
};

export const Field = () => {
  return (
    <Accordion collapseAll theme={accordionTheme}>
      <AccordionPanel theme={accordionTheme}>
        <AccordionTitle theme={accordionTheme.title}>
          Category for Medication (CMCAT)
          <span className="bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200 ml-auto ml-auto rounded-full px-3 py-1 text-sm">
            Text
          </span>
        </AccordionTitle>
        <AccordionContent theme={accordionTheme.content}>
          <Tabs
            theme={{
              tabpanel: "py-0",
            }}
            className="gap-0 [&>button]:flex-1"
          >
            <TabsItem title={"Field"}>
              <div className="grid grid-cols-2 gap-2.5 p-4">
                <div>
                  <span className="text-xs">Field Type</span>
                  <div className="flex items-center gap-2.5">
                    <span className="py-2.5 pl-3">Text</span>
                  </div>
                </div>
                <div>
                  <span className="text-xs">Field Label</span>
                  <div className="flex items-center gap-2.5">
                    <span className="py-2.5 pl-3">CM Dose Unit</span>
                  </div>
                </div>
                <div className="col-span-2">
                  <span className="text-xs">Field ID</span>
                  <div className="flex items-center gap-2.5">
                    <span className="py-2.5 pl-3">QUESTIONID.CMDOSU</span>
                    <button>
                      <Copy />
                    </button>
                  </div>
                </div>
                <div className="col-span-2">
                  <span className="text-xs">Description</span>
                  <div className="flex items-center gap-2.5">
                    <span className="py-2.5 pl-3">
                      Lorem ipsum dolor sit amet
                    </span>
                  </div>
                </div>
              </div>
            </TabsItem>
            <TabsItem title={"Format"}>
              <div className="space-y-5 p-4">
                <TextFormat maxLength={100} />
                <DateTimeFormat dateFormat="DD/MM/YYYY" />
                <IntegerFormat allowLeadingZeros={true} />
                <DecimalFormat
                  allowLeadingZeros={true}
                  allowTrailingZeros={false}
                  decimalPlaces={2}
                />
              </div>
            </TabsItem>
          </Tabs>
        </AccordionContent>
      </AccordionPanel>
    </Accordion>
  );
};
