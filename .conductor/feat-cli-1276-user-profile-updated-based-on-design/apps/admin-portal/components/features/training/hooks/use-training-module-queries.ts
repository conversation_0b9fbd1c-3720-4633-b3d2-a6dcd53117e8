import { skipToken, useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSort } from "@/hooks/use-sort";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const trainingModuleKeys = {
  all: () => ["training-modules"] as const,

  allModuleLists: () => [...trainingModuleKeys.all(), "modules"] as const,
  moduleList: (params?: MetadataParams) =>
    [...trainingModuleKeys.allModuleLists(), params] as const,

  allCourseLists: () => [...trainingModuleKeys.all(), "courses"] as const,
  courseList: (params?: MetadataParams) =>
    [...trainingModuleKeys.allCourseLists(), params] as const,
  allCourseDetails: () => [...trainingModuleKeys.all(), "detail"] as const,
  courseDetail: (id: string) =>
    [...trainingModuleKeys.allCourseDetails(), id] as const,

  allChapterLists: () => [...trainingModuleKeys.all(), "chapters"] as const,
  allChapterListsByCourseId: (id: string) => [
    ...trainingModuleKeys.allChapterLists(),
    id,
  ],
  chapterListByCourseId: (id: string, params?: MetadataParams) => [
    ...trainingModuleKeys.allChapterListsByCourseId(id),
    params,
  ],

  allEnrollmentLists: () =>
    [...trainingModuleKeys.all(), "enrollments"] as const,
  allEnrollmentListsByCourseId: (id: string) => [
    ...trainingModuleKeys.allEnrollmentLists(),
    id,
  ],
  enrollmentListByCourseId: (id: string, params?: MetadataParams) => [
    ...trainingModuleKeys.allEnrollmentListsByCourseId(id),
    params,
  ],

  allChapterProgressLists: () =>
    [...trainingModuleKeys.all(), "chapter-progress"] as const,
  chapterProgressListByEnrollmentId: (id: string) => [
    ...trainingModuleKeys.allChapterProgressLists(),
    id,
  ],
};

export const useModules = () => {
  const { page, take } = usePagination();

  const params = {
    page,
    take,
  };

  return useQuery({
    queryKey: trainingModuleKeys.moduleList(params),
    queryFn: () => api.trainingModules.getModules(params),
    placeholderData: (prev) => prev,
  });
};

export const useInfiniteModules = (search: string, initialPageSize = 50) => {
  return useInfiniteQuery({
    queryKey: ["infinite-modules", search],
    queryFn: ({ pageParam = 1 }) =>
      api.trainingModules.getModules({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

export const useCourses = () => {
  const { page, take } = usePagination();
  const { orderBy, orderDirection } = useSort();
  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
  };

  return useQuery({
    queryKey: trainingModuleKeys.courseList(params),
    queryFn: () => api.trainingModules.getCourses(params),
    placeholderData: (prev) => prev,
  });
};

export const useCourse = (id: string) => {
  return useQuery({
    queryKey: trainingModuleKeys.courseDetail(id),
    queryFn: () => api.trainingModules.getCourse(id),
    placeholderData: (prev) => prev,
  });
};

export const useChaptersByCourseId = (id: string) => {
  const { take, page } = usePagination();
  const params = {
    take,
    page,
  };
  return useQuery({
    queryKey: trainingModuleKeys.chapterListByCourseId(id, params),
    queryFn: () => api.trainingModules.getChapterByCourseId(id, params),
    placeholderData: (prev) => prev,
  });
};

export const useEnrollmentsByCourseId = (id: string) => {
  const { take, page } = usePagination();
  const [status] = useQueryState("status", parseAsString);

  const params = {
    take,
    page,
    filter: {
      status: status || undefined,
    },
  };
  return useQuery({
    queryKey: trainingModuleKeys.enrollmentListByCourseId(id, params),
    queryFn: () => api.trainingModules.getEnrollmentByCourseId(id, params),
    placeholderData: (prev) => prev,
  });
};

export const useChapterProgressesByEnrollmentId = (id?: string) => {
  return useQuery({
    queryKey: id
      ? trainingModuleKeys.chapterProgressListByEnrollmentId(id)
      : [],
    queryFn: id
      ? () => api.trainingModules.getChapterProgressesByEnrollmentId(id)
      : skipToken,
  });
};
