"use client";

import { useThemeMode } from "flowbite-react";
import {
  Area,
  AreaChart,
  Legend,
  ResponsiveContainer,
  Tooltip,
  TooltipContentProps,
  XAxis,
  YAxis,
} from "recharts";
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";

import { useMediaQuery } from "@/hooks/use-media-query";

export type StackedAreaChartPoint<K extends string | number> = Record<
  K,
  number
> & {
  date: string;
};

export type StackedAreaConfig<K> = {
  dataKey: K;
  name: string;
  color?: string;
  gradientId?: string;
};

export type StackedAreaChartProps<T, K extends keyof T> = {
  data: T[];
  configs: StackedAreaConfig<K>[] | readonly StackedAreaConfig<K>[];
  dateKey?: K;
  showLegend?: boolean;
  showTooltip?: boolean;
  totalLabel?: string;
  customTooltip?: React.ComponentType<TooltipContentProps<ValueType, NameType>>;
  className?: string;
};

const defaultColors = [
  "#3B82F6", // Blue
  "#10B981", // Green
  "#F59E0B", // Yellow
  "#EF4444", // Red
  "#8B5CF6", // Purple
  "#F97316", // Orange
  "#06B6D4", // Cyan
  "#84CC16", // Lime
];

const generateGradientId = (dataKey: string | number) =>
  `color${dataKey.toString().replace(/[^a-zA-Z0-9]/g, "")}`;

const DefaultTooltip = ({
  active,
  payload,
  label,
  totalLabel,
}: TooltipContentProps<ValueType, NameType> & { totalLabel?: string }) => {
  if (active && payload && payload.length) {
    const total = payload.reduce(
      (sum, entry) => sum + (Number(entry.value) || 0),
      0,
    );

    return (
      <div className="rounded-lg border bg-white p-3 shadow-lg dark:border-gray-600 dark:bg-gray-800">
        <p className="font-medium text-gray-900 dark:text-white">{label}</p>
        <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
          Total: {total.toLocaleString()} {totalLabel}
        </p>
        <div className="mt-1 space-y-1">
          {payload.map((entry, index: number) => (
            <div key={index} className="flex justify-between gap-4 text-sm">
              <span style={{ color: entry.color }}>{entry.name}:</span>
              <span className="font-medium dark:text-white">
                {Number(entry.value).toLocaleString()}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }
  return null;
};

export const StackedAreaChart = <T, K extends Exclude<keyof T, symbol>>({
  data,
  configs,
  dateKey = "date" as K,
  showLegend = true,
  showTooltip = true,
  totalLabel,
  customTooltip,
  className = "h-80",
}: StackedAreaChartProps<T, K>) => {
  const isMobile = useMediaQuery("(width <= 640px)");
  const { mode } = useThemeMode();
  const configsWithColors = configs.map((config, index) => ({
    ...config,
    color: config.color || defaultColors[index % defaultColors.length],
    gradientId: config.gradientId || generateGradientId(config.dataKey),
  }));

  const TooltipComponent = customTooltip || DefaultTooltip;

  return (
    <div className={className}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data}>
          <defs>
            {configsWithColors.map((config) => (
              <linearGradient
                key={config.gradientId}
                id={config.gradientId}
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="5%" stopColor={config.color} stopOpacity={0.8} />
                <stop offset="95%" stopColor={config.color} stopOpacity={0.1} />
              </linearGradient>
            ))}
          </defs>
          <XAxis
            interval="preserveStartEnd"
            dataKey={dateKey}
            tick={{ fontSize: 12, fill: mode === "dark" ? "#fff" : "#000" }}
          />
          <YAxis
            tick={{ fontSize: 12, fill: mode === "dark" ? "#fff" : "#000" }}
          />
          {showTooltip && (
            <Tooltip
              content={(props) => (
                <TooltipComponent {...props} totalLabel={totalLabel} />
              )}
            />
          )}
          {showLegend && !isMobile && <Legend />}
          {configsWithColors.map((config) => (
            <Area
              key={config.dataKey}
              type="monotone"
              dataKey={config.dataKey}
              stackId="1"
              stroke={config.color}
              fill={`url(#${config.gradientId})`}
              name={config.name}
            />
          ))}
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};
