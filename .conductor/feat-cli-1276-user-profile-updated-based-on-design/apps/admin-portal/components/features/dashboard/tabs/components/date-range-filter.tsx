import "react-day-picker/style.css";
import "@/components/ui/form/date-picker/date-picker.css";

import { useRef, useState } from "react";
import { DateRange, DayPicker } from "react-day-picker";
import { useOnClickOutside } from "usehooks-ts";

import { Button } from "@/components/ui/button";
import { CustomCaptionLabel } from "@/components/ui/form/date-picker/custom-caption-label";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { formatDate } from "@/lib/utils";

import { DateOptions } from "./utils";

export const DATE_OPTIONS = [
  { value: "today", label: "Today" },
  { value: "yesterday", label: "Yesterday" },
  { value: "last_week", label: "Last week" },
  { value: "last_month", label: "Last month" },
  { value: "last_quarter", label: "Last quarter" },
  { value: "last_year", label: "Last year" },
  { value: "custom", label: "Custom range" },
] as const;

type DateRangeFilterProps = {
  selectedFilter: DateOptions | "";
  onFilterChange: (filter: DateOptions | "", customRange?: DateRange) => void;
  selectedRange: DateRange;
};

export const DateRangeFilter = ({
  selectedFilter,
  onFilterChange,
  selectedRange,
}: DateRangeFilterProps) => {
  const [customRange, setCustomRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });
  const [showCustomPicker, setShowCustomPicker] = useState(false);
  const dayPickerContainerRef = useRef<HTMLDivElement>(null);
  const [month, setMonth] = useState(new Date());
  const [showYears, setShowYears] = useState(false);
  const handleClickOutside = () => {
    if (showCustomPicker) {
      setShowCustomPicker(false);
      onFilterChange("");
      setCustomRange({ from: undefined, to: undefined });
    }
  };
  const currentYear = month.getFullYear();

  useOnClickOutside(dayPickerContainerRef, handleClickOutside);

  const handleFilterChange = (value?: string | null) => {
    const newFilter = (value as DateOptions) || "";

    if (newFilter === "custom") {
      setShowCustomPicker(true);
      onFilterChange(newFilter);
    } else {
      setShowCustomPicker(false);
      setCustomRange({ from: undefined, to: undefined });
      onFilterChange(newFilter);
    }
  };

  const handleDateRangeSelect = (range?: DateRange) => {
    if (range?.from) {
      setCustomRange({
        from: range.from,
        to: range.to || range.from,
      });
    }
  };

  const handleSave = () => {
    if (customRange.from && customRange.to) {
      onFilterChange("custom", customRange);
      setShowCustomPicker(false);
    }
  };

  const handleClose = () => {
    if (!selectedRange?.from || !selectedRange?.to) {
      onFilterChange("");
      setCustomRange({ from: undefined, to: undefined });
    }
    setShowCustomPicker(false);
  };

  const currentLabel =
    selectedFilter === "custom" && selectedRange?.from && selectedRange?.to
      ? `${formatDate(selectedRange.from, "yyyy/MM/dd")} - ${formatDate(
          selectedRange.to,
          "yyyy/MM/dd",
        )}`
      : undefined;

  return (
    <div className="relative">
      <UncontrolledSelect
        className="w-52"
        value={selectedFilter}
        onChange={handleFilterChange}
        options={DATE_OPTIONS}
        currentLabel={currentLabel}
        placeholder="Select time period"
      />

      {selectedFilter === "custom" && showCustomPicker && (
        <div
          ref={dayPickerContainerRef}
          className="absolute right-0 top-[120%] z-50 rounded-lg border bg-white shadow-lg dark:border-gray-600 dark:bg-gray-800"
        >
          <DayPicker
            mode="range"
            selected={customRange}
            onSelect={handleDateRangeSelect}
            month={month}
            onMonthChange={setMonth}
            className="w-fit p-3"
            formatters={{
              formatWeekdayName: (weekday) => formatDate(weekday, "EEEEE"),
            }}
            disabled={{
              after: new Date(),
            }}
            components={{
              CaptionLabel: (props) => (
                <CustomCaptionLabel
                  showYears={showYears}
                  setShowYears={setShowYears}
                  currentYear={currentYear}
                  month={month}
                  setMonth={setMonth}
                  maxDate={new Date()}
                >
                  {props.children}
                </CustomCaptionLabel>
              ),
            }}
            hideNavigation
          />

          <div className="flex items-center gap-4 border-t border-gray-200 p-2 pt-3 dark:border-gray-600">
            <Button
              onClick={handleClose}
              className="flex-1"
              variant="outline"
              size="sm"
            >
              Close
            </Button>

            <Button
              onClick={handleSave}
              className="flex-1"
              variant="primary"
              size="sm"
              disabled={!customRange.from || !customRange.to}
            >
              Apply
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
