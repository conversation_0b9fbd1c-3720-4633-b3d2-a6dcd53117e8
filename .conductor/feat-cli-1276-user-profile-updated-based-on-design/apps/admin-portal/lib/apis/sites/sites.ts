import Base<PERSON><PERSON> from "../base";
import type { Contact, ListContactsResponse } from "../contacts";
import type { MetadataParams } from "../types";
import type {
  AddSiteContactPayload,
  AddSitePayload,
  AddSiteStudyPayload,
  LocalStudy,
  LocalStudyParams,
  ScaffoldFoldersPayload,
  ScaffoldFoldersResponse,
  Site,
  SitesListResponse,
  SitesStudiesListResponse,
  SiteStudy,
  UpdateSitePayload,
  UpdateSiteStudyPayload,
} from "./types";

export class SitesApi extends BaseApi {
  public constructor() {
    super("/sites", true);
  }

  public async list(params: MetadataParams): Promise<SitesListResponse> {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<SitesListResponse>(`?${paramUrl}`);
  }

  public async get(id: string): Promise<Site> {
    return this.http.get<Site>(`/${id}`);
  }

  public async update(id: string, data: UpdateSitePayload): Promise<Site> {
    return this.http.put<Site>(`/${id}`, data);
  }

  public async create(data: AddSitePayload): Promise<Site> {
    return this.http.post<Site>("/", data);
  }
  public async getStudies(id: string): Promise<SitesStudiesListResponse> {
    return this.http.get<SitesStudiesListResponse>(`/${id}/studies`);
  }
  public async getStudy({ siteId, studyId }: LocalStudyParams) {
    return this.http.get<LocalStudy>(`/${siteId}/studies/${studyId}`);
  }

  public async addStudy(
    id: string,
    data: AddSiteStudyPayload,
  ): Promise<SiteStudy> {
    return this.http.post<SiteStudy>(`/${id}/studies`, data);
  }

  public async updateStudy(
    id: string,
    studyId: string,
    data: UpdateSiteStudyPayload,
  ): Promise<SiteStudy> {
    return this.http.put<SiteStudy>(`/${id}/studies/${studyId}`, data);
  }

  public async getContacts(id: string): Promise<ListContactsResponse> {
    return this.http.get<ListContactsResponse>(`/${id}/contacts`);
  }

  public async addContact(
    id: string,
    data: AddSiteContactPayload,
  ): Promise<Contact> {
    return this.http.post<Contact>(`/${id}/contact`, data);
  }

  public async scaffoldFolders({
    siteId,
    studyId,
  }: ScaffoldFoldersPayload): Promise<ScaffoldFoldersResponse> {
    return this.http.post<ScaffoldFoldersResponse>(
      `/${siteId}/studies/${studyId}/scaffold`,
      {},
    );
  }
  public async createSystemFolder({ siteId, studyId }: ScaffoldFoldersPayload) {
    return this.http.post(`/${siteId}/studies/${studyId}/system-folder`);
  }
}
