import type { ListBaseResponse } from "@/lib/apis/types";

export type QuestionStatus = "DRAFT" | "PUBLISHED" | "ARCHIVED";

export type FieldReference = {
  fieldId: string;
  overrideLabel?: string;
  isRequired: boolean;
};

export type SimpleQuestionConfig = {
  questionText: string;
  field: FieldReference;
};

export type GroupedQuestionConfig = {
  questionText: string;
  fields: FieldReference[];
};

export type LogQuestionConfig = {
  questionText: string;
  columns: FieldReference[];
  minRows?: number;
  maxRows?: number;
  initialData?: {
    [variableName: string]: any;
  }[];
};
export type BaseQuestion = {
  id: string;
  questionName: string;
  displayId: string;
  description?: string;
  status: QuestionStatus;
  version: number;
  createdDate: string;
  lastUpdatedDate: string;
};

export type SimpleQuestion = BaseQuestion & {
  questionType: "SimpleQuestion";
  config: SimpleQuestionConfig;
};

export type GroupedQuestion = BaseQuestion & {
  questionType: "GroupedQuestion";
  config: GroupedQuestionConfig;
};

export type LogQuestion = BaseQuestion & {
  questionType: "LogQuestion";
  config: LogQuestionConfig;
};

export type Question = SimpleQuestion | GroupedQuestion | LogQuestion;

export type QuestionListResponse = ListBaseResponse<Question>;

export type BaseQuestionPayload = {
  questionName: string;
  studyId: string;
  description?: string;
};

export type CreateSimpleQuestionPayload = BaseQuestionPayload & {
  questionType: "SimpleQuestion";
  config: SimpleQuestionConfig;
};

export type CreateGroupedQuestionPayload = BaseQuestionPayload & {
  questionType: "GroupedQuestion";
  config: GroupedQuestionConfig;
};

export type CreateLogQuestionPayload = BaseQuestionPayload & {
  questionType: "LogQuestion";
  config: LogQuestionConfig;
};

export type CreateQuestionPayload =
  | CreateSimpleQuestionPayload
  | CreateGroupedQuestionPayload
  | CreateLogQuestionPayload;

export type UpdateQuestionPayload = CreateQuestionPayload & {
  id: string;
};
