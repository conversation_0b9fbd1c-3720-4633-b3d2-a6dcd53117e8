import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { Button } from "@/components/ui/button";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { EncounterActivity } from "@/lib/apis/encounters";
import { useEncounterStore } from "@/stores/encounte-store";

import { useProtocol } from "../../../../../hooks/use-protocols-queries";
import { ModalAddNewActivity } from "../../../modal-add-activity-to-encounter";
import { useActivityProcedures } from "../../hooks/use-activity-procedure";
import { useEncounterActivities } from "../../hooks/use-encounter-activities";
import { useRemoveActivityFromEncounter } from "../../hooks/use-remove-activity";
import { generateActivityProceduresColumns } from "../columns/activity-procedures";
import { generateEncounterActivitiesColumns } from "../columns/encounter-activities";
import { ModalAddExistingActivity } from "./modal-add-existing-activity";
import { ModalAddExistingProcedure } from "./modal-add-existing-procedures";
import { ModalAddProcedure } from "./modal-add-procedure";

type Props = {
  protocolId: string;
};

export const ActivitiesTab = ({ protocolId }: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activityAddMode, setActivityAddMode] = useState<"existing" | "new">(
    "existing",
  );
  const [selectedActivityId, setSelectedActivityId] = useState<string | null>(
    null,
  );
  const [isAddProcedureOpen, setIsAddProcedureOpen] = useState(false);

  const {
    isOpen: isOpenAddExistingProcedures,
    close: onCloseAddExistingProcedures,
    open: onOpenAddExistingProcedures,
  } = useDisclosure();

  const { id: encounterId } = useEncounterStore().currentEncounter!;
  const { data: protocol } = useProtocol(protocolId);

  const { mutateAsync: removeActivity } =
    useRemoveActivityFromEncounter(protocolId);
  const { data: activities, isLoading } = useEncounterActivities(
    encounterId as string,
  );

  const columns = useMemo(
    () =>
      generateEncounterActivitiesColumns(
        (activityId: string) =>
          removeActivity({ activityId, encounterId: encounterId as string }),
        protocol?.isPublished,
      ),
    [removeActivity, encounterId, protocol?.isPublished],
  );

  const handleAddActivityClick = (mode: "existing" | "new") => {
    setActivityAddMode(mode);
    setIsOpen(true);
  };

  const handleAddNewProcedure = (activityId: string) => {
    setSelectedActivityId(activityId);
    setIsAddProcedureOpen(true);
  };
  const handleAddExistingProcedures = (activityId: string) => {
    setSelectedActivityId(activityId);
    onOpenAddExistingProcedures();
  };

  const renderActivityProcedures = (activity: EncounterActivity) => {
    const ProceduresList = () => {
      const { data: procedures, isLoading } = useActivityProcedures(
        activity.id,
        true,
      );
      const procedureColumns = useMemo(
        () => generateActivityProceduresColumns(protocol?.isPublished),
        [],
      );

      if (isLoading) {
        return <TableLoading columns={procedureColumns} />;
      }

      return (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-md font-semibold">Procedures</h3>
            <div className="flex items-center gap-x-2">
              <Button
                variant="primary"
                disabled={protocol?.isPublished}
                onClick={() => handleAddExistingProcedures(activity.id)}
                size="sm"
              >
                <IoMdAdd className="mr-1" />
                Add Existing Procedure
              </Button>
              <Button
                variant="primary"
                disabled={protocol?.isPublished}
                onClick={() => handleAddNewProcedure(activity.id)}
                size="sm"
              >
                <IoMdAdd className="mr-1" />
                Add Procedure
              </Button>
            </div>
          </div>

          {procedures && procedures.results.length > 0 ? (
            <div className="rounded-lg border border-gray-200 dark:border-gray-700">
              <TableData columns={procedureColumns} data={procedures.results} />
            </div>
          ) : (
            <div className="rounded-lg border border-gray-200 p-4 text-center text-gray-500 dark:border-gray-700">
              No procedures available.
            </div>
          )}
        </div>
      );
    };

    return <ProceduresList />;
  };

  if (isLoading) {
    return (
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Activities</div>
          <Button
            disabled={protocol?.isPublished}
            variant="primary"
            onClick={() => handleAddActivityClick("existing")}
          >
            <IoMdAdd />
            Add Activity
          </Button>
        </div>
        <TableLoading columns={columns} />
      </Card>
    );
  }

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex flex-col justify-between gap-2 p-4 pb-0 text-lg font-semibold md:flex-row md:items-center">
          <div className="dark:text-gray-400">Activities</div>
          <div className="flex w-full flex-col justify-end gap-2 sm:w-fit sm:flex-row">
            <Button
              disabled={protocol?.isPublished}
              variant="primary"
              onClick={() => handleAddActivityClick("existing")}
            >
              <IoMdAdd />
              Add Existing Activity
            </Button>
            <Button
              disabled={protocol?.isPublished}
              variant="primary"
              onClick={() => handleAddActivityClick("new")}
            >
              <IoMdAdd />
              Add New Activity
            </Button>
          </div>
        </div>

        {activities && activities.results.length > 0 ? (
          <TableData
            columns={columns}
            data={activities.results}
            collapsible={{
              enable: true,
              renderSubRow: renderActivityProcedures,
              getRowId: (row: EncounterActivity) => row.id,
            }}
          />
        ) : (
          <div className="p-8 text-center text-gray-500">
            No activities available.
          </div>
        )}
      </Card>

      {activityAddMode === "existing" && (
        <ModalAddExistingActivity
          encounterId={encounterId as string}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          protocolId={protocolId}
        />
      )}

      {activityAddMode === "new" && (
        <ModalAddNewActivity isOpen={isOpen} onClose={() => setIsOpen(false)} />
      )}

      {selectedActivityId && (
        <ModalAddProcedure
          isOpen={isAddProcedureOpen}
          onClose={() => {
            setIsAddProcedureOpen(false);
            setSelectedActivityId(null);
          }}
          activityId={selectedActivityId}
          encounterId={encounterId as string}
        />
      )}
      <ModalAddExistingProcedure
        isOpen={isOpenAddExistingProcedures}
        onClose={onCloseAddExistingProcedures}
        activityId={selectedActivityId as string}
      />
    </>
  );
};
