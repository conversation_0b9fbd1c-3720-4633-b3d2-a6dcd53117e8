"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { ConfirmationModal } from "@/components/shared/confirmation-modal";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { SearchField } from "@/components/shared/search-field";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Codelist } from "@/lib/apis/codelist-definitions/types";

import { CodelistModal } from "./codelist-modal";
import { generateCodelistColumns } from "./columns";
import { FilterCodelists } from "./filter-codelists";
import {
  useArchiveCodelist,
  useCopyCodelist,
  useDeleteCodelist,
  usePublishCodelist,
} from "./hooks/use-codelist-mutations";
import { useCodelists } from "./hooks/use-codelist-queries";

export const CodelistTab = () => {
  const { data, isPending, isPlaceholderData } = useCodelists();
  const { mutateAsync: publishCodelist, isPending: isPublishing } =
    usePublishCodelist();
  const { mutateAsync: archiveCodelist, isPending: isArchiving } =
    useArchiveCodelist();
  const { mutateAsync: deleteCodelist, isPending: isDeleting } =
    useDeleteCodelist();
  const { mutateAsync: copyCodelist, isPending: isCopyingCodelist } =
    useCopyCodelist();
  const {
    isOpen: isModalOpen,
    open: openModal,
    close: closeModal,
  } = useDisclosure();
  const {
    isOpen: isPublishModalOpen,
    open: openPublishModal,
    close: closePublishModal,
  } = useDisclosure();
  const {
    isOpen: isDeleteModalOpen,
    open: openDeleteModal,
    close: closeDeleteModal,
  } = useDisclosure();
  const {
    isOpen: isArchiveModalOpen,
    open: openArchiveModal,
    close: closeArchiveModal,
  } = useDisclosure();
  const [selectedCodelist, setSelectedCodelist] = useState<Codelist | null>(
    null,
  );
  const [modalMode, setModalMode] = useState<"create" | "edit">("create");

  const openAddModal = () => {
    setSelectedCodelist(null);
    setModalMode("create");
    openModal();
  };

  const openEditModal = (codelist: Codelist) => {
    setSelectedCodelist(codelist);
    setModalMode("edit");
    openModal();
  };

  const handleCloseModal = () => {
    closeModal();
    setSelectedCodelist(null);
    setModalMode("create");
  };

  const handleCopyCodelist = async (codelist: Codelist) => {
    await copyCodelist({ id: codelist.id });
  };

  const handleShowPublishModal = (codelist: Codelist) => {
    setSelectedCodelist(codelist);
    openPublishModal();
  };

  const handleShowDeleteModal = (codelist: Codelist) => {
    setSelectedCodelist(codelist);
    openDeleteModal();
  };

  const handleShowArchiveModal = (codelist: Codelist) => {
    setSelectedCodelist(codelist);
    openArchiveModal();
  };

  const handleConfirmPublish = async () => {
    if (selectedCodelist) {
      await publishCodelist({ id: selectedCodelist.id });
      closePublishModal();
      setSelectedCodelist(null);
    }
  };

  const handleConfirmDelete = async () => {
    if (selectedCodelist) {
      await deleteCodelist({ id: selectedCodelist.id });
      closeDeleteModal();
      setSelectedCodelist(null);
    }
  };

  const handleConfirmArchive = async () => {
    if (selectedCodelist) {
      await archiveCodelist({ id: selectedCodelist.id });
      closeArchiveModal();
      setSelectedCodelist(null);
    }
  };

  const columns = useMemo(
    () =>
      generateCodelistColumns({
        onEdit: openEditModal,
        onCopy: handleCopyCodelist,
        onPublish: handleShowPublishModal,
        onArchive: handleShowArchiveModal,
        onDelete: handleShowDeleteModal,
      }),
    [],
  );

  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Codelists
          </h3>

          <div className="flex items-center gap-4">
            <SearchField
              queryKey="codelistSearch"
              placeholder="Search codelists..."
            />
            <FilterCodelists />
            <Button variant="primary" onClick={openAddModal}>
              <IoMdAdd className="h-4 w-4" /> New Codelist
            </Button>
          </div>
        </div>
      </div>

      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper
          isLoading={
            isPlaceholderData ||
            isPublishing ||
            isArchiving ||
            isDeleting ||
            isCopyingCodelist
          }
        >
          <Table columns={columns} data={data?.results ?? []} />
          {data?.metadata && <TableDataPagination metadata={data.metadata} />}
        </LoadingWrapper>
      )}

      {isModalOpen && (
        <CodelistModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          selectedCodelist={selectedCodelist}
        />
      )}

      <ConfirmationModal
        isOpen={isPublishModalOpen}
        onClose={closePublishModal}
        onConfirm={handleConfirmPublish}
        variant="warning"
        message="Are you sure you want to publish this codelist?"
        itemName={selectedCodelist?.name}
        confirmLabel="Publish"
        warningMessage="Once published, this codelist cannot be edited anymore."
        isLoading={isPublishing}
      />

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleConfirmDelete}
        variant="danger"
        message="Are you sure you want to delete this codelist?"
        itemName={selectedCodelist?.name}
        confirmLabel="Delete"
        isLoading={isDeleting}
      />

      <ConfirmationModal
        isOpen={isArchiveModalOpen}
        onClose={closeArchiveModal}
        onConfirm={handleConfirmArchive}
        variant="warning"
        message="Are you sure you want to archive this codelist?"
        itemName={selectedCodelist?.name}
        confirmLabel="Archive"
        warningMessage="Once archived, this codelist cannot be edited anymore."
        isLoading={isArchiving}
      />
    </Card>
  );
};
