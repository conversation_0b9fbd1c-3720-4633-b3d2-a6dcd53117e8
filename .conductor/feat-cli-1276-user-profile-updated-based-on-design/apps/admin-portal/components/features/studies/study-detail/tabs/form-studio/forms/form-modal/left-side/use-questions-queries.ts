// Types for questions functionality
export type QuestionFolder = {
  id: string;
  name: string;
  parentDirectoryId: string | null;
  subfolders: QuestionFolder[];
  questionCount: number;
  isEditable: boolean;
};

export type Question = {
  id: string;
  title: string;
  fieldType: "text";
  folderId: string;
  isRequired: boolean;
  createdDate: string;
};

// Placeholder hooks - will be implemented when API is ready
export const useQuestionFolders = () => {
  return {
    data: [] as QuestionFolder[],
    isLoading: false,
    isSuccess: true,
  };
};

export const useQuestions = (folderId: string | null) => {
  return {
    data: {
      results: [],
      metadata: null,
    },
    isPending: false,
    isPlaceholderData: false,
  };
};
