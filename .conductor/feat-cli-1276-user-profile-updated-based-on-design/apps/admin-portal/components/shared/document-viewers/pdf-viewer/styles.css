.react-pdf__message--loading,
.react-pdf__message--error {
  height: 100%;
}

/* PDF Viewer specific styles */
.react-pdf__Page {
  transition: all 0.2s ease-in-out;
}

.react-pdf__Page__canvas {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
  border-radius: 0.375rem;
}

/* Search highlighting */
.react-pdf__Page__textContent .highlight {
  background-color: yellow !important;
  color: black !important;
}

/* Toolbar responsive styles */
@media (max-width: 768px) {
  .pdf-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .pdf-toolbar > div {
    justify-content: center;
  }
}
