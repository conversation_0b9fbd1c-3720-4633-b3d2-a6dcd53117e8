import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const contactKeys = {
  all: () => ["contacts"] as const,
  allLists: (studyId: string) =>
    [...contactKeys.all(), studyId, "list"] as const,
  list: (studyId: string) => [...contactKeys.allLists(studyId)] as const,
};

export const useStudyContacts = (studyId: string) => {
  return useQuery({
    queryKey: contactKeys.list(studyId),
    queryFn: () => api.studies.getContacts(studyId),
  });
};
