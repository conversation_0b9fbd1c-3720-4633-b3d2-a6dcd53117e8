import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { parseAsInteger, parseAsString, useQueryState } from "nuqs";
import React, { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Enrollment } from "@/lib/apis/training-modules";

import { generateEnrollmentColumns } from "./columns";
import { ENROLLMENT_STATUSES, EnrollmentModal } from "./enrollment-modal";
import { useDeleteEnrollment } from "./hooks/use-enrollment-mutations";
import { useEnrollmentsByUserId } from "./hooks/use-enrollment-queries";

export const EnrollmentTab = () => {
  const userId = useParams().id as string;
  const [status, setStatus] = useQueryState("status", parseAsString);
  const [, setPage] = useQueryState("page", parseAsInteger);

  const { isOpen, open, close } = useDisclosure();
  const [selectedEnrollment, setSelectedEnrollment] =
    useState<Enrollment | null>(null);

  const { data, isPending, isPlaceholderData } = useEnrollmentsByUserId(userId);
  const { mutate, isPending: isDeleting } = useDeleteEnrollment(userId);

  const handleClose = () => {
    close();
    setSelectedEnrollment(null);
  };

  const columns = useMemo(
    () =>
      generateEnrollmentColumns({
        onView: (data) => {
          setSelectedEnrollment(data);
          open();
        },
        onDelete: (data) => mutate(data.id),
      }),
    [mutate, open],
  );

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-start justify-between p-4 pb-0 text-lg font-semibold sm:items-center">
          <h2 className="dark:text-gray-400">Enrollments</h2>

          <div className="flex flex-col-reverse gap-2 sm:flex-row">
            <UncontrolledSelect
              options={ENROLLMENT_STATUSES.map((status) => ({
                label: status.label,
                value: status.value,
              }))}
              className="w-[140px]"
              placeholder="Filter by status"
              value={status || ""}
              onChange={(value) => {
                setStatus(value || null);
                setPage(1);
              }}
            />
            <Button onClick={open} variant="primary">
              <IoMdAdd />
              Add Enrollment
            </Button>
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData || isDeleting}>
              <Table columns={columns} data={data?.results ?? []} />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>

      {isOpen && (
        <EnrollmentModal
          onClose={handleClose}
          isOpen={isOpen}
          selectedEnrollment={selectedEnrollment}
        />
      )}
    </>
  );
};
