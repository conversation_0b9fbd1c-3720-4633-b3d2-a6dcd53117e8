import { Radio as FlowbiteRadio } from "flowbite-react";
import React, { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import type { SetRequired } from "type-fest";

import { cn } from "@/lib/utils";

export type RadioProps = SetRequired<
  React.ComponentPropsWithoutRef<typeof FlowbiteRadio>,
  "name"
> & {
  shouldShowError?: boolean;
};

export const Radio = forwardRef<HTMLInputElement, RadioProps>(
  ({ name, className, ...props }, ref) => {
    const { control } = useFormContext();
    return (
      <Controller
        control={control}
        name={name}
        render={({ field: { value, ...rest } }) => (
          <FlowbiteRadio
            {...rest}
            ref={ref}
            color="blue"
            className={cn(
              "h-4 w-4 border border-gray-300 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:focus:bg-blue-600 dark:focus:ring-blue-600",
              className,
            )}
            checked={value === props.value}
            {...props}
          />
        )}
      />
    );
  },
);

Radio.displayName = "Radio";
