"use client";

import { parseAsString, useQueryState } from "nuqs";
import { BiError } from "react-icons/bi";
import { FiDatabase } from "react-icons/fi";

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { PageHeader } from "@/components/ui/page-header";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { HmacFailuresTable } from "./hmac-failures-table";
import { ProcessingErrorsTable } from "./processing-errors-table";

const SYSTEM_HEALTH_TABS = [
  {
    key: "processing-errors",
    title: (
      <p className="flex items-center gap-3">
        <BiError className="h-5 w-5 text-red-500" />
        System Processing Errors
      </p>
    ),
    content: <ProcessingErrorsTable />,
  },
  {
    key: "hmac-failures",
    title: (
      <p className="flex items-center gap-3">
        <FiDatabase className="h-5 w-5 text-orange-500" />
        HMAC Verification Failures
      </p>
    ),
    content: <HmacFailuresTable />,
  },
] as const;

const SELECT_OPTIONS = SYSTEM_HEALTH_TABS.map((tab) => ({
  label: tab.title,
  value: tab.key,
}));

const BREADCRUMB_ITEMS = [{ label: "System Health" }];

export const SystemHealthContent = () => {
  const [currentTab, setCurrentTab] = useQueryState("tab", parseAsString);

  return (
    <div className="space-y-4">
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader showBackButton href="/dashboards?tab=system-health">
        System Vitals & Health
      </PageHeader>

      <div className="space-y-4 sm:hidden">
        <UncontrolledSelect
          placeholder="Select system health section"
          options={SELECT_OPTIONS}
          value={currentTab || ""}
          onChange={(value) => {
            if (value) {
              setCurrentTab(value);
            }
          }}
          className="w-full"
        />

        <div>
          {SYSTEM_HEALTH_TABS.find((tab) => tab.key === currentTab)?.content ??
            SYSTEM_HEALTH_TABS[0].content}
        </div>
      </div>

      <div className="hidden sm:block">
        <TabsWrapper tabs={SYSTEM_HEALTH_TABS} />
      </div>
    </div>
  );
};
