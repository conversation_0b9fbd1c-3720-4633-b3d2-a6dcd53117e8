import { ColumnDef } from "@tanstack/react-table";

import { TableEditButton } from "@/components/shared/table-action-buttons";
import { FormStudioStatusBadge } from "@/components/ui/badges/form-studio-status-badge";

export type VariableStatus = "DRAFT" | "PUBLISHED" | "ARCHIVED";

export type Variable = {
  id: string;
  label: string;
  name: string;
  value: string;
  description?: string;
  status: VariableStatus;
  createdDate: string;
  lastUpdatedDate: string;
};

export const generateVariableColumns = (
  onEdit: (variable: Variable) => void,
): ColumnDef<Variable>[] => [
  {
    accessorKey: "label",
    header: "Label",
    cell: ({ row }) => (
      <button
        onClick={() => onEdit(row.original)}
        className="text-primary-500 font-medium hover:underline"
      >
        {row.original.label}
      </button>
    ),
  },
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <span className="font-mono text-sm">{row.original.name}</span>
    ),
  },
  {
    accessorKey: "value",
    header: "Value",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => <FormStudioStatusBadge variant={row.original.status} />,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <div className="flex gap-2">
        <TableEditButton type="button" onClick={() => onEdit(row.original)} />
      </div>
    ),
  },
];
