import type { Simplify } from "type-fest";

import type { Address } from "../addresses";
import type { CreateContactPayload } from "../contacts";
import type { Study } from "../studies";
import type { ListBaseResponse } from "../types";

export type Site = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  displayName: string | null;
  phone: string;
  email: string;
  logoLocation: string | null;
  isActive: boolean;
  archived: boolean;
  groupId: string | null;
  address: Address;
};

export type SiteStudy = {
  id: string;
  study: Study;
  status: string;
  patientTarget: number;
  principalInvestigator: string;
  protocolId: string;
  subInvestigator?: string;
  template?: boolean;
  siteId: string;
  studyId: string;
  siteNumber?: string;
};

export type LocalStudyParams = {
  siteId: string;
  studyId: string;
};

export type SitesListResponse = ListBaseResponse<Site>;
export type SitesStudiesListResponse = ListBaseResponse<SiteStudy>;

export type AddSitePayload = {
  name: string;
  email?: string;
  phone?: string;
  address: Simplify<Omit<Address, "country" | "stateProvince">>;
};

export type UpdateSitePayload = Partial<AddSitePayload>;

export type AddSiteStudyPayload = {
  studyId: string;
  status: string;
  patientTarget: number;
  principalInvestigator: string;
  subInvestigator?: string;
  protocolId: string;
  siteNumber?: string;
};

export type UpdateSiteStudyPayload = {
  status?: string;
  patientTarget?: number;
  principalInvestigator?: string;
  subInvestigator?: string;
  siteNumber?: string;
  protocolId?: string;
};

export type AddSiteContactPayload = CreateContactPayload;

export type ScaffoldFoldersPayload = {
  siteId: string;
  studyId: string;
};

export type ScaffoldFoldersResponse = {
  success: boolean;
  message: string;
  foldersCreated: number;
};

export type LocalStudy = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  siteId: string;
  studyId: string;
  protocolId: string;
  pin: string;
  shortId: string;
  status: string;
  patientTarget: number;
  principalInvestigator: string;
  subInvestigator: string | null;
  artifactCategoryVersionId: string;
  siteNumber: string;
  isScaffolded: boolean;
  study: Study;
  site: Site;
  protocol: Protocol;
};

type Protocol = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  version: number;
  consentRequired: boolean;
  ammendmentDate: string;
  protocolSummary: Record<string, any>;
  isPublished: boolean;
  publishedAt: string | null;
  isSubstantiveChange: boolean;
  status: string;
};
