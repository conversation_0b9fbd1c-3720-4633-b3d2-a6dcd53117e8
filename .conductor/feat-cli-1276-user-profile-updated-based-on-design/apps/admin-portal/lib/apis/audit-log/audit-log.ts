import BaseApi from "../base";
import type { MetadataParams } from "../types";
import type {
  AuditLogAction,
  AuditLogListResponse,
  AuditLogResourceType,
} from "./types";

export class AuditLogApi extends BaseApi {
  public constructor() {
    super("/activity-log", true);
  }

  public async getActivityLog(params: MetadataParams) {
    return this.http.get<AuditLogListResponse>(`/`, params);
  }

  public async getCriticalEvents(params: MetadataParams) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<AuditLogListResponse>(`/critical?${paramUrl}`);
  }

  public async getAllActions() {
    return this.http.get<AuditLogAction[]>(`/action-type-list`);
  }

  public async getAllResourceTypes() {
    return this.http.get<AuditLogResourceType[]>(`/resource-type-list`);
  }
}
