import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const useInfinitePromptVariables = (
  search: string,
  filters?: MetadataParams["filter"],
  initialPageSize = 1000,
) => {
  return useInfiniteQuery({
    queryKey: ["prompt-variables", search, filters],
    queryFn: ({ pageParam = 1 }) =>
      api.promptVariables.list({
        page: pageParam,
        take: initialPageSize,
        filter: { key: search, ...filters },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    placeholderData: (prev) => prev,
    initialPageParam: 1,
  });
};
