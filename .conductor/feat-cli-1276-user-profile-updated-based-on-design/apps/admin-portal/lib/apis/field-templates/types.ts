import { FIELD_TYPES } from "@/components/features/studies/study-detail/tabs/form-studio/fields/columns";
import type { ListBaseResponse } from "@/lib/apis/types";

export type FieldConfig = {
  allowPartial?: string;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
  maxLength?: number;
  min?: number;
  max?: number;
  unitOfMeasure?: string;
  layoutDirection?: "horizontal" | "vertical";
  decimalPlaces?: number;
  codeListId?: string;
  isDisplayOnForm?: boolean;
};

export type FieldTemplateItem = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  fieldName: string;
  fieldType: (typeof FIELD_TYPES)[number];
  friendlyName: string;
  shortCode: string;
  description?: string;
  version: number;
  templateIdentifier: string;
  config?: FieldConfig;
  metadata?: any;
};

export type FieldTemplateListResponse = ListBaseResponse<FieldTemplateItem>;

export type CreateFieldTemplatePayload = {
  fieldName: string;
  fieldType: (typeof FIELD_TYPES)[number];
  friendlyName: string;
  description?: string;
  templateIdentifier: string;
  config?: FieldConfig;
  metadata?: any;
};

export type UpdateFieldTemplatePayload = CreateFieldTemplatePayload & {
  id: string;
};
