import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteSites = (search: string, initialPageSize = 50) => {
  return useInfiniteQuery({
    queryKey: ["infinite-sites", search],
    queryFn: ({ pageParam = 1 }) =>
      api.sites.list({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
