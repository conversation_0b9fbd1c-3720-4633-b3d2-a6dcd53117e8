"use client";

import { HiExclamationTriangle } from "react-icons/hi2";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import type { UserInvitation } from "@/lib/apis/users/types";

type ModalRevokeInvitationProps = {
  isOpen: boolean;
  onClose: () => void;
  invitation: UserInvitation | null;
  onConfirm: (invitationId: string) => void;
  isRevoking: boolean;
};

export const ModalRevokeInvitation = ({
  isOpen,
  onClose,
  invitation,
  onConfirm,
  isRevoking,
}: ModalRevokeInvitationProps) => {
  if (!invitation) return null;

  const handleConfirm = async () => {
    await onConfirm(invitation.id);
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <HiExclamationTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Revoke Invitation
          </h3>
        </div>
      </Modal.Header>
      <Modal.Body>
        <div className="space-y-6">
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Are you sure you want to revoke this invitation? This action
              cannot be undone.
            </p>
          </div>

          <div className="rounded-xl border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800/50">
            <h4 className="mb-3 text-sm font-medium text-gray-900 dark:text-white">
              Invitation Details
            </h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between py-1">
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Email Address
                </span>
                <span className="text-sm font-semibold text-gray-900 dark:text-white">
                  {invitation.emailAddress}
                </span>
              </div>
              {invitation.publicMetadata && (
                <>
                  <div className="flex items-center justify-between py-1">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Full Name
                    </span>
                    <span className="text-sm font-semibold text-gray-900 dark:text-white">
                      {`${invitation.publicMetadata.firstName} ${invitation.publicMetadata.lastName}`.trim()}
                    </span>
                  </div>
                  {invitation.publicMetadata.groupId && (
                    <div className="flex items-center justify-between py-1">
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Group Type
                      </span>
                      <span className="inline-flex rounded-full bg-blue-100 px-2 py-1 text-xs font-medium uppercase text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                        {invitation.publicMetadata.groupType}
                      </span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          <div className="flex flex-col justify-end gap-3 sm:flex-row">
            <CloseButton onClose={onClose} />
            <Button
              type="button"
              variant="outlineDanger"
              onClick={handleConfirm}
              isLoading={isRevoking}
              disabled={isRevoking}
              className="min-w-[140px]"
            >
              Revoke Invitation
            </Button>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};
