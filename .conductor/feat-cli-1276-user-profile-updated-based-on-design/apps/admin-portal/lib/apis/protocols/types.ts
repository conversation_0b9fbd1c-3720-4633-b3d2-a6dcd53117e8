import { PROTOCOL_STATUS } from "@/components/features/studies/study-detail/tabs/protocols/tabs/overview/modal-edit-protocol";

import { Epoch } from "../epochs/types";
import { StudyArm } from "../study-arms/types";
import { ListBaseResponse } from "../types";

export type ProtocolEncounter = {
  id: string;
  name: string;
  order: number;
  publishedDate?: string;
  publishedBy?: boolean;
  visitType?: {
    id: string;
    name: string;
  };
  protocols: Protocol[];
  description?: string | null;
  visitDay?: number;
  visitWindowStart?: number;
  visitWindowEnd?: number;
  visitTypeId: string;
  epochId?: string;
  studyArmId?: string;
  totalActivities: number;
};

export type ProtocolEpoch = Epoch & {
  protocolId: string;
};

export type ProtocolStudyArm = StudyArm & {
  protocolId: string;
};

export type Protocol = {
  id: string;
  isPublished?: boolean;
  name: string;
  consentRequired: boolean;
  ammendmentDate: string;
  version?: number;
  hasContent: boolean;
  protocolEncounters?: ProtocolEncounter[];
  status: (typeof PROTOCOL_STATUS)[number];
};

export type CreateProtocolPayload = Omit<Protocol, "id">;

export type AddProtocolContentPayload = {
  protocolId: string;
  content: string;
};

export type EditProtocolContentPayload = AddProtocolContentPayload;

export type AddProtocolEncounterPayload = {
  protocolId: string;
  visitTypeId?: string;
  description?: string;
  visitDay?: number;
  visitWindowStart?: number;
  visitWindowEnd?: number;
  epochId?: string;
  studyArmId?: string;
};

export type AddProtocolEpochPayload = {
  name: string;
  description?: string;
};

export type AddProtocolStudyArmPayload = {
  protocolId: string;
  name: string;
  description?: string;
};

export type ProtocolContent = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  protocolId: string;
  metadata: {
    content: string;
  };
  content: string;
  processingError: string | null;
};

export type UpdateProtocolEncountersPayload = {
  id?: string;
  protocolId?: string;
  name?: string;
  description?: string | null;
  isActive?: boolean;
  order?: number;
  visitTypeId?: string;
  visitDay?: number;
  visitWindowStart?: number;
  visitWindowEnd?: number;
  epochId?: string;
  studyArmId?: string;
};

export type UpdateProtocolPayload = {
  name?: string;
  consentRequired?: boolean;
  version?: number;
  ammendmentDate?: string;
};

export type UpdateProtocolEpochsPayload = {
  id: string;
  protocolId: string;
  name?: string;
  description?: string | null;
  isActive?: boolean;
};

export type UpdateProtocolStudyArmsPayload = {
  id: string;
  protocolId: string;
  name?: string;
  description?: string | null;
  isActive?: boolean;
};

export type ReorderProtocolEncountersPayload = {
  encounterId: string;
  encounterIds: string[];
};

export type ReorderProtocolEpochsPayload = {
  epochId: string;
  epochIds: string[];
};

export type ArchiveProtocolStudyArmPayload = {
  protocolId: string;
  studyArmId: string;
};

export type ArchiveProtocolEpochPayload = {
  protocolId: string;
  epochId: string;
};

export type ProtocolStudyArmsListResponse = ListBaseResponse<ProtocolStudyArm>;
export type ProtocolEncountersResponse = ListBaseResponse<ProtocolEncounter>;
export type ProtocolEpochsListResponse = ListBaseResponse<ProtocolEpoch>;
