import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteFields = (
  search?: string,
  status?: string,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["field-library", "fields", search, status],
    queryFn: ({ pageParam = 1 }) =>
      api.fieldLibrary.list({
        page: pageParam,
        take: initialPageSize,
        filter: {
          search: search || undefined,
          status: status || undefined,
        },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
