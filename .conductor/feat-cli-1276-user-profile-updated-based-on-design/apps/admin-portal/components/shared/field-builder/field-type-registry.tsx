import { Checkbox } from "flowbite-react";
import { z } from "zod";

import { FieldType } from "@/components/features/studies/study-detail/tabs/form-studio/fields/columns";
import { InputField, Select, Textarea } from "@/components/ui/form";
import { DateTimePicker } from "@/components/ui/form/date-picker/date-time-picker";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { Radio } from "@/components/ui/form/radio";
import { FieldConfig } from "@/lib/apis/field-library/types";

import type { NormalizedCodelistTerm } from "./utils";
import {
  createArraySchema,
  createDateSchema,
  createLayoutClass,
  createNumberSchema,
  createPlaceholder,
  createStringSchema,
} from "./utils";
import {
  ChoiceValidations,
  DateValidations,
  NumberValidations,
  TextValidations,
} from "./validation-fields";

type InternalConfig = FieldConfig & {
  dateFormat?: string;
  codelist?: { terms: NormalizedCodelistTerm[] };
};

export type FieldRenderProps = {
  name: string;
  label: string;
  type: FieldType;
  config?: InternalConfig;
};

type FieldTypeDefinition = {
  extractConfig: (config?: InternalConfig) => Record<string, any>;
  createSchema: (name: string, label: string, config?: any) => z.ZodObject<any>;
  renderPreview: (values: FieldRenderProps) => JSX.Element;
  renderValidation: (
    key: string | number,
    mode?: "global" | "study",
  ) => JSX.Element;
};

export const FIELD_TYPE_REGISTRY: Record<FieldType, FieldTypeDefinition> = {
  Text: {
    extractConfig: (config) => ({
      ...(typeof config?.maxLength === "number" && {
        maxLength: config.maxLength,
      }),
    }),

    createSchema: (name, label, config) =>
      z.object({
        [name]: createStringSchema(label, config),
      }),

    renderPreview: (values) => (
      <InputField
        name={values.name}
        placeholder={createPlaceholder(values.label)}
        maxLength={values.config?.maxLength}
      />
    ),

    renderValidation: (key) => <TextValidations key={key} />,
  },

  TextArea: {
    extractConfig: (config) => ({
      ...(typeof config?.maxLength === "number" && {
        maxLength: config.maxLength,
      }),
    }),

    createSchema: (name, label, config) =>
      z.object({
        [name]: createStringSchema(label, config),
      }),

    renderPreview: (values) => (
      <Textarea
        name={values.name}
        placeholder={createPlaceholder(values.label)}
        maxLength={values.config?.maxLength}
      />
    ),

    renderValidation: (key) => <TextValidations key={key} />,
  },

  Integer: {
    extractConfig: (config) => ({
      ...(typeof config?.min === "number" && { min: config.min }),
      ...(typeof config?.max === "number" && { max: config.max }),
      ...(config?.unitOfMeasure && { unitOfMeasure: config.unitOfMeasure }),
    }),

    createSchema: (name, label, config) =>
      z.object({
        [name]: createNumberSchema(label, config),
      }),

    renderPreview: (values) => (
      <InputNumber
        name={values.name}
        placeholder={createPlaceholder(values.label)}
        min={values.config?.min}
        max={values.config?.max}
        decimalScale={0}
        step={1}
      />
    ),

    renderValidation: (key) => <NumberValidations key={key} type="integer" />,
  },

  Decimal: {
    extractConfig: (config) => ({
      ...(typeof config?.min === "number" && { min: config.min }),
      ...(typeof config?.max === "number" && { max: config.max }),
      ...(config?.unitOfMeasure && { unitOfMeasure: config.unitOfMeasure }),
      ...(typeof config?.decimalPlaces === "number" && {
        decimalPlaces: config.decimalPlaces,
      }),
    }),

    createSchema: (name, label, config) =>
      z.object({
        [name]: createNumberSchema(label, config),
      }),

    renderPreview: (values) => (
      <InputNumber
        name={values.name}
        placeholder={createPlaceholder(values.label)}
        min={values.config?.min}
        max={values.config?.max}
        decimalScale={values.config?.decimalPlaces}
        step={0.1}
      />
    ),

    renderValidation: (key) => <NumberValidations key={key} type="decimal" />,
  },

  Date: {
    extractConfig: (config) => ({
      ...(config?.dateFormat && { dateFormat: config.dateFormat }),
      ...(config?.disablePastDates && {
        disablePastDates: config.disablePastDates,
      }),
      ...(config?.disableFutureDates && {
        disableFutureDates: config.disableFutureDates,
      }),
    }),

    createSchema: (name, label) =>
      z.object({
        [name]: createDateSchema(label),
      }),

    renderPreview: (values) => (
      <DateTimePicker
        name={values.name}
        placeholder={createPlaceholder(values.label, "Select")}
        format={
          values.config?.dateFormat as
            | "mm/dd/yyyy"
            | "dd/mm/yyyy"
            | "yyyy/mm/dd"
        }
        maxDate={values.config?.disableFutureDates ? new Date() : undefined}
        minDate={values.config?.disablePastDates ? new Date() : undefined}
        showTime={false}
      />
    ),

    renderValidation: (key) => <DateValidations key={key} type="Date" />,
  },

  DateTime: {
    extractConfig: (config) => ({
      ...(config?.dateFormat && { dateFormat: config.dateFormat }),
      ...(config?.disablePastDates && {
        disablePastDates: config.disablePastDates,
      }),
      ...(config?.disableFutureDates && {
        disableFutureDates: config.disableFutureDates,
      }),
    }),

    createSchema: (name, label) =>
      z.object({
        [name]: createDateSchema(label),
      }),

    renderPreview: (values) => (
      <DateTimePicker
        name={values.name}
        placeholder={createPlaceholder(values.label, "Select")}
        format={
          values.config?.dateFormat as
            | "mm/dd/yyyy"
            | "dd/mm/yyyy"
            | "yyyy/mm/dd"
        }
        maxDate={values.config?.disableFutureDates ? new Date() : undefined}
        minDate={values.config?.disablePastDates ? new Date() : undefined}
        showTime={true}
      />
    ),

    renderValidation: (key) => <DateValidations key={key} type="DateTime" />,
  },

  Dropdown: {
    extractConfig: (config) => ({
      ...(config?.codelistId && { codelistId: config.codelistId }),
    }),

    createSchema: (name, label) =>
      z.object({
        [name]: createStringSchema(label),
      }),

    renderPreview: (values) => (
      <Select
        name={values.name}
        options={values.config?.codelist?.terms?.map((term) => ({
          label: term.displayLabel,
          value: term.submissionValue,
        }))}
        placeholder={createPlaceholder(values.label, "Select")}
      />
    ),

    renderValidation: (key, mode = "study") => (
      <ChoiceValidations key={key} showLayoutField={false} mode={mode} />
    ),
  },

  RadioButton: {
    extractConfig: (config) => ({
      ...(config?.codelistId && { codelistId: config.codelistId }),
      ...(config?.layoutDirection && {
        layoutDirection: config.layoutDirection,
      }),
    }),

    createSchema: (name, label) =>
      z.object({
        [name]: createStringSchema(label),
      }),

    renderPreview: (values) => (
      <div className={createLayoutClass(values.config?.layoutDirection)}>
        {values.config?.codelist?.terms?.map((term) => (
          <div key={term.id} className="flex items-center gap-2">
            <Radio
              name={values.name}
              value={term.submissionValue}
              id={`${values.name}-${term.submissionValue}`}
            />
            <Label htmlFor={`${values.name}-${term.submissionValue}`}>
              {term.displayLabel}
            </Label>
          </div>
        ))}
      </div>
    ),

    renderValidation: (key, mode = "study") => (
      <ChoiceValidations key={key} mode={mode} />
    ),
  },

  Checkbox: {
    extractConfig: (config) => ({
      ...(config?.codelistId && { codelistId: config.codelistId }),
      ...(config?.layoutDirection && {
        layoutDirection: config.layoutDirection,
      }),
    }),

    createSchema: (name, label) =>
      z.object({
        [name]: createArraySchema(label),
      }),

    renderPreview: (values) => (
      <div
        className={`flex flex-wrap gap-4 ${
          values.config?.layoutDirection === "vertical"
            ? "flex-col"
            : "flex-row"
        }`}
      >
        {values.config?.codelist?.terms?.map((term) => (
          <div key={term.submissionValue} className="flex items-center gap-2">
            <Checkbox
              name={`${values.name}.${term.submissionValue}`}
              value={term.submissionValue}
              id={`${values.name}-${term.submissionValue}`}
            />
            <Label htmlFor={`${values.name}-${term.submissionValue}`}>
              {term.displayLabel}
            </Label>
          </div>
        ))}
      </div>
    ),

    renderValidation: (key, mode = "study") => (
      <ChoiceValidations key={key} mode={mode} />
    ),
  },
};

export const getConfigByFieldType = (
  type: FieldType,
  config?: InternalConfig,
) => {
  const fieldType = FIELD_TYPE_REGISTRY[type];
  if (!fieldType) return undefined;

  const typeSpecificConfig = fieldType.extractConfig(config) || {};
  const commonConfig = {
    ...(config?.isDisplayOnForm && {
      isDisplayOnForm: config.isDisplayOnForm,
    }),
  };

  return { ...typeSpecificConfig, ...commonConfig };
};

export const createValidationSchema = (values: FieldRenderProps) => {
  const { name, type, config, label } = values;
  if (!name) return z.object({});

  const fieldType = FIELD_TYPE_REGISTRY[type];
  if (!fieldType) {
    return z.object({
      [name]: createStringSchema(label),
    });
  }

  return fieldType.createSchema(name, label, config);
};

export const renderFieldPreview = (values: FieldRenderProps) => {
  const { name, label, type } = values;

  if (!name || !label || !type) {
    return (
      <div className="py-8 text-center text-gray-500">
        Complete field configuration to see preview
      </div>
    );
  }

  const fieldType = FIELD_TYPE_REGISTRY[type];
  if (!fieldType) {
    return FIELD_TYPE_REGISTRY.Text.renderPreview(values);
  }

  return fieldType.renderPreview(values);
};

export const renderFieldValidation = (
  type: FieldType,
  key: string | number,
  mode?: "global" | "study",
) => {
  const fieldType = FIELD_TYPE_REGISTRY[type];
  if (!fieldType) {
    return FIELD_TYPE_REGISTRY.Text.renderValidation(key, mode);
  }

  return fieldType.renderValidation(key, mode);
};
