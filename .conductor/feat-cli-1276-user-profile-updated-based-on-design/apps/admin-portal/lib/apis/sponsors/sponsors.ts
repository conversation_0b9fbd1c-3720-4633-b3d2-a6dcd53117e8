import type { MetadataParams } from "@/lib/apis/types";

import Base<PERSON>pi from "../base";
import type { Contact, ListContactsResponse } from "../contacts";
import type { SitesListResponse } from "../sites/types";
import type { StudySitesListResponse } from "../studies";
import type {
  AddSponsorContactPayload,
  AddSponsorPayload,
  EditSponsorPayload,
  Sponsor,
  SponsorGetUploadLogoPayload,
  SponsorListResponse,
  UpdateSponsorPayload,
} from "./types";

export class SponsorsApi extends BaseApi {
  public constructor() {
    super("/sponsors", true);
  }

  public async list(params: MetadataParams): Promise<SponsorListResponse> {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<SponsorListResponse>(`?${paramUrl}`);
  }

  public async get(id: string): Promise<Sponsor> {
    return this.http.get<Sponsor>(`/${id}`);
  }

  public async getUploadLogo(
    id: string,
    payload: SponsorGetUploadLogoPayload,
  ): Promise<{ url: string }> {
    return this.http.post<{ url: string }>(`/${id}/logo-upload-url`, payload);
  }
  public async update(
    id: string,
    data: UpdateSponsorPayload,
  ): Promise<Sponsor> {
    return this.http.put<Sponsor>(`/${id}`, data);
  }

  public async edit(id: string, data: EditSponsorPayload): Promise<Sponsor> {
    return this.http.put<Sponsor>(`/${id}`, data);
  }

  public async create(data: AddSponsorPayload): Promise<Sponsor> {
    return this.http.post<Sponsor>("/", data);
  }
  public async getLogoUrl(id: string) {
    return this.http.get<{ logoUrl: string }>(`/${id}/logo-url`);
  }

  // Sponsor Contacts
  public async addContact(
    id: string,
    data: AddSponsorContactPayload,
  ): Promise<Contact> {
    return this.http.post<Contact>(`/${id}/contacts`, data);
  }

  public async getContacts(id: string): Promise<ListContactsResponse> {
    return this.http.get<ListContactsResponse>(`/${id}/contacts`);
  }

  // Sponsor Studies
  public async getStudies(id: string): Promise<StudySitesListResponse> {
    return this.http.get<StudySitesListResponse>(`/${id}/studies`);
  }

  // Sponsor Sites
  public async getSites(id: string): Promise<SitesListResponse> {
    return this.http.get<SitesListResponse>(`/${id}/sites`);
  }
}
