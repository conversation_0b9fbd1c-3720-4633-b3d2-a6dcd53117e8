import React from "react";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";

import { QuestionFolder } from "./use-questions-queries";

const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  parentDirectoryId: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedFolder?: QuestionFolder | null;
  parentFolderId?: string | null;
};

export const FolderModal = ({
  isOpen,
  onClose,
  selectedFolder,
  parentFolderId,
}: Props) => {
  const isEditing = !!selectedFolder;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    // TODO: Implement folder creation/update when API is ready
    console.log(isEditing ? "Update folder" : "Create folder", data);
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? "Edit" : "New"} Folder`}
    >
      <Form
        defaultValues={{
          name: selectedFolder?.name || "",
          parentDirectoryId:
            parentFolderId || selectedFolder?.parentDirectoryId || "",
        }}
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="space-y-1">
          <Label htmlFor="name">Folder Name</Label>
          <InputField
            name="name"
            id="name"
            placeholder="Enter folder name..."
          />
        </div>

        <div className="mt-6 flex flex-col justify-end gap-4 sm:flex-row">
          <CloseButton onClose={onClose} />
          <Button type="submit" variant="primary">
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
