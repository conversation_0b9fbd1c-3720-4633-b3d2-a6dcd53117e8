import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { PatientStatusBadge } from "@/components/ui/badges/patient-status-badge";
import { Patient } from "@/lib/apis/studies";
import { formatDate } from "@/lib/utils";

export const patientColumns: ColumnDef<Patient>[] = [
  {
    header: "Id",
    accessorKey: "name",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Link
          className=" text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
          href={`/sites/${data.siteId}/studies/${data.studyId}/patients/${data.id}`}
        >
          {data.name}
        </Link>
      );
    },
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => {
      return <PatientStatusBadge variant={row.original.status} />;
    },
  },
  {
    header: "Study Code",
    accessorKey: "study.studyCode",
    id: "study.studyCode",
  },
  {
    header: "Protocol",
    accessorKey: "currentProtocol.name",
    id: "currentProtocol.name",
  },
  {
    header: "Enrollment Date",
    accessorKey: "enrollmentDate",
    cell: ({ row }) => {
      return row.original.enrollmentDate
        ? formatDate(row.original.enrollmentDate, "LLL dd, yyyy")
        : "N/A";
    },
  },
  {
    header: "Study Arm",
    accessorKey: "studyArm.name",
    id: "studyArm.name",
  },
];
