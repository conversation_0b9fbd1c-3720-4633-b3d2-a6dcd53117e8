import { Card } from "flowbite-react";
import { usePathname, useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import React, { useMemo, useState } from "react";
import { Hi<PERSON><PERSON><PERSON>, Hi<PERSON><PERSON>, HiX } from "react-icons/hi";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { PromptVariable } from "@/lib/apis/prompt-variables";

import { generatePromptVariableColumns } from "./columns";
import { FilterModal } from "./filter-variable-modal";
import { usePromptVariables } from "./hooks/use-prompt-variable-queries";
import { useFilterVariable } from "./hooks/useFilterVariable";
// import { useDeletePromptVariable } from "./hooks/use-protocol-prompt-mutations";
import PromptVariableModal from "./prompt-variable-modal";

const VariablePromptTab = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { key, label, type, computed, published, isActive, isCacheable } =
    useFilterVariable();
  const { isOpen, close, open } = useDisclosure();

  const {
    isOpen: isOpenFilterModal,
    close: onCloseFilterModal,
    open: onOpenFilterModal,
  } = useDisclosure();

  const [selectedVariable, setSelectedVariable] =
    useState<PromptVariable | null>(null);
  const { data, isPending, isPlaceholderData } = usePromptVariables();
  // const { mutate, isPending: isDeleting } = useDeletePromptVariable();

  const columns = useMemo(
    () =>
      generatePromptVariableColumns({
        onView: (data) => {
          setSelectedVariable(data);
          open();
        },
        // onDelete: (data) => {
        //   mutate(data?.id);
        // },
      }),
    [open, setSelectedVariable],
  );

  const appliedFilter = [
    { label: "Key", value: key, key: "key" },
    { label: "Label", value: label, key: "label" },
    { label: "Type", value: type, key: "type" },
    { label: "Active", value: isActive, key: "isActive" },
    { label: "Published", value: published, key: "published" },
    { label: "Cacheable", value: isCacheable, key: "isCacheable" },
    { label: "Computed", value: computed, key: "computed" },
  ];

  const handleRemoveAFilter = (key: string) => {
    const currentParams = new URLSearchParams(
      Array.from(searchParams.entries()),
    );
    currentParams.delete(key);
    currentParams.set("page", "1");

    const query = currentParams ? `?${currentParams.toString()}&page=1` : "";
    router.push(`${pathname}${query}`);
  };

  const handleCloseModal = () => {
    close();
    setSelectedVariable(null);
  };

  const renderTable = () => {
    if (isPending) return <TableLoading columns={columns} />;
    return (
      <>
        <LoadingWrapper isLoading={isPlaceholderData}>
          <TableData data={data?.results ?? []} columns={columns} />
        </LoadingWrapper>
        {data?.metadata && <TableDataPagination metadata={data.metadata} />}
      </>
    );
  };
  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="space-y-2 p-4">
          <div className="flex flex-col items-start justify-between gap-4  sm:flex-row sm:items-center">
            <Button
              onClick={onOpenFilterModal}
              variant="outline"
              className=" w-fit"
            >
              <HiFilter />
              <span className="relative">Filter</span>
            </Button>
          </div>
          <div className="flex max-w-full flex-wrap items-center gap-2 overflow-hidden">
            {appliedFilter.map((filter) => {
              if (!filter.value) return null;
              return (
                <div
                  key={filter.key}
                  className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                >
                  <span className="max-w-[160px] truncate">
                    {filter.label} :{" "}
                    {typeof filter.value === "boolean" ? "Yes" : filter.value}
                  </span>
                  <button
                    onClick={() => handleRemoveAFilter(filter.key)}
                    className="ml-1 flex-shrink-0 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    <HiX className="h-4 w-4" />
                  </button>
                </div>
              );
            })}
          </div>
        </div>
        {renderTable()}
      </Card>
      {isOpen && (
        <PromptVariableModal
          isOpen={isOpen}
          onClose={handleCloseModal}
          selectedPromptVariable={selectedVariable}
        />
      )}
      <FilterModal isOpen={isOpenFilterModal} onClose={onCloseFilterModal} />
    </>
  );
};

export default VariablePromptTab;
