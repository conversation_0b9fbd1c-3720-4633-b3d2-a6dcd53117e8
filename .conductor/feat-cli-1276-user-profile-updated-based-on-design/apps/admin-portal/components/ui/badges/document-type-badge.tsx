import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

import { PillBadge } from "./pill-badge";

export const documentTypes = [
  "txt",
  "csv",
  "xls",
  "xlsx",
  "pdf",
  "doc",
  "docx",
  "jpg",
  "jpeg",
  "png",
  "eml",
  "default",
] as const;

export type DocumentType = (typeof documentTypes)[number];

const documentTypeVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap before:hidden",
  {
    variants: {
      variant: {
        pdf: "bg-red-100 text-red-500 dark:bg-red-900 dark:text-red-300",
        doc: "bg-blue-100 text-blue-500 dark:bg-blue-900 dark:text-blue-300",
        docx: "bg-blue-100 text-blue-500 dark:bg-blue-900 dark:text-blue-300",
        jpg: "bg-teal-100 text-teal-600 dark:bg-teal-900 dark:text-teal-300",
        jpeg: "bg-teal-100 text-teal-600 dark:bg-teal-900 dark:text-teal-300",
        png: "bg-teal-100 text-teal-600 dark:bg-teal-900 dark:text-teal-300",
        xls: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300",
        xlsx: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300",
        txt: "bg-orange-100 text-orange-500 dark:bg-orange-900 dark:text-orange-300",
        csv: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300",
        eml: "bg-purple-100 text-purple-500 dark:bg-purple-900 dark:text-purple-300",
        default:
          "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

type Props = {
  className?: string;
  type: string;
};

export const DocumentTypeBadge = ({ type, className }: Props) => {
  const validType =
    documentTypes.find((t) => t === type.toLocaleLowerCase()) || "default";

  return (
    <PillBadge
      className={cn(
        documentTypeVariants({ variant: validType }),
        "rounded-2xl",
        className,
      )}
    >
      {validType.toUpperCase()}
    </PillBadge>
  );
};
