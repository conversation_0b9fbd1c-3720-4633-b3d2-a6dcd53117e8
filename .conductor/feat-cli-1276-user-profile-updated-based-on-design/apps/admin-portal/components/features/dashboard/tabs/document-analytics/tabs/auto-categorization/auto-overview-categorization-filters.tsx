"use client";

import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { useSiteStudies } from "@/components/features/sites/site-detail/tabs/hooks/use-site-studies";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, FormRef, Select } from "@/components/ui/form";
import { DateRangePicker } from "@/components/ui/form/date-picker/date-range-picker";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { useInfiniteSites } from "@/hooks/queries/use-infinite-sites";
import { cn } from "@/lib/utils";

import { useOverviewCategorizationFilters } from "./hooks/use-overview-categorization-filters";

const schema = z.object({
  siteId: z.string().optional().nullable(),
  studyId: z.string().optional().nullable(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
});

export const AutoOverviewCategorizationFilters = () => {
  const [open, setOpen] = useState(false);

  const {
    siteId,
    setSiteId,
    studyId,
    setStudyId,
    fromDate,
    setFromDate,
    toDate,
    setToDate,
  } = useOverviewCategorizationFilters();

  const [selectedSiteId, setSelectedSiteId] = useState(siteId || "");
  const { data: studies } = useSiteStudies(selectedSiteId);

  const formRef = useRef<FormRef<typeof schema>>(null);

  const handleApplyFilters = (values: z.infer<typeof schema>) => {
    setSiteId(values.siteId || null);
    setStudyId(values.studyId || null);
    // setStatus(values.status || null);
    // setMethod(values.method || null);
    setFromDate(values.dateRange?.from?.toISOString() || null);
    setToDate(values.dateRange?.to?.toISOString() || null);
    setOpen(false);
  };

  const handleClearFilters = () => {
    setSiteId(null);
    setStudyId(null);
    // setStatus(null);
    // setMethod(null);
    setFromDate(null);
    setToDate(null);
    formRef.current?.formHandler.reset();
    setSelectedSiteId("");
    setOpen(false);
  };

  const activeFilterCount = [siteId, studyId, fromDate || toDate].filter(
    Boolean,
  ).length;

  return (
    <Dropdown open={open} onOpenChange={setOpen}>
      <DropdownTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "relative flex items-center gap-2 px-3 py-2",
            "border-gray-300 bg-white text-gray-700 hover:border-gray-400 hover:bg-gray-50",
            "dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:border-gray-500 dark:hover:bg-gray-700",
          )}
        >
          <ListFilter className="h-4 w-4" />
          {activeFilterCount > 0 && (
            <span className="absolute right-0 top-0 flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </DropdownTrigger>

      <DropdownContent className="z-30 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <Form
          schema={schema}
          mode="onChange"
          onSubmit={handleApplyFilters}
          className="w-80 sm:w-96"
          ref={formRef}
          defaultValues={{
            siteId: siteId || "",
            studyId: studyId || "",
            dateRange: {
              from: fromDate ? new Date(fromDate) : undefined,
              to: toDate ? new Date(toDate) : undefined,
            },
          }}
        >
          <div className="flex flex-col divide-y">
            <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
              <Label
                htmlFor="siteId"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
              >
                Site
              </Label>
              <LazySelect
                name="siteId"
                useInfiniteQuery={useInfiniteSites}
                getOptionLabel={(site) => site.name}
                getOptionValue={(site) => site.id}
                placeholder="Select site"
                searchPlaceholder="Search sites..."
                onSelect={(value) => {
                  setSelectedSiteId(value?.id || "");
                }}
              />
            </div>

            <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
              <Label
                htmlFor="studyId"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
              >
                Study
              </Label>
              <Select
                name="studyId"
                options={studies?.results.map((study) => ({
                  label: study.study.name,
                  value: study.studyId,
                }))}
              />
            </div>

            <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
              <Label
                htmlFor="dateRange"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
              >
                Date Range
              </Label>
              <DateRangePicker
                name="dateRange"
                placeholder="Select date range"
                maxDate={new Date()}
              />
            </div>
          </div>

          <div className="flex justify-end gap-5 px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Button
              variant="outline"
              className={cn(
                "w-full justify-center",
                activeFilterCount === 0 && "pointer-events-none invisible",
              )}
              onClick={handleClearFilters}
              type="button"
            >
              Clear Filters
            </Button>
            <Button
              variant="primary"
              type="submit"
              className="w-full justify-center"
            >
              Apply Filters
            </Button>
          </div>
        </Form>
      </DropdownContent>
    </Dropdown>
  );
};
