import { useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { parseAsString } from "nuqs";
import { useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const codelistKeys = {
  all: () => ["codelist"] as const,
  allLists: () => [...codelistKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...codelistKeys.allLists(), params] as const,
  allDetails: () => [...codelistKeys.all(), "detail"] as const,
  detail: (id: string) => [...codelistKeys.allDetails(), id] as const,
};

export const useCodelists = () => {
  const { id: studyId } = useParams<{ id: string }>();

  const { page, take } = usePagination();
  const { orderBy, orderDirection } = useSort();
  const { search } = useSearch("codelistSearch");
  const [status] = useQueryState("status", parseAsString);

  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      search: search || undefined,
      status: status || undefined,
    },
  };

  return useQuery({
    queryKey: codelistKeys.list(params),
    queryFn: () => api.codeListDefinition.list(studyId, params),
    placeholderData: (prev) => prev,
  });
};

export const useCodelistItem = (id: string) => {
  return useQuery({
    queryKey: codelistKeys.detail(id),
    queryFn: () => api.codeListDefinition.get(id),
    enabled: !!id,
  });
};
