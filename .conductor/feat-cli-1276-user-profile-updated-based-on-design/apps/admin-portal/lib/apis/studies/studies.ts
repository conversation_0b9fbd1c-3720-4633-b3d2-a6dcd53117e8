import BaseApi from "../base";
import type { Contact, UpdateContactPayload } from "../contacts";
import type { Protocol } from "../protocols/types";
import { LocalStudyParams } from "../sites";
import type { MetadataParams } from "../types";
import type { VisitSchedule } from "../visit-schedules/types";
import type {
  AddStudyContactPayload,
  AddStudyProtocolPayload,
  AddVisitSchedulePayload,
  AssignableUserListResponse,
  CreateStudyPayload,
  CreateStudyTaskPayload,
  LocalStudyListResponse,
  PatientListResponse,
  Study,
  StudyContactsResponse,
  StudyListResponse,
  StudyProtocolsResponse,
  StudySitesResponse,
  UpdateStudyPayload,
} from "./types";

export class StudyApi extends BaseApi {
  constructor() {
    super("/studies", true);
  }

  public async list(params: MetadataParams): Promise<StudyListResponse> {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<StudyListResponse>(`?${paramUrl}`);
  }

  public async allStudies(params: MetadataParams) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<StudySitesResponse>(`?${paramUrl}`);
  }

  public async get(id: string): Promise<Study> {
    return this.http.get<Study>(`/${id}`);
  }

  public async update(id: string, data: UpdateStudyPayload): Promise<Study> {
    return this.http.put<Study>(`/${id}`, data);
  }

  public async create(data: CreateStudyPayload): Promise<Study> {
    return this.http.post<Study>("/", data);
  }

  public async getSites(id: string): Promise<StudySitesResponse> {
    return this.http.get<StudySitesResponse>(`/${id}/sites`);
  }

  public async getLocalStudies(params?: MetadataParams) {
    return this.http.get<LocalStudyListResponse>(`/local-studies`, params);
  }

  public async getContacts(id: string): Promise<StudyContactsResponse> {
    return this.http.get<StudyContactsResponse>(`/${id}/contacts`);
  }
  public async getPatients({
    payload,
    params,
  }: {
    payload: LocalStudyParams;
    params?: MetadataParams;
  }) {
    const paramUrl = params ? this.generateQueryParams(params) : params;

    return this.http.get<PatientListResponse>(
      `/${payload.studyId}/sites/${payload.siteId}/patients?${paramUrl}`,
    );
  }
  public async createTask(payload: CreateStudyTaskPayload) {
    return this.http.post(`/${payload.studyId}/tasks`, payload);
  }

  public async getAssignableUsers({
    params,
    studyId,
  }: {
    studyId: string;
    params?: MetadataParams;
  }) {
    const paramUrl = params ? this.generateQueryParams(params) : "";

    return this.http.get<AssignableUserListResponse>(
      `/${studyId}/assignable-profiles?${paramUrl}`,
    );
  }

  public async addContact(
    id: string,
    data: AddStudyContactPayload,
  ): Promise<Contact> {
    return this.http.post<Contact>(`/${id}/contacts`, data);
  }

  public async updateContact(
    id: string,
    payload: UpdateContactPayload,
  ): Promise<Contact> {
    return this.http.put<Contact>(`/${id}/contacts`, payload);
  }

  public async getProtocols(
    id: string,
    params?: MetadataParams,
  ): Promise<StudyProtocolsResponse> {
    return this.http.get<StudyProtocolsResponse>(`/${id}/protocols`, params);
  }

  public async addProtocol(
    id: string,
    data: AddStudyProtocolPayload,
  ): Promise<Protocol> {
    return this.http.post<Protocol>(`/${id}/protocols`, data);
  }

  public async addVisitSchedule(
    id: string,
    data: AddVisitSchedulePayload,
  ): Promise<VisitSchedule> {
    return this.http.post<VisitSchedule>(`/${id}/visit-schedules`, data);
  }
}
