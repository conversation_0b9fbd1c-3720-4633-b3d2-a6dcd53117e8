"use client";
import { Card } from "flowbite-react";
import React, { useMemo, useState } from "react";
import { HiPlus } from "react-icons/hi";

import { <PERSON>Field } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { TMFRefModel } from "@/lib/apis/tmf-ref-models";

import { generateTmfColumns } from "./columns";
import { useDeleteTMFRefModel } from "./hooks/use-tmf-ref-model-mutations";
import { useTMFRefModels } from "./hooks/use-tmf-ref-model-queries";
import { TMFModal } from "./tmf-modal";

export const TMFReferenceModelsTab = () => {
  const { isOpen, close, open } = useDisclosure();

  const [selectedTMFModel, setSelectedTMFModel] = useState<TMFRefModel | null>(
    null,
  );
  const { data, isPending, isPlaceholderData } = useTMFRefModels();
  const { mutate, isPending: isDeleting } = useDeleteTMFRefModel();

  const handleCloseModal = () => {
    close();
    setSelectedTMFModel(null);
  };

  const columns = useMemo(
    () =>
      generateTmfColumns({
        onView: (data) => {
          setSelectedTMFModel(data);
          open();
        },
      }),
    [open],
  );
  return (
    <>
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          TMF Reference Models
        </h2>
      </div>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-between gap-4 p-4">
          <div>
            <SearchField placeholder="Search..." />
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData || isDeleting}>
              <TableData data={data?.results ?? []} columns={columns} />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
      <TMFModal
        isOpen={isOpen}
        onClose={handleCloseModal}
        selectedTMFModel={selectedTMFModel}
      />
    </>
  );
};
