import BaseApi from "../base";
import type { MetadataParams } from "../types";
import type {
  CreateFieldTemplatePayload,
  FieldTemplateItem,
  FieldTemplateListResponse,
  UpdateFieldTemplatePayload,
} from "./types";

export class FieldTemplatesApi extends BaseApi {
  constructor() {
    super("/edc/field-templates", true);
  }

  public async list(
    params?: MetadataParams,
  ): Promise<FieldTemplateListResponse> {
    return this.http.get<FieldTemplateListResponse>("", params);
  }

  public async create(
    payload: CreateFieldTemplatePayload,
  ): Promise<FieldTemplateItem> {
    return this.http.post<FieldTemplateItem>("", payload);
  }

  public async update(
    payload: UpdateFieldTemplatePayload,
  ): Promise<FieldTemplateItem> {
    return this.http.put<FieldTemplateItem>(`/${payload.id}`, payload);
  }

  public async delete(id: string): Promise<void> {
    return this.http.delete<void>(`/${id}`);
  }
}

export const fieldTemplates = new FieldTemplatesApi();
