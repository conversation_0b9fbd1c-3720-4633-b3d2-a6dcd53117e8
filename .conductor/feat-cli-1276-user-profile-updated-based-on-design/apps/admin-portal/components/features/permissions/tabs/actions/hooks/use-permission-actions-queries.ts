import { useQuery } from "@tanstack/react-query";
import { parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const permissionActionKeys = {
  all: () => ["permission-actions"] as const,

  allList: () => [...permissionActionKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...permissionActionKeys.allList(), params] as const,
};

export const usePermissionActions = () => {
  const [subject] = useQueryState("subject", parseAsString);
  const [action] = useQueryState("action", parseAsString);
  const { page, take } = usePagination();
  const params = {
    page,
    take,
    filter: {
      action: action,
      subjectName: subject,
    },
  };
  return useQuery({
    queryKey: permissionActionKeys.list(params),
    queryFn: () => api.roles.getPermissions(params),
    placeholderData: (prev) => prev,
  });
};
