import { FileRecord } from "../../models/file-record";
import { ListBaseResponse } from "../types";
import { DOCUMENT_STATUSES } from "./../../../components/ui/badges/ebinder-document-badge";

export type EBinderMultipleUploadPayload = {
  studyId: string;
  siteId: string;
  categoryId?: string;
  files: {
    fileName: string;
    description?: string;
    contentType?: string;
    originationType?: string;
    extension?: string;
  }[];
};

export type EBinderMultipleUploadResponse = Record<
  string,
  {
    fileId: string;
    url: string;
  }
>;

export type EBinderCreatePlaceHolderPayload = {
  studyId: string;
  siteId: string;
  categoryId?: string;
  title: string;
  description?: string;
  isfFolderId: string;
};

export type EBinderUpdatePlaceholderPayload = {
  id: string;
  title?: string;
  description?: string;
  categoryId?: string;
  status?: string;
  processingStatus?: string;
  parentDirectoryId?: string;
  expiryDate?: string | null;
};

export type EBinderMoveDocumentPayload = {
  id: string;
  parentDirectoryId: string;
  isRollback?: boolean;
} & StatisticParams;

export type EBinderUpdateDocumentPayload = {
  id: string;
  title?: string;
  description?: string;
  categoryId?: string;
  status?: string;
  processingStatus?: string;
};

export type EBinderStatistics = {
  placeholder: number;
  draft: number;
  submitted: number;
  attention: number;
  reviewed: number;
  finalized: number;
  rejected: number;
  total: number;
};

export type StatisticParams = {
  studyId: string;
  siteId: string;
};

export type DocumentStatus = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: (typeof DOCUMENT_STATUSES)[number];
  description: null | string;
};
export type DocumentFolder = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  studyId: string;
  siteId: string;
  parentDirectoryId: null | string;
  isEditable: true;
};

export type Document = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  title: string;
  description: null;
  siteId: string;
  studyId: string;
  isActive: boolean;
  archived: boolean;
  categoryId: string;
  statusId: string;
  expiryDate: Date;
  assignedProfileId: string;
  assignedGroupId: string;
  summary: null;
  tags: null;
  version: number[];
  currentVersionId: string;
  processingStatus: string;
  currentVersion: CurrentVersion;
  category: Category;
  folderPath: string;
  isfFolderId: string | null;
  isfStatusId: string | null;
  tmfFolderId: string | null;
  tmfStatusId: string | null;
  tmfQCStepId: string | null;
  isfStatus?: DocumentStatus | null;
  tmfStatus?: DocumentStatus | null;
  isfFolder: DocumentFolder;
  tmfFolder: DocumentFolder;
};

export type Category = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  tmfRefModel: string;
  zoneNumber: string;
  sectionNumber: string;
  artifactNumber: string;
  tmfCore: string;
  zoneName: string;
  sectionName: string;
  artifactName: string;
  subartifact: string;
  isTMF: boolean;
  isISF: boolean;
  investigatorInitiatedStudyArtifacts: string;
  isActive: boolean;
  categoryVersionId: null;
};

export type CurrentVersion = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  fileRecordId: string;
  fileRecord: FileRecord;
  versionNumber: number[];
  fileId: string;
};

export type DocumentListResponse = ListBaseResponse<Document>;
