import { z } from "zod";

import { useInfiniteEligibleUsers } from "@/components/features/settings/groups/group-detail/hooks/use-eligible-users";
import { useGroup } from "@/components/features/settings/groups/hooks/use-group";
import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteRoles } from "@/hooks/queries/use-infinite-roles";
import { capitalize } from "@/utils/string";

import { useAddProfile } from "./hooks/use-users-mutations";

const addProfileSchema = z.object({
  roleId: z
    .string({ required_error: "Please select a role" })
    .min(1, "Please select a role"),
  userId: z
    .string({ required_error: "Please select a user" })
    .min(1, "Please select a user"),
});

type ModalAddProfileProps = {
  groupId: string;
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddProfile = ({
  groupId,
  isOpen,
  onClose,
}: ModalAddProfileProps) => {
  const { data: group } = useGroup(groupId);

  const { mutateAsync: addProfile, isPending } = useAddProfile();

  const handleSubmit = async (values: z.infer<typeof addProfileSchema>) => {
    await addProfile({
      groupId,
      userId: values.userId,
      roleId: values.roleId,
    });
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Profile</Modal.Header>
      <Modal.Body>
        <Form
          schema={addProfileSchema}
          onSubmit={handleSubmit}
          className="space-y-4"
        >
          <div className="flex flex-col gap-2">
            <Label htmlFor="roleId">Role</Label>
            <LazySelect
              name="roleId"
              id="roleId"
              searchPlaceholder="Search role..."
              useInfiniteQuery={useInfiniteRoles}
              getOptionLabel={(role) =>
                `${role.name} (${capitalize(role.type)})`
              }
              getOptionValue={(role) => role.id}
              params={[group?.type]}
              placeholder="Select Role"
            />
          </div>
          <div className="col-span-2 flex flex-col gap-2">
            <Label htmlFor="userId">User</Label>
            <LazySelect
              name="userId"
              id="userId"
              searchPlaceholder="Search user..."
              useInfiniteQuery={useInfiniteEligibleUsers}
              getOptionLabel={(user) => `${user.firstName} ${user.lastName}`}
              getOptionValue={(user) => user.id}
              params={[groupId]}
              placeholder="Select user"
            />
          </div>

          <div className="flex flex-col justify-end gap-4 sm:flex-row">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              disabled={isPending}
              variant="primary"
              enabledForDirty
              disabledForInvalid
              isLoading={isPending}
            >
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
