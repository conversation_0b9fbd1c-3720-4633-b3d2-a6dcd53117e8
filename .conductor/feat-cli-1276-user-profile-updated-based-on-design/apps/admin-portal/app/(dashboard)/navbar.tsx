"use client";

import { useA<PERSON>, use<PERSON><PERSON><PERSON>, useUser } from "@clerk/nextjs";
import { useQueryClient } from "@tanstack/react-query";
import { DarkThemeToggle, Navbar, Tooltip } from "flowbite-react";
import { User } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  HiChevronDown,
  HiLogout,
  HiMenuAlt1,
  Hi<PERSON><PERSON>,
  Hi<PERSON>,
} from "react-icons/hi";

import {
  Dropdown,
  DropdownContent,
  DropdownItem,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { useSidebarContext } from "@/contexts/sidebar-context";
import { useAuthenticated } from "@/hooks/auth";
import { useMediaQuery } from "@/hooks/use-media-query";
import { clearAllStores } from "@/utils/clear-stores";

export function DashboardNavbar() {
  const sidebar = useSidebarContext();
  const isDesktop = useMediaQuery("(min-width: 1024px)");

  function handleToggleSidebar() {
    if (isDesktop) {
      return;
    }
    sidebar.mobile.toggle();
  }
  return (
    <Navbar
      fluid
      className="fixed top-0 z-[35] w-full border-b border-gray-700 bg-[#212a36] p-0 text-white sm:p-0 dark:bg-gray-800"
    >
      <div className="w-full p-3 pr-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <button
              onClick={handleToggleSidebar}
              className="mr-3 cursor-pointer rounded p-2 text-gray-600 hover:bg-gray-700 hover:text-gray-300 lg:hidden"
            >
              <span className="sr-only">Toggle sidebar</span>
              <div>
                {sidebar.mobile.isOpen ? (
                  <HiX className="h-6 w-6" />
                ) : (
                  <HiMenuAlt1 className="h-6 w-6" />
                )}
              </div>
            </button>
            <Navbar.Brand as={Link} href="/dashboards" className="mr-14">
              <Image
                className="ml-2 mr-3 h-8"
                alt=""
                src="/images/logo.svg"
                width={152}
                height={27}
              />
            </Navbar.Brand>
          </div>
          <div className="hidden lg:block"></div>
          <div className="flex items-center lg:gap-3">
            <div className="flex items-center">
              <div className="hidden dark:block">
                <Tooltip content="Toggle light mode">
                  <DarkThemeToggle className="hover:bg-gray-700" />
                </Tooltip>
              </div>
              <div className="dark:hidden">
                <Tooltip content="Toggle dark mode">
                  <DarkThemeToggle className="hover:bg-gray-700" />
                </Tooltip>
              </div>
              <div className="ml-3 flex items-center">
                <UserProfileDropdown />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Navbar>
  );
}

export function UserProfileDropdown() {
  const { signOut } = useAuth();
  const { openUserProfile } = useClerk();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { data } = useAuthenticated();
  const { user } = useUser();
  const userData = data?.data;

  const handleSignOut = async () => {
    await signOut();
    queryClient.clear();
    clearAllStores();
    router.push("/authentication/sign-in");
  };

  const userName = userData
    ? `${userData.firstName} ${userData.lastName}`
    : "User";
  const initials = userData
    ? `${userData.firstName?.[0] || ""}${userData.lastName?.[0] || ""}`.toUpperCase()
    : "U";

  return (
    <Dropdown placement="bottom-end">
      <DropdownTrigger>
        <button className="flex items-center space-x-2 rounded-xl px-3 py-2  text-gray-200 transition-all duration-200 hover:bg-gray-700">
          {user ? (
            <Image
              src={user.imageUrl}
              alt="User Avatar"
              height={32}
              width={32}
              className="rounded-xl"
            />
          ) : (
            <div className="flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 text-sm font-medium text-white shadow-sm">
              {initials}
            </div>
          )}
          <HiChevronDown className="h-4 w-4 transition-transform duration-200" />
        </button>
      </DropdownTrigger>

      <DropdownContent className="border border-gray-200 bg-white p-0 shadow-lg dark:border-gray-700 dark:bg-gray-800">
        <div className="w-80 p-4">
          <div className="flex items-center gap-4">
            {user ? (
              <Image
                src={user.imageUrl}
                alt="User Avatar"
                width={48}
                height={48}
                className="rounded-xl"
              />
            ) : (
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 text-base font-medium text-white shadow-sm">
                {initials}
              </div>
            )}
            <div className="min-w-0 flex-1">
              <p className="truncate text-sm font-semibold text-gray-900 dark:text-gray-100">
                {userName}
              </p>
              <p className="truncate text-xs text-gray-500 dark:text-gray-400">
                {userData?.email || "<EMAIL>"}
              </p>
            </div>
          </div>

          <div className="mt-4 border-t border-gray-200 pt-4 dark:border-gray-700">
            <DropdownItem
              onClick={() => openUserProfile()}
              className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <User className=" h-4 w-4" />
              Profile
            </DropdownItem>
            <DropdownItem
              onClick={handleSignOut}
              className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
            >
              <HiLogout className=" h-4 w-4" />
              Sign out
            </DropdownItem>
          </div>
        </div>
      </DropdownContent>
    </Dropdown>
  );
}
