#!/bin/bash

set -euo pipefail

MODULE=$1
ENVIRONMENT_NAME=$2
REGION_NAME=$3

function print_slack_summary_build() {
    local slack_msg_header
		if [[ "$REGION_NAME" == "NONE" ]]; then
			slack_msg_header=":loudspeaker: :loudspeaker: :loudspeaker: *${MODULE} - ${ENVIRONMENT_NAME}*"
		else
			slack_msg_header=":loudspeaker: :loudspeaker: :loudspeaker: *${MODULE} - ${ENVIRONMENT_NAME} - ${REGION_NAME}*"
		fi
cat <<-SLACK
{
	"blocks": [
		{
			"type": "section",
			"text": {
				"type": "mrkdwn",
				"text": "${slack_msg_header}"
			}
		},
		{
			"type": "divider"
		},
		{
			"type": "section",
			"fields": [
				{
					"type": "mrkdwn",
					"text": "*Environment:*\n\n${ENVIRONMENT_NAME}"
				},
				{
					"type": "mrkdwn",
					"text": "*Branch/Tag:*\n\n${GITHUB_REF_NAME}"
				}
			]
		},
		{
			"type": "section",
			"fields": [
				{
					"type": "mrkdwn",
					"text": " "
				}
			]
		},
		{
			"type": "section",
			"fields": [
				{
					"type": "mrkdwn",
					"text": "*Trigged By:*\n\n${GITHUB_TRIGGERING_ACTOR}"
				},
				{
					"type": "mrkdwn",
					"text": "*Commit:*\n\n${GITHUB_SHA}"
				}
			]
		},
		{
			"type": "section",
			"fields": [
				{
					"type": "mrkdwn",
					"text": " "
				}
			]
		},
		{
			"type": "section",
			"fields": [
				{
					"type": "mrkdwn",
					"text": "*Job:*\n\n${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}"
				},
				{
					"type": "mrkdwn",
					"text": "*Message:*\n\n${COMMIT_MESSAGE}"
				}
			]
		},
		{
			"type": "divider"
		}
	]
}
SLACK
}

function share_slack_update_build() {
    local slack_webhook
    slack_webhook="$SLACK_WEBHOOK_URL"
    curl -X POST                                           \
            --data-urlencode "payload=$(print_slack_summary_build)"  \
            "${slack_webhook}"
}

share_slack_update_build
