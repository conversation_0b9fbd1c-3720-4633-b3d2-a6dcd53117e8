import { ListFilter } from "lucide-react";
import { useQueryState } from "nuqs";
import { parseAsString } from "nuqs/server";
import { useRef, useState } from "react";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, FormRef, Select } from "@/components/ui/form";
import { DateRangePicker } from "@/components/ui/form/date-picker/date-range-picker";
import { Label } from "@/components/ui/form/label";
import { cn } from "@/lib/utils";

// List of document types
const DOC_TYPES = [
  { label: "PDF", value: "pdf" },
  { label: "DOC", value: "doc" },
  { label: "DOCX", value: "docx" },
  { label: "XLS", value: "xls" },
  { label: "XLSX", value: "xlsx" },
  { label: "CSV", value: "csv" },
  { label: "TXT", value: "txt" },
  { label: "JPG", value: "jpg" },
  { label: "JPEG", value: "jpeg" },
  { label: "PNG", value: "png" },
  { label: "EML", value: "eml" },
];

const schema = z.object({
  documentType: z.string().optional(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
});

export const FilterCTMRDocuments = () => {
  const [open, setOpen] = useState(false);

  return (
    <Dropdown open={open} onOpenChange={setOpen} placement="bottom">
      <DropdownTrigger>
        <Button variant="outline" className="sm:px-4 sm:py-1.5">
          <ListFilter className="size-5" />
        </Button>
      </DropdownTrigger>
      <DropdownContent className="z-10 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <FilterContent setOpen={setOpen} />
      </DropdownContent>
    </Dropdown>
  );
};

const FilterContent = ({ setOpen }: { setOpen: (open: boolean) => void }) => {
  const ref = useRef<FormRef<typeof schema>>(null);

  const [documentType, setDocumentType] = useQueryState(
    "extension",
    parseAsString.withDefault(""),
  );

  const [createdFrom, setCreatedFrom] = useQueryState(
    "fromCreatedDate",
    parseAsString.withDefault(""),
  );

  const [createdTo, setCreatedTo] = useQueryState(
    "toCreatedDate",
    parseAsString.withDefault(""),
  );

  function onSubmit(data: z.infer<typeof schema>) {
    setDocumentType(data.documentType || null);
    setCreatedFrom(
      data.dateRange?.from ? data.dateRange.from.toISOString() : null,
    );
    setCreatedTo(data.dateRange?.to ? data.dateRange.to.toISOString() : null);
    setOpen(false);
  }

  function onClear() {
    setDocumentType(null);
    setCreatedFrom(null);
    setCreatedTo(null);
    ref.current?.formHandler?.reset();
  }

  const dateRange =
    createdFrom || createdTo
      ? {
          from: createdFrom ? new Date(createdFrom) : undefined,
          to: createdTo ? new Date(createdTo) : undefined,
        }
      : undefined;

  const defaultValues = {
    documentType: documentType ?? "",
    dateRange,
  };

  const hasFilters = !!documentType || !!createdFrom || !!createdTo;

  return (
    <div className="">
      <Form
        schema={schema}
        mode="onChange"
        onSubmit={onSubmit}
        className="min-w-96"
        ref={ref}
        defaultValues={defaultValues}
        key={Object.values(defaultValues).join("-")}
      >
        <div className="flex flex-col divide-y">
          {/* Document Type */}
          <div className="px-[15px] pb-5 pt-2.5">
            <Label
              htmlFor="documentType"
              className="mb-2 block text-base font-normal leading-6 text-gray-700"
            >
              Document Type
            </Label>
            <Select
              id="documentType"
              name="documentType"
              className="h-fit w-full"
              options={DOC_TYPES}
              placeholder="Select Document Type"
            />
          </div>

          {/* Date Range */}
          <div className="px-[15px] pb-5 pt-2.5">
            <Label
              htmlFor="dateRange"
              className="mb-2 block text-base font-normal leading-6 text-gray-700"
            >
              Created Date
            </Label>
            <DateRangePicker name="dateRange" placeholder="Select Date" />
          </div>
        </div>

        <div className="flex justify-end gap-5 px-[15px] pb-5 pt-2.5">
          <Button
            variant="outline"
            className={cn(
              "w-full justify-center",
              !hasFilters && "pointer-events-none invisible",
            )}
            onClick={() => {
              onClear();
            }}
            type="button"
          >
            Clear Filters
          </Button>
          <Button
            variant="primary"
            type="submit"
            className="w-full justify-center"
          >
            Apply Filters
          </Button>
        </div>
      </Form>
    </div>
  );
};
