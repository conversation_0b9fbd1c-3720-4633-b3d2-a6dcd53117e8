import type { ListBaseResponse } from "../types";

export type ScannerApplication = {
  id: string;
  versionNumber: string;
  apkLink: string;
  releaseNotes: string | null;
};

export type ScannerApplicationsListResponse =
  ListBaseResponse<ScannerApplication>;

export type AddScannerApplicationPayload = {
  versionNumber: string;
  apkLink?: string;
  releaseNotes?: string;
};

export type ScannerApplicationGetUploadApkPayload = {
  fileType: string;
  extension: string;
};

export type UpdateScannerApplicationPayload =
  Partial<AddScannerApplicationPayload>;
