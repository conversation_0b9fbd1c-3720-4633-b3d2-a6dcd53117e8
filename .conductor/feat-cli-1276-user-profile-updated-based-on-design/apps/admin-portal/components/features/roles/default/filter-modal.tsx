import {
  parseAsBoolean,
  parseAsInteger,
  parseAsString,
  useQueryState,
} from "nuqs";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import { Checkbox, Form, InputField, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { ROLE_TYPES } from "@/lib/constants";

const filterSchema = z.object({
  name: z.string().optional(),
  type: z.string().optional(),
  isActive: z.boolean().default(false),
});

type FilterFormValues = z.infer<typeof filterSchema>;

type ModalFilterProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalFilter = ({ isOpen, onClose }: ModalFilterProps) => {
  const [name, setName] = useQueryState("name", parseAsString);
  const [type, setType] = useQueryState("type", parseAsString);
  const [, setPage] = useQueryState("page", parseAsInteger);

  const [isActive, setIsActive] = useQueryState("active", parseAsBoolean);

  const hasFilter = !!name || !!type || !!isActive;

  const handleSubmit = (data: FilterFormValues) => {
    setName(data.name || null);
    setType(data.type || null);
    setIsActive(data.isActive || null);
    setPage(1);
    onClose();
  };

  const handleClearFilters = () => {
    setName(null);
    setType(null);
    setIsActive(null);
    setPage(1);
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Filter roles</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={filterSchema}
          onSubmit={handleSubmit}
          defaultValues={{
            type: type || "",
            name: name || "",
            isActive: isActive || false,
          }}
        >
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Role Name</Label>
              <InputField
                id="name"
                name="name"
                placeholder="Search by name..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Role Type</Label>
              <Select
                id="type"
                name="type"
                placeholder="Select a type..."
                options={ROLE_TYPES.map((type) => ({
                  label: type.toUpperCase(),
                  value: type,
                }))}
              />
            </div>

            <div className="flex flex-wrap items-center gap-x-8 gap-y-2 sm:col-span-2">
              <div className="flex items-center gap-2">
                <Checkbox id="isActive" name="isActive" />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-8"></div>

          <div className="flex flex-col justify-end gap-5 border-none pt-4 sm:flex-row">
            {hasFilter && (
              <Button
                onClick={handleClearFilters}
                type="button"
                variant="outline"
              >
                Clear Filters
              </Button>
            )}
            <Button type="submit" variant="primary">
              Apply Filters
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
