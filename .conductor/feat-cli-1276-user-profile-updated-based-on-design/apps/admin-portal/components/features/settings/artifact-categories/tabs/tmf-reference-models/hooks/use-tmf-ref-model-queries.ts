import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const tmfRefModelKeys = {
  all: () => ["tmf-models"] as const,

  allLists: () => [...tmfRefModelKeys.all(), "lists"] as const,
  list: (params: MetadataParams) =>
    [...tmfRefModelKeys.allLists(), params] as const,
};

export const useTMFRefModels = () => {
  const { search } = useSearch();
  const { take, page } = usePagination();

  const params = {
    page,
    take,
    filter: {
      tmfRefModel: search,
    },
  };

  return useQuery({
    queryKey: tmfRefModelKeys.list(params),
    queryFn: () => api.tmfRefModelApi.list(params),
    placeholderData: (prev) => prev,
  });
};
