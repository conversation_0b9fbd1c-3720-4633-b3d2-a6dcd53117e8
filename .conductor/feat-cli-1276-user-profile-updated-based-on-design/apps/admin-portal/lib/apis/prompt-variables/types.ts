import { ROLE_MESSAGE_TYPES } from "@/components/features/settings/app-prompts/tabs/templates/message-modal";
import { PROMPT_TYPES } from "@/components/features/settings/app-prompts/tabs/templates/prompt-modal";
import { VARIABLE_TYPES } from "@/components/features/settings/app-prompts/tabs/variables/prompt-variable-modal";

import type { ListBaseResponse } from "../types";
type Message = {
  text: string;
};

type ChatHistory = {
  role: (typeof ROLE_MESSAGE_TYPES)[number];
  parts: Message[];
};
export type AddPromptPayload = {
  name: string;
  description?: string;
  type: (typeof PROMPT_TYPES)[number]["value"];
  chatHistory: ChatHistory[];
  generationConfig?: {
    topK?: number;
    topP?: number;
    temperature?: number;
  };
};

export type UpdatePromptPayload = AddPromptPayload & {
  id: string;
};

export type Prompt = AddPromptPayload & {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  isActive: boolean;
  version: number;
};

export type PromptListResponse = ListBaseResponse<Prompt>;

//==============

export type ResolverListResponse = {
  results: string[];
};

export type VariableType = (typeof VARIABLE_TYPES)[number];

export type PromptVariable = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  key: string;
  label: string;
  description: string | null;
  type: VariableType;
  computed: boolean;
  fallbackValue: any;
  published: boolean;
  isActive: boolean;
  resolverFunction: string | null;
  // isCacheable: boolean;
};

export type AddPromptVariablePayload = {
  key: string;
  label?: string;
  description?: string;
  type: VariableType;
  computed: boolean;
  fallbackValue?: any;
  published: boolean;
  isActive: boolean;
  resolverFunction?: string;
  // isCacheable: boolean;
};

export type UpdatePromptVariablePayload = AddPromptVariablePayload & {
  id: string;
};

export type PromptVariableListResponse = ListBaseResponse<PromptVariable>;
