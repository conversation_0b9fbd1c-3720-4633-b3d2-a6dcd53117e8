import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { PublishVisitSchedulePayload } from "@/lib/apis/visit-schedules";

import { USE_VISIT_SCHEDULE_QUERY_KEY } from "./use-visit-schedule";

export const usePublishVisitSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: PublishVisitSchedulePayload & { id: string }) => {
      const { id, published } = payload;
      return api.visitSchedules.publish(id, { published });
    },
    onSuccess: (_, variables) => {
      toast.success("Visit schedule published");
      queryClient.invalidateQueries({
        queryKey: [USE_VISIT_SCHEDULE_QUERY_KEY, variables.id],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
