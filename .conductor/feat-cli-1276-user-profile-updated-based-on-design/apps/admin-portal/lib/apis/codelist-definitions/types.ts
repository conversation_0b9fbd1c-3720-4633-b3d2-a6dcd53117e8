import type { ListBaseResponse } from "@/lib/apis/types";

export type CodelistStatus = "DRAFT" | "PUBLISHED" | "ARCHIVED";

export type CodelistTermPayload = {
  submissionValue: string;
  displayLabel: string;
  definition?: string;
  displayOrder: number;
};
export type CodelistTerm = CodelistTermPayload & {
  codelistDefinitionId: string;
  createdDate: string;
  id: string;
  lastUpdatedDate: string;
  synonyms: any;
  sourceCode: null | string;
};

export type Codelist = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  label: string;
  codelistId?: string;
  description?: string;
  status: CodelistStatus;
  version: number;
  terms: CodelistTerm[];
  sourceCode: null | string;
  sourceOntology: null | string;
  sourceVersion: null | string;
  isExtensible: boolean;
  studyId: string;
};

export type CodelistListResponse = ListBaseResponse<Codelist>;

export type CreateCodelistPayload = {
  name: string;
  label: string;
  studyId: string;
  description?: string;
  terms: CodelistTermPayload[];
};

export type UpdateCodelistPayload = CreateCodelistPayload & {
  id: string;
};
