"use client";
import {
  autoUpdate,
  flip,
  FloatingFocus<PERSON>anager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import { Popover } from "flowbite-react";
import { AlertCircle, ChevronDown } from "lucide-react";
import { useMemo, useState } from "react";
import { get, useController, useFormContext } from "react-hook-form";
import { HiSearch } from "react-icons/hi";

import { useDebounce } from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

export type Props = {
  name: string;
  placeholderText?: string;
  className?: string;
  options?: Readonly<
    { label: React.ReactNode; value: string; disabled?: boolean }[]
  >;
  searchable?: boolean;
  searchPlaceholder?: string;
};

export const SelectCell = ({
  name,
  className,
  placeholderText = "Select an option",
  options = [],
  searchable = false,
  searchPlaceholder = "Search...",
}: Props) => {
  const { control } = useFormContext();
  const [isEditing, setIsEditing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const {
    field,
    formState: { errors },
  } = useController({
    name,
    control,
  });
  const errorObj = get(errors, name);
  const errorMessage = errorObj?.message?.valueOf();
  const hasError = typeof errorMessage === "string";

  const { refs, floatingStyles, context } = useFloating({
    open: isEditing,
    onOpenChange: (open) => {
      setIsEditing(open);
      setSearchTerm("");
      if (!open) {
        field.onBlur();
      }
    },
    placement: "bottom-start",
    middleware: [
      offset(8),
      flip({
        fallbackPlacements: ["top-start"],
        fallbackStrategy: "bestFit",
        padding: 1,
        crossAxis: false,
      }),
      shift({
        padding: 1,
      }),
      size({
        apply({ rects, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
          });
        },
        padding: 1,
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context, { enabled: !field.disabled }),
    useDismiss(context),
  ]);

  // Filter options based on search term
  const filteredOptions = useMemo(() => {
    if (!searchable || !debouncedSearchTerm) {
      return options;
    }
    return options.filter((option) =>
      String(option.label)
        .toLowerCase()
        .includes(debouncedSearchTerm.toLowerCase()),
    );
  }, [options, debouncedSearchTerm, searchable]);

  const handleCellClick = () => {
    if (!field.disabled) {
      setIsEditing(true);
    }
  };

  const selectedOption = options.find((option) => option.value === field.value);

  const displayValue = selectedOption ? selectedOption.label : placeholderText;

  return (
    <div
      className={cn(
        "group/select relative flex w-full items-center gap-2 border border-gray-300 bg-white p-2 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600",
        isEditing && "border-blue-400 shadow-sm dark:border-blue-500",
        hasError && "border-red-500",
        field.disabled && "cursor-not-allowed opacity-50",
        className,
      )}
      onClick={handleCellClick}
    >
      <div
        ref={refs.setReference}
        {...getReferenceProps()}
        className="flex w-full items-center justify-between"
      >
        <div
          className={cn(
            "truncate text-gray-900 dark:text-white",
            !selectedOption && "text-gray-500 dark:text-gray-400",
          )}
        >
          {displayValue}
        </div>

        <ChevronDown
          className={cn(
            "size-3 flex-shrink-0 text-gray-400 opacity-0 transition-all group-hover/select:opacity-100 dark:text-gray-500",
            isEditing && "rotate-180 opacity-100",
            field.disabled && "hidden",
          )}
        />
      </div>

      {/* Dropdown Panel */}
      {isEditing && (
        <FloatingPortal>
          <FloatingFocusManager context={context} modal={false}>
            <div
              ref={refs.setFloating}
              style={floatingStyles}
              {...getFloatingProps()}
              className="z-50 rounded-lg border border-gray-200 bg-white shadow-lg dark:border-gray-600 dark:bg-gray-700"
            >
              {/* Search Input */}
              {searchable && (
                <div className="border-b border-gray-200 p-2 dark:border-gray-600">
                  <div className="relative">
                    <HiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder={searchPlaceholder}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="focus:border-primary-500 focus:ring-primary-500 w-full rounded-md border border-gray-300 bg-white py-2 pl-9 pr-3 text-sm placeholder-gray-500 focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400"
                      autoFocus
                    />
                  </div>
                </div>
              )}

              <div className="select-options max-h-60 overflow-y-auto py-1">
                {filteredOptions.length === 0 ? (
                  <div className="flex h-10 items-center justify-center px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                    {searchable && searchTerm
                      ? "No results found"
                      : "No options available"}
                  </div>
                ) : (
                  filteredOptions.map((option) => {
                    const isSelected = field.value === option.value;
                    return (
                      <button
                        key={option.value}
                        type="button"
                        disabled={option.disabled}
                        className={cn(
                          "w-full px-3 py-2 text-left text-sm",
                          "hover:bg-gray-100 disabled:cursor-not-allowed disabled:text-red-400 dark:hover:bg-gray-600 dark:disabled:text-red-500",
                          isSelected
                            ? "bg-primary-50 text-primary-600 dark:bg-primary-600/20 dark:text-primary-400"
                            : "text-gray-900 dark:text-gray-200",
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          field.onChange(option.value);
                          field.onBlur();
                          setSearchTerm("");
                          setIsEditing(false);
                        }}
                      >
                        <span className="block truncate">{option.label}</span>
                      </button>
                    );
                  })
                )}
              </div>
            </div>
          </FloatingFocusManager>
        </FloatingPortal>
      )}

      {hasError && (
        <Popover
          placement="top"
          trigger="hover"
          content={
            <p className="px-2 py-1 text-red-600 dark:text-red-400">
              {errorMessage}
            </p>
          }
        >
          <AlertCircle className="ml-auto h-4 w-4 cursor-help text-red-500" />
        </Popover>
      )}
    </div>
  );
};
