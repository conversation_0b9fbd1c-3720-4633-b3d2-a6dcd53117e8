import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  CreateFieldTemplatePayload,
  UpdateFieldTemplatePayload,
} from "@/lib/apis/field-templates/types";

import { fieldTemplateKeys } from "./use-field-template-queries";

export const useCreateFieldTemplate = () =>
  useMutation({
    mutationFn: (payload: CreateFieldTemplatePayload) =>
      api.fieldTemplates.create(payload),
    onError: (err) =>
      toast.error(err?.message || "Failed to create field template"),
    onSettled: (_, err) =>
      !err && toast.success("Field template created successfully"),
    meta: {
      awaits: fieldTemplateKeys.allLists(),
    },
  });

export const useUpdateFieldTemplate = () =>
  useMutation({
    mutationFn: (payload: UpdateFieldTemplatePayload) =>
      api.fieldTemplates.update(payload),
    onError: (err) =>
      toast.error(err?.message || "Failed to update field template"),
    onSettled: (_, err) =>
      !err && toast.success("Field template updated successfully"),
    meta: {
      awaits: fieldTemplateKeys.allLists(),
    },
  });

export const useDeleteFieldTemplate = () =>
  useMutation({
    mutationFn: (id: string) => api.fieldTemplates.delete(id),
    onError: (err) =>
      toast.error(err?.message || "Failed to delete field template"),
    onSettled: (_, err) =>
      !err && toast.success("Field template deleted successfully"),
    meta: {
      awaits: fieldTemplateKeys.allLists(),
    },
  });
